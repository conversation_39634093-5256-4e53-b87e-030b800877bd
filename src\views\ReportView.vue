<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  Card,
  Row,
  Col,
  Tabs,
  DatePicker,
  Table,
  Button,
  Select,
  Modal,
  message,
  PageHeader,
} from 'ant-design-vue'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import { DownloadOutlined } from '@ant-design/icons-vue'
import { use } from 'echarts/core'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import VChart from 'vue-echarts'
import type { AlignType } from 'ant-design-vue/es/vc-table/interface'
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'

// Register ECharts components
use([
  CanvasRenderer,
  Bar<PERSON>hart,
  LineChart,
  Pie<PERSON>hart,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
])

const activeTab = ref('monthly')
const selectedDate = ref(dayjs())

interface ChartParams {
  name: string
  value: number
  axisDim: string
  axisIndex: number
  seriesIndex: number
  dataIndex: number
  percent?: number
}

interface ChartLabelFormatterParams {
  name: string
  value: number
  percent: number
}

// Add customer data
const customers = [
  { id: 0, name: 'All Customers' },
  { id: 1, name: 'Customer A' },
  { id: 2, name: 'Customer B' },
]

const selectedCustomer = ref(0)

// Add mock category data
// const categories = ref<Category[]>([...]) // Removing unused mock data

// Update monthly columns
const monthlyColumns = [
  {
    title: 'Category',
    dataIndex: 'name',
    key: 'name',
    width: '40%',
  },
  {
    title: 'Quantity',
    dataIndex: 'quantity',
    key: 'quantity',
    align: 'right' as AlignType,
  },
  {
    title: 'Charges',
    dataIndex: 'charges',
    key: 'charges',
    align: 'right' as AlignType,
  },
]

// Update monthly data structure to include category charges
interface CategoryCharge {
  categoryId: number
  categoryCode: string
  categoryName: string
  quantity: number
  unitPrice: number
  totalCharge: number
}

interface YearlyData {
  year: string
  totalInbound: number
  totalOutbound: number
  totalCharges: number
  monthlyBreakdown: Array<{ month: string; amount: number }>
}

interface MonthlyData {
  date: string
  customerId: number
  customerName: string
  totalInbound: number
  totalOutbound: number
  categoryCharges: CategoryCharge[]
  totalCharges: number
}

const monthlyData = ref<MonthlyData[]>([
  {
    date: '2024-03',
    customerId: 1,
    customerName: 'Customer A',
    totalInbound: 156,
    totalOutbound: 142,
    categoryCharges: [
      {
        categoryId: 1,
        categoryCode: 'CAT-001',
        categoryName: 'Standard Storage',
        quantity: 1000,
        unitPrice: 5000,
        totalCharge: 5000000,
      },
      {
        categoryId: 2,
        categoryCode: 'CAT-002',
        categoryName: 'Cold Storage',
        quantity: 500,
        unitPrice: 8000,
        totalCharge: 4000000,
      },
      {
        categoryId: 3,
        categoryCode: 'CAT-003',
        categoryName: 'Handling',
        quantity: 290,
        unitPrice: 2000,
        totalCharge: 580000,
      },
      {
        categoryId: 4,
        categoryCode: 'CAT-004',
        categoryName: 'Packaging',
        quantity: 150,
        unitPrice: 3000,
        totalCharge: 450000,
      },
      {
        categoryId: 5,
        categoryCode: 'CAT-005',
        categoryName: 'Transportation',
        quantity: 80,
        unitPrice: 10000,
        totalCharge: 800000,
      },
    ],
    totalCharges: 10830000,
  },
  {
    date: '2024-03',
    customerId: 2,
    customerName: 'Customer B',
    totalInbound: 120,
    totalOutbound: 115,
    categoryCharges: [
      {
        categoryId: 1,
        categoryCode: 'CAT-001',
        categoryName: 'Standard Storage',
        quantity: 800,
        unitPrice: 5000,
        totalCharge: 4000000,
      },
      {
        categoryId: 2,
        categoryCode: 'CAT-002',
        categoryName: 'Cold Storage',
        quantity: 400,
        unitPrice: 8000,
        totalCharge: 3200000,
      },
      {
        categoryId: 3,
        categoryCode: 'CAT-003',
        categoryName: 'Handling',
        quantity: 235,
        unitPrice: 2000,
        totalCharge: 470000,
      },
      {
        categoryId: 4,
        categoryCode: 'CAT-004',
        categoryName: 'Packaging',
        quantity: 120,
        unitPrice: 3000,
        totalCharge: 360000,
      },
      {
        categoryId: 5,
        categoryCode: 'CAT-005',
        categoryName: 'Transportation',
        quantity: 65,
        unitPrice: 10000,
        totalCharge: 650000,
      },
    ],
    totalCharges: 8680000,
  },
])

// Update the filtered data computed property
const filteredMonthlyData = computed(() => {
  if (selectedCustomer.value === 0) {
    // Return aggregated data for all customers
    const aggregatedCharges = monthlyData.value.reduce(
      (acc, customer) => {
        customer.categoryCharges.forEach((charge) => {
          const existingCharge = acc.categoryCharges.find((c) => c.categoryId === charge.categoryId)
          if (existingCharge) {
            existingCharge.quantity += charge.quantity
            existingCharge.totalCharge += charge.totalCharge
          } else {
            acc.categoryCharges.push({ ...charge })
          }
        })
        acc.totalInbound += customer.totalInbound
        acc.totalOutbound += customer.totalOutbound
        acc.totalCharges += customer.totalCharges
        return acc
      },
      {
        date: monthlyData.value[0].date,
        customerId: 0,
        customerName: 'All Customers',
        totalInbound: 0,
        totalOutbound: 0,
        categoryCharges: [] as CategoryCharge[],
        totalCharges: 0,
      },
    )
    return [aggregatedCharges]
  }
  return monthlyData.value.filter((item) => item.customerId === selectedCustomer.value)
})

// Update yearly data with customer information
const yearlyData = ref([
  {
    year: '2024',
    customerId: 1,
    customerName: 'Customer A',
    totalInbound: 1850,
    totalOutbound: 1680,
    storageCharges: 285000.5,
    handlingCharges: 215500.75,
    otherCharges: 125250.0,
    totalCharges: 625750.25,
    monthlyBreakdown: [
      { month: 'Jan', amount: 45000 },
      { month: 'Feb', amount: 52000 },
      { month: 'Mar', amount: 48000 },
      { month: 'Apr', amount: 55000 },
      { month: 'May', amount: 51000 },
      { month: 'Jun', amount: 58000 },
    ],
  },
  {
    year: '2024',
    customerId: 2,
    customerName: 'Customer B',
    totalInbound: 1450,
    totalOutbound: 1380,
    storageCharges: 245000.5,
    handlingCharges: 185500.75,
    otherCharges: 95250.0,
    totalCharges: 525750.25,
    monthlyBreakdown: [
      { month: 'Jan', amount: 40000 },
      { month: 'Feb', amount: 48000 },
      { month: 'Mar', amount: 45000 },
      { month: 'Apr', amount: 50000 },
      { month: 'May', amount: 47000 },
      { month: 'Jun', amount: 53000 },
    ],
  },
])

// Filter yearly data based on selected customer
const filteredYearlyData = computed(() => {
  if (selectedCustomer.value === 0) {
    // Return aggregated data for all customers
    return [
      {
        year: yearlyData.value[0].year,
        totalInbound: yearlyData.value.reduce((sum, item) => sum + item.totalInbound, 0),
        totalOutbound: yearlyData.value.reduce((sum, item) => sum + item.totalOutbound, 0),
        totalCharges: yearlyData.value.reduce((sum, item) => sum + item.totalCharges, 0),
        monthlyBreakdown: yearlyData.value[0].monthlyBreakdown.map((_, index) => ({
          month: yearlyData.value[0].monthlyBreakdown[index].month,
          amount: yearlyData.value.reduce(
            (sum, item) => sum + item.monthlyBreakdown[index].amount,
            0,
          ),
        })),
      },
    ]
  }
  return yearlyData.value.filter((item) => item.customerId === selectedCustomer.value)
})

// Update monthly report data to show category-based breakdown
interface TableRow {
  name: string
  quantity: string
  charges: string
  type: 'category' | 'total'
}

const monthlyReportData = computed(() => {
  const data = filteredMonthlyData.value[0]
  const rows: TableRow[] = []

  // Add category rows
  data.categoryCharges.forEach((category) => {
    rows.push({
      name: category.categoryName,
      quantity: category.quantity.toLocaleString(),
      charges: formatCurrency(category.totalCharge),
      type: 'category',
    })
  })

  // Add total row
  rows.push({
    name: 'Total Charges',
    quantity: '',
    charges: formatCurrency(data.totalCharges),
    type: 'total',
  })

  return rows
})

// Update chart options to use filtered data
const yearlyChartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: (params: ChartParams[]) => {
      const data = params[0]
      return `${data.name}<br/>${formatCurrency(data.value)}`
    },
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: filteredYearlyData.value[0].monthlyBreakdown.map((item) => item.month),
    boundaryGap: true,
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: (value: number) => formatCurrency(value),
    },
  },
  series: [
    {
      name: 'Monthly Charges',
      type: 'bar',
      data: filteredYearlyData.value[0].monthlyBreakdown.map((item) => item.amount),
      itemStyle: {
        color: '#1890ff',
      },
      label: {
        show: true,
        position: 'top',
        formatter: (params: ChartParams) => formatCurrency(params.value),
      },
    },
  ],
}))

// Update the monthly distribution option
const monthlyDistributionOption = computed(() => {
  const data = filteredMonthlyData.value[0]

  return {
    tooltip: {
      trigger: 'item',
      formatter: (params: ChartParams) => {
        return `${params.name}: ${formatCurrency(params.value)} (${params.percent}%)`
      },
    },
    legend: {
      orient: 'vertical',
      left: 'left',
    },
    series: [
      {
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: true,
          formatter: (params: ChartLabelFormatterParams) => formatCurrency(params.value),
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold',
          },
        },
        data: data.categoryCharges.map((category) => ({
          value: category.totalCharge,
          name: category.categoryName,
          itemStyle: { color: getColorForCategory(category.categoryId) },
        })),
      },
    ],
  }
})

// Add a function to get consistent colors for categories
const getColorForCategory = (categoryId: number) => {
  const colors = {
    1: '#1890ff', // Standard Storage - Blue
    2: '#52c41a', // Cold Storage - Green
    3: '#faad14', // Handling - Yellow
    4: '#722ed1', // Packaging - Purple
    5: '#eb2f96', // Transportation - Pink
  }
  return colors[categoryId as keyof typeof colors] || '#666666'
}

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount)
}

// Handle date change
const handleDateChange = (value: Dayjs | string) => {
  if (value) {
    selectedDate.value = typeof value === 'string' ? dayjs(value) : value
    // TODO: Fetch data for selected date
  }
}

// Handle report download
const handleDownload = async () => {
  try {
    const reportElement = document.querySelector(
      `.report-content-${activeTab.value}`,
    ) as HTMLElement
    if (!reportElement) {
      console.error('Report content element not found')
      return
    }

    // Create canvas from the report content
    const canvas = await html2canvas(reportElement, {
      scale: 2,
      useCORS: true,
      logging: false,
      backgroundColor: '#ffffff',
    })

    // Initialize PDF with larger format for better quality
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4',
    })

    const pageWidth = pdf.internal.pageSize.getWidth()

    // Add title with larger font and center alignment
    pdf.setFontSize(24)
    pdf.setTextColor(0, 0, 0)
    const title = 'Warehouse Operations Report'
    const titleWidth =
      (pdf.getStringUnitWidth(title) * pdf.getFontSize()) / pdf.internal.scaleFactor
    const titleX = (pageWidth - titleWidth) / 2
    pdf.text(title, titleX, 20)

    // Add horizontal line under title
    pdf.setLineWidth(0.5)
    pdf.line(20, 25, pageWidth - 20, 25)

    // Add report info in a table format
    pdf.setFontSize(11)
    const reportDate = dayjs().format('YYYY-MM-DD HH:mm')
    const reportPeriod =
      activeTab.value === 'monthly'
        ? selectedDate.value.format('MMMM YYYY')
        : selectedDate.value.format('YYYY')
    const customerName =
      customers.find((c) => c.id === selectedCustomer.value)?.name || 'All Customers'

    // Create info table with gray background
    pdf.setFillColor(240, 240, 240)
    pdf.rect(20, 35, pageWidth - 40, 30, 'F')

    const infoTable = [
      ['Report Type:', `${activeTab.value === 'monthly' ? 'Monthly' : 'Yearly'} Report`],
      ['Report Period:', reportPeriod],
      ['Customer:', customerName],
      ['Generated on:', reportDate],
    ]

    pdf.setFontSize(10)
    infoTable.forEach((row, index) => {
      pdf.setFont('helvetica', 'bold')
      pdf.text(row[0], 25, 43 + index * 7)
      pdf.setFont('helvetica', 'normal')
      pdf.text(row[1], 60, 43 + index * 7)
    })

    // Add summary section
    pdf.setFontSize(14)
    pdf.setFont('helvetica', 'bold')
    pdf.text('Summary', 20, 80)

    const data =
      activeTab.value === 'monthly'
        ? (filteredMonthlyData.value[0] as MonthlyData)
        : (filteredYearlyData.value[0] as YearlyData)

    // Create summary table with category breakdown for monthly report
    if (activeTab.value === 'monthly') {
      const monthlyData = data as MonthlyData
      // Draw summary table header
      pdf.setFillColor(240, 240, 240)
      pdf.rect(20, 85, pageWidth - 40, 7, 'F')

      pdf.setFontSize(10)
      pdf.setFont('helvetica', 'bold')
      pdf.text('Category', 25, 90)
      pdf.text('Quantity', pageWidth - 80, 90)
      pdf.text('Charges', pageWidth - 35, 90)

      // Draw category rows
      let yPos = 97
      monthlyData.categoryCharges.forEach((category: CategoryCharge, index: number) => {
        if (index % 2 === 0) {
          pdf.setFillColor(250, 250, 250)
          pdf.rect(20, yPos - 4, pageWidth - 40, 7, 'F')
        }
        pdf.setFont('helvetica', 'normal')
        pdf.text(category.categoryName, 25, yPos)
        pdf.text(category.quantity.toLocaleString(), pageWidth - 80, yPos, { align: 'right' })
        pdf.text(formatCurrency(category.totalCharge), pageWidth - 35, yPos, { align: 'right' })
        yPos += 7
      })

      // Draw total row
      pdf.setFillColor(240, 245, 255)
      pdf.rect(20, yPos - 4, pageWidth - 40, 7, 'F')
      pdf.setFont('helvetica', 'bold')
      pdf.text('Total', 25, yPos)
      pdf.text(formatCurrency(monthlyData.totalCharges), pageWidth - 35, yPos, { align: 'right' })
      yPos += 15

      // Add charts section
      pdf.addPage()
      pdf.setFontSize(14)
      pdf.setFont('helvetica', 'bold')
      pdf.text('Visualizations', 20, 20)

      // Add the canvas image
      const imgData = canvas.toDataURL('image/png')
      const imgWidth = pageWidth - 40
      const imgHeight = (canvas.height * imgWidth) / canvas.width
      pdf.addImage(imgData, 'PNG', 20, 30, imgWidth, imgHeight)
    } else {
      const yearlyData = data as YearlyData
      // Yearly report format
      const summaryData = [
        ['Year', yearlyData.year],
        ['Total Inbound Products', yearlyData.totalInbound.toString()],
        ['Total Outbound Products', yearlyData.totalOutbound.toString()],
        ['Total Charges', formatCurrency(yearlyData.totalCharges)],
        [
          'Monthly Average',
          formatCurrency(yearlyData.totalCharges / yearlyData.monthlyBreakdown.length),
        ],
      ]

      pdf.setFontSize(10)
      let yPos = 85
      summaryData.forEach((row, index) => {
        if (index === 0) {
          pdf.setFont('helvetica', 'bold')
        } else {
          pdf.setFont('helvetica', 'normal')
        }
        pdf.text(row[0], 25, yPos)
        pdf.text(row[1], pageWidth - 35, yPos, { align: 'right' })
        yPos += 7
      })

      // Add monthly breakdown
      pdf.setFontSize(14)
      pdf.setFont('helvetica', 'bold')
      pdf.text('Monthly Breakdown', 20, yPos + 10)

      // Draw monthly breakdown table
      pdf.setFillColor(240, 240, 240)
      pdf.rect(20, yPos + 15, pageWidth - 40, 7, 'F')

      pdf.setFontSize(10)
      pdf.text('Month', 25, yPos + 20)
      pdf.text('Amount', pageWidth - 35, yPos + 20)

      yPos += 27
      yearlyData.monthlyBreakdown.forEach(
        (month: { month: string; amount: number }, index: number) => {
          if (index % 2 === 0) {
            pdf.setFillColor(250, 250, 250)
            pdf.rect(20, yPos - 4, pageWidth - 40, 7, 'F')
          }
          pdf.setFont('helvetica', 'normal')
          pdf.text(month.month, 25, yPos)
          pdf.text(formatCurrency(month.amount), pageWidth - 35, yPos, { align: 'right' })
          yPos += 7
        },
      )

      // Add visualizations
      pdf.addPage()
      pdf.setFontSize(14)
      pdf.setFont('helvetica', 'bold')
      pdf.text('Visualizations', 20, 20)

      const imgData = canvas.toDataURL('image/png')
      const imgWidth = pageWidth - 40
      const imgHeight = (canvas.height * imgWidth) / canvas.width
      pdf.addImage(imgData, 'PNG', 20, 30, imgWidth, imgHeight)
    }

    // Save the PDF with appropriate naming
    const fileName =
      activeTab.value === 'monthly'
        ? `warehouse-report-${selectedDate.value.format('YYYY-MM')}.pdf`
        : `warehouse-report-${selectedDate.value.format('YYYY')}.pdf`

    pdf.save(fileName)
  } catch (error) {
    console.error('Error generating PDF:', error)
  }
}
</script>

<template>
  <div class="header-container-master">
    <PageHeader title="Warehouse Operations Report" sub-title="View report warehouse">
      <template #extra>
        <Button type="primary" @click="handleDownload">
          <DownloadOutlined /> Download Report
        </Button>
      </template>
    </PageHeader>

    <div class="report-container">
      <Card>
        <Tabs v-model:activeKey="activeTab">
          <!-- Monthly Report Tab -->
          <Tabs.TabPane key="monthly" tab="Monthly Report">
            <div class="report-content-monthly">
              <div class="tab-header">
                <div class="filter-container">
                  <DatePicker
                    v-model:value="selectedDate"
                    picker="month"
                    @change="handleDateChange"
                    style="width: 200px"
                  />
                  <Select
                    v-model:value="selectedCustomer"
                    style="width: 200px; margin-left: 16px"
                    placeholder="Select Customer"
                  >
                    <Select.Option
                      v-for="customer in customers"
                      :key="customer.id"
                      :value="customer.id"
                    >
                      {{ customer.name }}
                    </Select.Option>
                  </Select>
                </div>
              </div>

              <Row :gutter="[16, 16]">
                <Col :span="12">
                  <Card title="Monthly Summary">
                    <Table
                      :columns="monthlyColumns"
                      :data-source="monthlyReportData"
                      :pagination="false"
                      size="middle"
                      :row-class-name="
                        (record: TableRow) => {
                          if (record.type === 'category') return 'category-row'
                          if (record.type === 'total') return 'total-row'
                          return ''
                        }
                      "
                    />
                  </Card>
                </Col>
                <Col :span="12">
                  <Card title="Charges Distribution">
                    <div class="chart-container">
                      <v-chart
                        style="height: 300px"
                        :option="monthlyDistributionOption"
                        :autoresize="true"
                      />
                    </div>
                  </Card>
                </Col>
              </Row>
            </div>
          </Tabs.TabPane>

          <!-- Yearly Report Tab -->
          <Tabs.TabPane key="yearly" tab="Yearly Report">
            <div class="report-content-yearly">
              <div class="tab-header">
                <div class="filter-container">
                  <DatePicker
                    v-model:value="selectedDate"
                    picker="year"
                    @change="handleDateChange"
                    style="width: 200px"
                  />
                  <Select
                    v-model:value="selectedCustomer"
                    style="width: 200px; margin-left: 16px"
                    placeholder="Select Customer"
                  >
                    <Select.Option
                      v-for="customer in customers"
                      :key="customer.id"
                      :value="customer.id"
                    >
                      {{ customer.name }}
                    </Select.Option>
                  </Select>
                </div>
              </div>

              <Row :gutter="[16, 16]">
                <Col :span="24">
                  <Card title="Yearly Trend">
                    <div class="chart-container">
                      <v-chart
                        style="height: 400px"
                        :option="yearlyChartOption"
                        :autoresize="true"
                      />
                    </div>
                  </Card>
                </Col>
              </Row>

              <Row :gutter="[16, 16]" class="mt-4">
                <Col :span="24">
                  <Card title="Yearly Summary">
                    <Table
                      :columns="[
                        { title: 'Year', dataIndex: 'year', key: 'year' },
                        { title: 'Total Inbound', dataIndex: 'totalInbound', key: 'totalInbound' },
                        {
                          title: 'Total Outbound',
                          dataIndex: 'totalOutbound',
                          key: 'totalOutbound',
                        },
                        {
                          title: 'Total Charges',
                          dataIndex: 'totalCharges',
                          key: 'totalCharges',
                          customRender: ({ text }) => formatCurrency(text),
                        },
                      ]"
                      :data-source="filteredYearlyData"
                      :pagination="false"
                      size="middle"
                    />
                  </Card>
                </Col>
              </Row>
            </div>
          </Tabs.TabPane>
        </Tabs>
      </Card>
    </div>
  </div>
</template>

<style scoped>
.tab-header {
  margin-bottom: 24px;
}

.report-content-monthly,
.report-content-yearly {
  background: white;
  padding: 5px;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.mt-4 {
  margin-top: 24px;
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

:deep(.category-row) {
  background-color: #fafafa;
  color: rgba(0, 0, 0, 0.85);
}

:deep(.total-row) {
  background-color: #f0f5ff;
  font-weight: 700;
  border-top: 1px solid #d9d9d9;
}
</style>
