import { axiosClient } from '@IVC/common/utils/axiosClient'
import { message } from 'ant-design-vue'
import axios from 'axios'
import type {
  WorkoutCustomerCharge,
  WorkoutCustomerChargeFormData,
  CategoryOption,
  CategoryWorkoutOption,
  CustomerOption,
  DivisionOption,
} from '../../../types/WorkoutCustomerCharge'

// Get Workout Customer Charge by ID
export async function getWorkoutCustomerChargeById(id: string): Promise<WorkoutCustomerCharge | null> {
  try {
    const response = await axiosClient.get<{status: string, data: WorkoutCustomerCharge}>(`/workout-charges/${id}`)
    return response.data.data
  } catch (error) {
    if (axios.isAxiosError(error) && error.response?.status === 404) {
      return null
    }
    if (axios.isAxiosError(error)) {
      console.error(`Failed to get workout customer charge with ID ${id}:`, error.response?.data?.message || error.message)
    }
    throw error
  }
}

// Get all Workout Customer Charges with optional filters
export async function getWorkoutCustomerCharges(filters?: {
  customer_id?: number
  division_id?: number
}): Promise<WorkoutCustomerCharge[]> {
  try {
    let url = '/workout-charges/'
    const params = new URLSearchParams()

    if (filters?.customer_id) {
      params.append('customer_id', filters.customer_id.toString())
    }
    if (filters?.division_id) {
      params.append('division_id', filters.division_id.toString())
    }

    if (params.toString()) {
      url += `?${params.toString()}`
    }

    const response = await axiosClient.get<{status: string, data: WorkoutCustomerCharge[]}>(url)
    return response.data.data
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Failed to get workout customer charges:', error.response?.data?.message || error.message)
    }
    throw error
  }
}

// Get existing Workout Customer Charge for customer/division
export async function getWorkoutCustomerChargeByCustomerDivision(
  customerId: number,
  divisionId: number | null,
): Promise<WorkoutCustomerCharge | null> {
  const divisionParam = divisionId === null ? 'null' : divisionId.toString()
  try {
    console.log(`Getting workout charge for customer ${customerId}, division ${divisionParam}`)
    const response = await axiosClient.get<{status: string, data: WorkoutCustomerCharge}>(
      `/workout-charges/customer/${customerId}/division/${divisionParam}`
    )
    console.log('Retrieved workout charge data:', response.data.data)
    return response.data.data
  } catch (error) {
    if (axios.isAxiosError(error) && error.response?.status === 404) {
      console.log(`No workout charge found for customer ${customerId}, division ${divisionParam}`)
      return null
    }
    if (axios.isAxiosError(error)) {
      console.error(`Failed to get workout customer charge for customer/division ${customerId}/${divisionId}:`, error.response?.data?.message || error.message)
    }
    throw error
  }
}

// Create new Workout Customer Charge (safe create - fails if exists)
export async function createWorkoutCustomerCharge(
  data: WorkoutCustomerChargeFormData,
): Promise<WorkoutCustomerCharge> {
  try {
    console.log('Creating workout customer charge with data:', {
      customer_id: data.customer_id,
      division_id: data.division_id,
      content_keys: Object.keys(data.workout_customer_charge_content || {})
    })
    const response = await axiosClient.post<{status: string, data: WorkoutCustomerCharge, message: string}>('/workout-charges/', {
      customer_id: data.customer_id,
      division_id: data.division_id,
      workout_customer_charge_content: data.workout_customer_charge_content
    })
    console.log('Created workout customer charge:', response.data.data)
    message.success('Workout customer charge created successfully!')
    return response.data.data
  } catch (error) {
    console.error('Error creating workout customer charge:', error)
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 400) {
        message.error(error.response.data.message || 'Workout charge already exists for this customer/division')
      } else {
        message.error('Failed to create workout customer charge')
      }
    }
    throw error
  }
}

// Replace Workout Customer Charge (UPSERT - always succeeds)
export async function replaceWorkoutCustomerCharge(
  data: WorkoutCustomerChargeFormData,
): Promise<WorkoutCustomerCharge> {
  try {
    console.log('Replacing workout customer charge with data:', {
      customer_id: data.customer_id,
      division_id: data.division_id,
      content_keys: Object.keys(data.workout_customer_charge_content || {})
    })
    const response = await axiosClient.post<{status: string, data: WorkoutCustomerCharge, message: string}>('/workout-charges/replace', {
      customer_id: data.customer_id,
      division_id: data.division_id,
      workout_customer_charge_content: data.workout_customer_charge_content
    })
    console.log('Replaced workout customer charge:', response.data.data)
    message.success('Workout customer charge updated successfully!')
    return response.data.data
  } catch (error) {
    console.error('Error replacing workout customer charge:', error)
    if (axios.isAxiosError(error)) {
      message.error(error.response?.data?.message || 'Failed to update workout customer charge')
    }
    throw error
  }
}

// Update Workout Customer Charge
export async function updateWorkoutCustomerCharge(
  id: string,
  data: WorkoutCustomerChargeFormData,
): Promise<WorkoutCustomerCharge> {
  try {
    const response = await axiosClient.put<{status: string, data: WorkoutCustomerCharge, message: string}>(`/workout-charges/${id}`, {
      customer_id: data.customer_id,
      division_id: data.division_id,
      workout_customer_charge_content: data.workout_customer_charge_content
    })
    console.log('Updated workout customer charge:', response.data.data)
    message.success('Workout customer charge updated successfully!')
    return response.data.data
  } catch (error) {
    console.error('Error updating workout customer charge:', error)
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 404) {
        message.error('Workout customer charge not found')
      } else {
        message.error(error.response?.data?.message || 'Failed to update workout customer charge')
      }
    }
    throw error
  }
}

// Delete Workout Customer Charge
export async function deleteWorkoutCustomerCharge(id: string): Promise<void> {
  try {
    await axiosClient.delete(`/workout-charges/${id}`)
    message.success('Workout customer charge deleted successfully!')
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 404) {
        message.error('Workout customer charge not found')
      } else {
        message.error(error.response?.data?.message || 'Failed to delete workout customer charge')
      }
    }
    throw error
  }
}

// Get customers for dropdown
export async function getCustomersForDropdown(): Promise<CustomerOption[]> {
  try {
    const response = await axiosClient.get<{status: string, data: CustomerOption[]}>('/customers/')
    return response.data.data.filter(customer => customer.name && customer.code)
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Failed to get customers for dropdown:', error.response?.data?.message || error.message)
    }
    throw error
  }
}

// Get divisions for dropdown by customer ID
export async function getDivisionsByCustomerId(customerId: number): Promise<DivisionOption[]> {
  try {
    const response = await axiosClient.get<{status: string, data: DivisionOption[]}>(`/customers/${customerId}/divisions`)
    return response.data.data.filter(division => division.name && division.code)
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Failed to get divisions by customer ID:', error.response?.data?.message || error.message)
    }
    throw error
  }
}

// Get categories for dropdown
export async function getCategoriesForDropdown(): Promise<CategoryOption[]> {
  try {
    const response = await axiosClient.get<{status: string, data: CategoryOption[]}>('/workout-charges/categories/')
    return response.data.data.filter(category => category.name)
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Failed to get categories for dropdown:', error.response?.data?.message || error.message)
    }
    // Return mock data as fallback
    throw error
  }
}

// Get category workouts by category ID
export async function getCategoryWorkoutsByCategoryId(
  categoryId: string,
): Promise<CategoryWorkoutOption[]> {
  try {
    const response = await axiosClient.get<{status: string, data: CategoryWorkoutOption[]}>(`/workout-charges/categories/${categoryId}/workouts`)
    return response.data.data.filter(workout => workout.name)
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Failed to get category workouts:', error.response?.data?.message || error.message)
    }
    // Return mock data as fallback
    throw error
  }
}
