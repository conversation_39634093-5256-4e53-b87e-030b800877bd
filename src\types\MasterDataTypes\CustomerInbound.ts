export interface CustomerInbound {
  id: number // PRIMARY KEY
  customerId: number // customer_id
  avrAsNum: string // avr_as_num
  avhArvYmd: string // avh_arv_ymd (ISO date: YYYY-MM-DD)
  avrProdCod: number // avr_prod_cod
  avdProdNam: string // avd_prod_nam
  avrRtpcQty: number // avr_rtpc_qty
  prodPpcNum: number // prod_ppc_num
  prodHrc1: string // prod_hrc1
  ai: number // ai
  innerMaster: number // inner_master
  carton: number // carton
  pcs: number // pcs
  innerCarton: number // inner_carton
  innerPcs: number // inner_pcs
  totalCarton: number // total_carton
  totalPcs: number // total_pcs
  totalAi: number // total_ai
  totalM3: number // total_m3
  createdAt: string // created_at (ISO datetime)
  updatedAt: string // updated_at (ISO datetime)
}
