<template>
  <div class="inbound-list-view">
    <div class="header-container-invoice">
      <a-page-header title="Inbound List" sub-title="View and search imported inbound records">
        <template #extra>
          <a-button type="primary" @click="goToImportPage">
            <ImportOutlined /> Import Inbound
          </a-button>
        </template>
      </a-page-header>

      <!-- Filter Icon Toggle -->
      <div>
        <a-button
          class="btn-search-condition"
          type="default"
          shape="square"
          @click="toggleFilter"
          :title="showFilter ? 'Hide filters' : 'Show filters'"
        >
          <template #icon>
            <FilterOutlined />
          </template>
          Search Filter
        </a-button>
      </div>

      <!-- Search Filters -->
      <div class="search-section" v-show="showFilter">
        <a-card title="Search Filters" class="search-card">
          <a-form layout="vertical">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="Customer">
                  <a-select
                    v-model:value="searchFilters.customerId"
                    placeholder="Select customer"
                    allow-clear
                    show-search
                    :filter-option="filterCustomerOption"
                    @change="handleCustomerChange"
                  >
                    <a-select-option
                      v-for="customer in customers"
                      :key="customer.id"
                      :value="customer.id"
                    >
                      {{ customer.name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="Division">
                  <a-select
                    v-model:value="searchFilters.divisionId"
                    placeholder="Select division"
                    allow-clear
                    show-search
                    :filter-option="filterDivisionOption"
                    @change="handleDivisionChange"
                    :disabled="!searchFilters.customerId"
                  >
                    <a-select-option
                      v-for="division in filteredDivisions"
                      :key="division.id"
                      :value="division.id"
                    >
                      {{ division.name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="Month">
                  <a-date-picker
                    v-model:value="searchFilters.month"
                    picker="month"
                    placeholder="Select month"
                    format="YYYY-MM"
                    value-format="YYYY-MM"
                    @change="handleMonthChange"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="Actions">
                  <a-space>
                    <a-button type="primary" @click="searchData" :loading="isLoading">
                      <template #icon>
                        <SearchOutlined />
                      </template>
                      Search
                    </a-button>
                    <a-button @click="clearFilters">
                      <template #icon>
                        <ClearOutlined />
                      </template>
                      Clear
                    </a-button>
                  </a-space>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>
      </div>

      <!-- Error Message -->
      <a-alert
        v-if="errorMessage"
        type="error"
        :message="errorMessage"
        show-icon
        closable
        @close="errorMessage = ''"
        style="margin-bottom: 16px"
      />

      <!-- Data Table -->

      <!-- Inbound Records Title and Actions -->
      <div class="inbound-header">
        <a-space>
          <span>Inbound Records</span>
          <a-tag v-if="hasSearched" color="blue">{{ pagination.total }} records</a-tag>
        </a-space>
        <a-space>
          <a-button @click="refreshData" :loading="isLoading">
            <template #icon>
              <ReloadOutlined />
            </template>
            Refresh
          </a-button>
          <a-button type="primary" @click="exportData" :disabled="inboundData.length === 0">
            <template #icon>
              <DownloadOutlined />
            </template>
            Export
          </a-button>
        </a-space>
      </div>

      <div class="table-section">
        <a-table
          :dataSource="inboundData"
          :columns="tableColumns"
          :pagination="paginationConfig"
          :loading="isLoading"
          :scroll="{ x: 2500 }"
          size="middle"
          @change="handleTableChange"
        >
          <!-- Custom render for date column -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'avh_arv_ymd'">
              <a-tag color="blue">
                {{ formatDate(record.avh_arv_ymd) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'customer_name'">
              <a-space>
                <a-avatar size="small" style="background-color: #1890ff">
                  {{ record.customer_name?.charAt(0) || 'N' }}
                </a-avatar>
                <span>{{ record.customer_name || 'Unknown' }}</span>
              </a-space>
            </template>
            <template v-else-if="column.key === 'division_name'">
              <a-tag v-if="record.division_name" color="green">
                {{ record.division_name }}
              </a-tag>
              <span v-else style="color: #999">No Division</span>
            </template>
            <template v-else-if="column.key === 'created_at'">
              <span style="color: #666; font-size: 12px">
                {{ formatDateTime(record.created_at) }}
              </span>
            </template>
            <template v-else-if="column.key === 'total_m3'">
              <span style="font-weight: 500">
                {{ Number(record.total_m3).toFixed(3) }}
              </span>
            </template>
            <template v-else-if="column.key === 'actions'">
              <ActionButtons
                name="Record"
                :show-edit="false"
                :show-view="true"
                :show-delete="true"
                :show-save="false"
                :show-download="false"
                :show-add-customer-divisions="false"
                :show-setting-button="false"
                :show-add-workout="false"
                @view="viewRecord(record)"
                @delete="deleteRecord(record)"
              />
            </template>
          </template>
        </a-table>
      </div>

      <!-- Record Detail Modal -->
      <a-modal
        v-model:open="showDetailModal"
        title="Inbound Record Details"
        :width="800"
        :footer="null"
      >
        <div v-if="selectedRecord" class="record-detail">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="ID">{{ selectedRecord.id }}</a-descriptions-item>
            <a-descriptions-item label="Customer">{{
              selectedRecord.customer_name
            }}</a-descriptions-item>
            <a-descriptions-item label="Division">{{
              selectedRecord.division_name || 'No Division'
            }}</a-descriptions-item>
            <a-descriptions-item label="ASN Number">{{
              selectedRecord.avr_as_num
            }}</a-descriptions-item>
            <a-descriptions-item label="ASN Line Number">{{
              selectedRecord.avr_asln_num
            }}</a-descriptions-item>
            <a-descriptions-item label="ASN Sequence Number">{{
              selectedRecord.avr_assq_num
            }}</a-descriptions-item>
            <a-descriptions-item label="Arrival Date">{{
              formatDate(selectedRecord.avh_arv_ymd)
            }}</a-descriptions-item>
            <a-descriptions-item label="Product Code">{{
              selectedRecord.avr_prod_cod
            }}</a-descriptions-item>
            <a-descriptions-item label="Product Name" :span="2">{{
              selectedRecord.avd_prod_nam
            }}</a-descriptions-item>
            <a-descriptions-item label="RTPC Quantity">{{
              selectedRecord.avr_rtpc_qty
            }}</a-descriptions-item>
            <a-descriptions-item label="PPC Number">{{
              selectedRecord.prod_ppc_num
            }}</a-descriptions-item>
            <a-descriptions-item label="HRC1">{{ selectedRecord.prod_hrc1 }}</a-descriptions-item>
            <a-descriptions-item label="AI">{{ selectedRecord.ai }}</a-descriptions-item>
            <a-descriptions-item label="Inner Master">{{
              selectedRecord.inner_master
            }}</a-descriptions-item>
            <a-descriptions-item label="Carton">{{ selectedRecord.carton }}</a-descriptions-item>
            <a-descriptions-item label="PCS">{{ selectedRecord.pcs }}</a-descriptions-item>
            <a-descriptions-item label="Inner Carton">{{
              selectedRecord.inner_carton
            }}</a-descriptions-item>
            <a-descriptions-item label="Inner PCS">{{
              selectedRecord.inner_pcs
            }}</a-descriptions-item>
            <a-descriptions-item label="Total Carton">{{
              selectedRecord.total_carton
            }}</a-descriptions-item>
            <a-descriptions-item label="Total PCS">{{
              selectedRecord.total_pcs
            }}</a-descriptions-item>
            <a-descriptions-item label="Total AI">{{
              selectedRecord.total_ai
            }}</a-descriptions-item>
            <a-descriptions-item label="Total M3">{{
              Number(selectedRecord.total_m3).toFixed(3)
            }}</a-descriptions-item>
            <a-descriptions-item label="Created At">{{
              formatDateTime(selectedRecord.created_at)
            }}</a-descriptions-item>
            <a-descriptions-item label="Updated At">{{
              formatDateTime(selectedRecord.updated_at)
            }}</a-descriptions-item>
          </a-descriptions>
        </div>
      </a-modal>

      <!-- Delete Confirmation Modal -->
      <a-modal
        v-model:open="showDeleteModal"
        title="Confirm Delete"
        :width="400"
        @ok="confirmDelete"
        @cancel="cancelDelete"
      >
        <p>Are you sure you want to delete this inbound record?</p>
        <p><strong>ASN:</strong> {{ recordToDelete?.avr_as_num }}</p>
        <p><strong>Product:</strong> {{ recordToDelete?.avd_prod_nam }}</p>
      </a-modal>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import {
  SearchOutlined,
  ClearOutlined,
  ReloadOutlined,
  DownloadOutlined,
  ImportOutlined,
  FilterOutlined,
} from '@ant-design/icons-vue'
import {
  type InboundRecord,
  type Customer,
  type Division,
  type SearchFilters,
  type PaginationParams,
  loadCustomers as loadCustomersService,
  loadDivisions as loadDivisionsService,
  searchInboundData,
  deleteInboundRecord,
  initializeDatabase,
} from '@IVC/services/Invoice/InboundListService'
import ActionButtons from '@IVC/components/ActionButtons.vue'

// Router hook
const router = useRouter()

// Reactive state
const isLoading = ref(false)
const errorMessage = ref('')
const hasSearched = ref(false)
const showDetailModal = ref(false)
const showDeleteModal = ref(false)
const selectedRecord = ref<InboundRecord | null>(null)
const recordToDelete = ref<InboundRecord | null>(null)
const showFilter = ref(false)

// Data arrays
const inboundData = ref<InboundRecord[]>([])
const customers = ref<Customer[]>([])
const divisions = ref<Division[]>([])

// Search filters
const searchFilters = reactive<SearchFilters>({
  customerId: null,
  divisionId: null,
  month: null,
})

// Pagination
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
})

const paginationConfig = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: (total: number, range: [number, number]) =>
    `${range[0]}-${range[1]} of ${total} items`,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50', '100'],
})

// Computed properties
const filteredDivisions = computed(() => {
  if (!searchFilters.customerId) {
    return divisions.value
  }
  return divisions.value.filter((d) => d.customer_id === searchFilters.customerId)
})

// Table columns configuration
const tableColumns = [
  {
    title: 'id',
    dataIndex: 'id',
    key: 'id',
    width: 80,
    sorter: true,
    fixed: 'left' as const,
  },
  {
    title: 'customer_name',
    dataIndex: 'customer_name',
    key: 'customer_name',
    width: 150,
    fixed: 'left' as const,
  },
  {
    title: 'division_name',
    dataIndex: 'division_name',
    key: 'division_name',
    width: 120,
  },
  {
    title: 'avr_as_num',
    dataIndex: 'avr_as_num',
    key: 'avr_as_num',
    width: 150,
  },
  {
    title: 'avr_asln_num',
    dataIndex: 'avr_asln_num',
    key: 'avr_asln_num',
    width: 120,
    align: 'right' as const,
  },
  {
    title: 'avr_assq_num',
    dataIndex: 'avr_assq_num',
    key: 'avr_assq_num',
    width: 120,
    align: 'right' as const,
  },
  {
    title: 'avh_arv_ymd',
    dataIndex: 'avh_arv_ymd',
    key: 'avh_arv_ymd',
    width: 120,
  },
  {
    title: 'avr_prod_cod',
    dataIndex: 'avr_prod_cod',
    key: 'avr_prod_cod',
    width: 120,
    align: 'right' as const,
  },
  {
    title: 'avd_prod_nam',
    dataIndex: 'avd_prod_nam',
    key: 'avd_prod_nam',
    width: 200,
    ellipsis: true,
  },
  {
    title: 'avr_rtpc_qty',
    dataIndex: 'avr_rtpc_qty',
    key: 'avr_rtpc_qty',
    width: 100,
    align: 'right' as const,
  },
  {
    title: 'prod_ppc_num',
    dataIndex: 'prod_ppc_num',
    key: 'prod_ppc_num',
    width: 120,
    align: 'right' as const,
  },
  {
    title: 'prod_hrc1',
    dataIndex: 'prod_hrc1',
    key: 'prod_hrc1',
    width: 100,
  },
  {
    title: 'ai',
    dataIndex: 'ai',
    key: 'ai',
    width: 80,
    align: 'right' as const,
  },
  {
    title: 'inner_master',
    dataIndex: 'inner_master',
    key: 'inner_master',
    width: 120,
    align: 'right' as const,
  },
  {
    title: 'carton',
    dataIndex: 'carton',
    key: 'carton',
    width: 100,
    align: 'right' as const,
  },
  {
    title: 'pcs',
    dataIndex: 'pcs',
    key: 'pcs',
    width: 100,
    align: 'right' as const,
  },
  {
    title: 'inner_carton',
    dataIndex: 'inner_carton',
    key: 'inner_carton',
    width: 120,
    align: 'right' as const,
  },
  {
    title: 'inner_pcs',
    dataIndex: 'inner_pcs',
    key: 'inner_pcs',
    width: 120,
    align: 'right' as const,
  },
  {
    title: 'total_carton',
    dataIndex: 'total_carton',
    key: 'total_carton',
    width: 120,
    align: 'right' as const,
  },
  {
    title: 'total_pcs',
    dataIndex: 'total_pcs',
    key: 'total_pcs',
    width: 120,
    align: 'right' as const,
  },
  {
    title: 'total_ai',
    dataIndex: 'total_ai',
    key: 'total_ai',
    width: 100,
    align: 'right' as const,
  },
  {
    title: 'total_m3',
    dataIndex: 'total_m3',
    key: 'total_m3',
    width: 100,
    align: 'right' as const,
  },
  {
    title: 'created_at',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 150,
  },
  {
    title: 'actions',
    key: 'actions',
    width: 150,
    fixed: 'right' as const,
  },
]

// Initialize component
onMounted(async () => {
  await initializeData()
})

// Initialize data
const initializeData = async () => {
  try {
    isLoading.value = true
    await initializeDatabase()
    await loadCustomers()
    await loadDivisions()
    await searchData() // Load initial data
  } catch (error) {
    console.error('Error initializing data:', error)
    errorMessage.value = 'Failed to initialize data'
  } finally {
    isLoading.value = false
  }
}

// Load customers for dropdown
const loadCustomers = async () => {
  try {
    customers.value = await loadCustomersService()
  } catch (error) {
    console.error('Error loading customers:', error)
    message.error('Failed to load customers')
  }
}

// Load divisions for dropdown
const loadDivisions = async (customerId?: string) => {
  try {
    divisions.value = await loadDivisionsService(customerId)
  } catch (error) {
    console.error('Error loading divisions:', error)
    message.error('Failed to load divisions')
  }
}

// Search data with filters
const searchData = async () => {
  try {
    isLoading.value = true
    errorMessage.value = ''

    const paginationParams: PaginationParams = {
      current: pagination.current,
      pageSize: pagination.pageSize,
    }

    const result = await searchInboundData(searchFilters, paginationParams)

    inboundData.value = result.data
    pagination.total = result.total
    paginationConfig.total = result.total
    paginationConfig.current = pagination.current

    hasSearched.value = true

    if (inboundData.value.length === 0 && hasSearched.value) {
      message.info('No records found matching your criteria')
    }
  } catch (error) {
    console.error('Error searching data:', error)
    errorMessage.value =
      'Failed to search data: ' + (error instanceof Error ? error.message : String(error))
    message.error('Failed to search data')
  } finally {
    isLoading.value = false
  }
}

// Event handlers
const handleCustomerChange = async () => {
  // Clear division when customer changes
  searchFilters.divisionId = null
  // Load divisions for selected customer
  if (searchFilters.customerId) {
    await loadDivisions(searchFilters.customerId)
  } else {
    await loadDivisions()
  }
  searchData()
}

const handleDivisionChange = () => {
  searchData()
}

const handleMonthChange = () => {
  searchData()
}

const clearFilters = async () => {
  searchFilters.customerId = null
  searchFilters.divisionId = null
  searchFilters.month = null
  pagination.current = 1
  paginationConfig.current = 1
  await loadDivisions() // Load all divisions when clearing filters
  searchData()
}

const refreshData = () => {
  searchData()
}

// Define types for table change event
interface TableChangeParams {
  current?: number
  pageSize?: number
  total?: number
}

const handleTableChange = (pag: TableChangeParams) => {
  pagination.current = pag.current || 1
  pagination.pageSize = pag.pageSize || 20
  paginationConfig.current = pag.current || 1
  paginationConfig.pageSize = pag.pageSize || 20
  searchData()
}

// View record details
const viewRecord = (record: InboundRecord) => {
  selectedRecord.value = record
  showDetailModal.value = true
}

// Delete record
const deleteRecord = (record: InboundRecord) => {
  recordToDelete.value = record
  showDeleteModal.value = true
}

const confirmDelete = async () => {
  if (!recordToDelete.value) return

  try {
    isLoading.value = true
    await deleteInboundRecord(recordToDelete.value.id)
    message.success('Record deleted successfully')
    showDeleteModal.value = false
    recordToDelete.value = null
    await searchData() // Refresh data
  } catch (error) {
    console.error('Error deleting record:', error)
    message.error('Failed to delete record')
  } finally {
    isLoading.value = false
  }
}

const cancelDelete = () => {
  showDeleteModal.value = false
  recordToDelete.value = null
}

// Export data
const exportData = () => {
  // Implementation for export functionality
  message.info('Export functionality will be implemented')
}

// Navigate to Import Inbound page
const goToImportPage = () => {
  console.log('goToImportPage called in InboundListView')
  const query: { customerId?: string; divisionId?: string } = {}
  if (searchFilters.customerId) {
    query.customerId = searchFilters.customerId
  }
  if (searchFilters.divisionId) {
    query.divisionId = searchFilters.divisionId
  }
  console.log('Navigating to Import Inbound with query:', query)
  router
    .push({ name: 'Import Inbound', query })
    .then(() => {
      console.log('Navigation to Import Inbound successful')
    })
    .catch((error) => {
      console.error('Navigation to Import Inbound failed:', error)
    })
}

// Define type for select option
interface SelectOption {
  value: string
  label?: string
}

// Filter function for customer dropdown
const filterCustomerOption = (input: string, option: SelectOption) => {
  const customer = customers.value.find((c) => c.id === option.value)
  if (!customer) return false

  return customer.name.toLowerCase().includes(input.toLowerCase())
}

// Filter function for division dropdown
const filterDivisionOption = (input: string, option: SelectOption) => {
  const division = divisions.value.find((d) => d.id === option.value)
  if (!division) return false

  return division.name.toLowerCase().includes(input.toLowerCase())
}

// Utility functions
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    })
  } catch {
    return dateString
  }
}

const formatDateTime = (dateString: string) => {
  if (!dateString) return '-'
  try {
    const date = new Date(dateString)
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch {
    return dateString
  }
}

const toggleFilter = () => {
  showFilter.value = !showFilter.value
}
</script>

<style scoped>
.inbound-list-view {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header-text h2 {
  margin: 0;
  color: #1a1a1a;
}

.header-text p {
  margin: 4px 0 0 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.search-section {
  margin-bottom: 24px;
}

.search-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.results-summary {
  margin-bottom: 16px;
}

.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.record-detail .ant-descriptions {
  margin-top: 16px;
}

.inbound-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0px;
  border-bottom: 1px solid #f2f2f2;
  margin-bottom: 5px;
}

.inbound-header .ant-space {
  color: #16085a !important;
}
</style>
