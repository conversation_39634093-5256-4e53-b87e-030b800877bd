<template>
  <div class="header-container-master">
    <a-spin :spinning="loading" class="content-wrapper">
      <!-- Page Header -->
      <PageHeader title="Company Profile" sub-title="Manage information of your company">
        <template #extra>
          <a-button type="primary" @click="handleSave" :loading="saving" :disabled="!canSave">
            <template #icon>
              <SaveOutlined />
            </template>
            Save Changes
          </a-button>
        </template>
      </PageHeader>
      <!-- Company Information Section -->
      <div class="profile-section company-info-section">
        <div class="section-header">
          <h2>
            Company Information
            <a-button
              type="text"
              size="small"
              @click="showCompanyInfoModal = true"
              class="edit-icon-btn"
            >
              <template #icon>
                <EditOutlined />
              </template>
            </a-button>
          </h2>
        </div>

        <div class="company-info-display">
          <div class="info-row">
            <div class="info-left">
              <div class="info-item">
                <label>Company Name:</label>
                <span>{{ companyInfo.name || 'N/A' }}</span>
              </div>
              <div class="info-divider"></div>
              <div class="info-item">
                <label>Tax Code:</label>
                <span>{{ companyInfo.taxCode || 'N/A' }}</span>
              </div>
              <div class="info-divider"></div>
              <div class="info-item">
                <label>Address:</label>
                <span>{{ companyInfo.address || 'N/A' }}</span>
              </div>
              <div class="info-divider"></div>
              <div class="info-item">
                <label>Phone:</label>
                <span>{{ companyInfo.phone || 'N/A' }}</span>
              </div>
            </div>
            <div class="info-right">
              <!-- Space for future content -->
            </div>
          </div>
        </div>
      </div>

      <!-- Company Information Edit Modal -->
      <a-modal
        v-model:open="showCompanyInfoModal"
        title="Edit Company Information"
        :width="600"
        @ok="handleSaveCompanyInfo"
        @cancel="handleCancelCompanyInfo"
        :confirm-loading="savingCompanyInfo"
      >
        <a-form layout="vertical" class="company-info-modal-form">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item
                label="Company Name"
                :validate-status="modalValidation.name.status"
                :help="modalValidation.name.message"
                required
              >
                <a-input
                  v-model:value="editingCompanyInfo.name"
                  placeholder="Enter company name *"
                  :class="{ 'required-field': !editingCompanyInfo.name }"
                  @blur="validateModalField('name')"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="Tax Code"
                :validate-status="modalValidation.taxCode.status"
                :help="modalValidation.taxCode.message"
                required
              >
                <a-input
                  v-model:value="editingCompanyInfo.taxCode"
                  placeholder="Enter tax code *"
                  :class="{ 'required-field': !editingCompanyInfo.taxCode }"
                  @blur="validateModalField('taxCode')"
                />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item
                label="Address"
                :validate-status="modalValidation.address.status"
                :help="modalValidation.address.message"
                required
              >
                <a-input
                  v-model:value="editingCompanyInfo.address"
                  placeholder="Enter company address *"
                  :class="{ 'required-field': !editingCompanyInfo.address }"
                  @blur="validateModalField('address')"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="Phone"
                :validate-status="modalValidation.phone.status"
                :help="modalValidation.phone.message"
                required
              >
                <a-input
                  v-model:value="editingCompanyInfo.phone"
                  placeholder="Enter phone number *"
                  :class="{ 'required-field': !editingCompanyInfo.phone }"
                  @blur="validateModalField('phone')"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-modal>

      <!-- Company Stores Section -->
      <div class="profile-section">
        <div class="section-header">
          <h2>Company Stores</h2>
          <a-button type="primary" @click="addNewStore" size="small">
            <template #icon>
              <PlusOutlined />
            </template>
            Add Store
          </a-button>
        </div>

        <div class="table-container">
          <a-table
            :columns="storeColumns"
            :data-source="stores"
            :pagination="false"
            :scroll="{ y: 250 }"
            row-key="id"
            size="small"
            bordered
            class="editable-table compact-table"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <a-input
                  v-model:value="record.name"
                  placeholder="Store Name *"
                  :class="{ 'required-field': isStoreFieldRequired(record, 'name') }"
                  @change="markStoreAsEdited(record)"
                />
              </template>
              <template v-else-if="column.key === 'location'">
                <a-input
                  v-model:value="record.location"
                  placeholder="Location *"
                  :class="{ 'required-field': isStoreFieldRequired(record, 'location') }"
                  @change="markStoreAsEdited(record)"
                />
              </template>
              <template v-else-if="column.key === 'manager'">
                <a-input
                  v-model:value="record.manager"
                  placeholder="Manager Name *"
                  :class="{ 'required-field': isStoreFieldRequired(record, 'manager') }"
                  @change="markStoreAsEdited(record)"
                />
              </template>
              <template v-else-if="column.key === 'phone'">
                <a-input
                  v-model:value="record.phone"
                  placeholder="Phone Number *"
                  :class="{ 'required-field': isStoreFieldRequired(record, 'phone') }"
                  @change="markStoreAsEdited(record)"
                />
              </template>
              <template v-else-if="column.key === 'setDefault'">
                <a-radio :checked="record.isDefault" @change="setDefaultStore(record)" />
              </template>
              <template v-else-if="column.key === 'status'">
                <a-switch
                  :checked="record.status === 'active'"
                  size="small"
                  :disabled="record.isDefault"
                  @change="(checked) => toggleStoreStatus(record, checked)"
                />
              </template>
              <template v-else-if="column.key === 'actions'">
                <a-button
                  type="text"
                  danger
                  size="small"
                  @click="deleteStore(record.id)"
                  :disabled="!canDeleteStore(record) || record.isDefault"
                >
                  <template #icon>
                    <DeleteOutlined />
                  </template>
                </a-button>
              </template>
            </template>
          </a-table>
        </div>
      </div>

      <!-- Debit Codes Section -->
      <div class="profile-section">
        <div class="section-header">
          <h2>Debit Codes</h2>
          <a-button type="primary" @click="addNewDebitCode" size="small">
            <template #icon>
              <PlusOutlined />
            </template>
            Add Debit Code
          </a-button>
        </div>

        <div class="table-container">
          <a-table
            :columns="debitCodeColumns"
            :data-source="debitCodes"
            :pagination="false"
            :scroll="{ y: 350, x: 'max-content' }"
            row-key="id"
            size="small"
            bordered
            class="editable-table compact-table debit-codes-table"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'code'">
                <a-input
                  v-model:value="record.code"
                  placeholder="Debit Code *"
                  :class="{ 'required-field': isDebitCodeFieldRequired(record, 'code') }"
                  @change="markDebitCodeAsEdited(record)"
                />
              </template>
              <template v-else-if="column.key === 'name'">
                <a-input
                  v-model:value="record.name"
                  placeholder="Name *"
                  :class="{ 'required-field': isDebitCodeFieldRequired(record, 'name') }"
                  @change="markDebitCodeAsEdited(record)"
                />
              </template>
              <template v-else-if="column.key === 'description'">
                <a-input
                  v-model:value="record.description"
                  placeholder="Description *"
                  :class="{ 'required-field': isDebitCodeFieldRequired(record, 'description') }"
                  @change="markDebitCodeAsEdited(record)"
                />
              </template>
              <template v-else-if="column.key === 'isActive'">
                <a-switch
                  v-model:checked="record.isActive"
                  size="small"
                  @change="markDebitCodeAsEdited(record)"
                />
              </template>
              <template v-else-if="column.key === 'actions'">
                <a-button
                  v-if="record.isUsed"
                  type="text"
                  size="small"
                  @click="toggleDebitCodeStatus(record)"
                  :title="record.isActive ? 'Deactivate' : 'Activate'"
                >
                  <template #icon>
                    <CheckOutlined v-if="!record.isActive" />
                    <StopOutlined v-else />
                  </template>
                </a-button>
                <a-button
                  v-else
                  type="text"
                  danger
                  size="small"
                  @click="deleteDebitCode(record.id)"
                  :disabled="!canDeleteDebitCode(record)"
                >
                  <template #icon>
                    <DeleteOutlined />
                  </template>
                </a-button>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { message, PageHeader } from 'ant-design-vue'
import {
  SaveOutlined,
  DeleteOutlined,
  CheckOutlined,
  StopOutlined,
  PlusOutlined,
  EditOutlined,
} from '@ant-design/icons-vue'
import type { TableColumnsType } from 'ant-design-vue'
import type { CompanyProfile, CompanyStore, DebitCode } from '../types/CompanyProfile'
import {
  getCompanyProfile,
  updateCompanyProfile,
  updateCompanyInfo,
  addCompanyStore,
  updateCompanyStore,
  deleteCompanyStore as deleteStoreService,
  addDebitCode,
  updateDebitCode,
  deleteDebitCode as deleteDebitCodeService,
  createTempCompanyStore,
  createTempDebitCode,
  initializeCompanyProfileDB,
} from '../services/master-data/companyProfile/companyProfileService'
import { useDefaultStore } from '../stores/useDefaultStore'

// Reactive data
const loading = ref(false)
const saving = ref(false)
const companyProfile = ref<CompanyProfile | null>(null)
const companyInfo = reactive({
  name: '',
  address: '',
  phone: '',
  taxCode: '',
})

const originalCompanyInfo = reactive({
  name: '',
  address: '',
  phone: '',
  taxCode: '',
})
const stores = ref<CompanyStore[]>([])
const debitCodes = ref<DebitCode[]>([])
const editedStores = ref<Set<number>>(new Set())
const editedDebitCodes = ref<Set<number>>(new Set())
const newStores = ref<Set<number>>(new Set()) // Track new stores (negative IDs)
const newDebitCodes = ref<Set<number>>(new Set()) // Track new debit codes (negative IDs)
const companyInfoChanged = ref(false) // Track company info changes

// Default store composable
const { updateDefaultStoreName } = useDefaultStore()

// Computed properties
const hasChanges = computed(() => {
  return (
    editedStores.value.size > 0 ||
    editedDebitCodes.value.size > 0 ||
    newStores.value.size > 0 ||
    newDebitCodes.value.size > 0 ||
    companyInfoChanged.value
  )
})

const canSave = computed(() => {
  if (!hasChanges.value) return false

  // Validate new stores (temp IDs are >= 999999)
  for (const storeId of newStores.value) {
    const store = stores.value.find((s) => s.id === storeId)
    if (store && (!store.name || !store.location || !store.manager || !store.phone)) {
      return false
    }
  }

  // Validate edited stores
  for (const storeId of editedStores.value) {
    const store = stores.value.find((s) => s.id === storeId)
    if (store && (!store.name || !store.location || !store.manager || !store.phone)) {
      return false
    }
  }

  // Validate new debit codes (temp IDs are >= 999999)
  for (const codeId of newDebitCodes.value) {
    const code = debitCodes.value.find((c) => c.id === codeId)
    if (code && (!code.code || !code.name || !code.description)) {
      return false
    }
  }

  // Validate edited debit codes
  for (const codeId of editedDebitCodes.value) {
    const code = debitCodes.value.find((c) => c.id === codeId)
    if (code && (!code.code || !code.name || !code.description)) {
      return false
    }
  }

  return true
})

// Helper functions for field validation
const isStoreFieldRequired = (store: CompanyStore, field: string) => {
  // Field is required if store is new or edited
  const isNewStore = newStores.value.has(store.id)
  const isEditedStore = editedStores.value.has(store.id)

  if (isNewStore || isEditedStore) {
    const value = store[field as keyof CompanyStore] as string
    return !value || value.trim() === ''
  }
  return false
}

const isDebitCodeFieldRequired = (code: DebitCode, field: string) => {
  // Field is required if code is new or edited
  const isNewCode = newDebitCodes.value.has(code.id)
  const isEditedCode = editedDebitCodes.value.has(code.id)

  if (isNewCode || isEditedCode) {
    const value = code[field as keyof DebitCode] as string
    return !value || value.trim() === ''
  }
  return false
}

// Modal states
const showCompanyInfoModal = ref(false)
const savingCompanyInfo = ref(false)
const editingCompanyInfo = reactive({
  name: '',
  address: '',
  phone: '',
  taxCode: '',
})

// Validation
const validation = reactive({
  name: { status: '', message: '' },
  taxCode: { status: '', message: '' },
  address: { status: '', message: '' },
  phone: { status: '', message: '' },
})

const modalValidation = reactive({
  name: { status: '', message: '' },
  taxCode: { status: '', message: '' },
  address: { status: '', message: '' },
  phone: { status: '', message: '' },
})

// Store columns
const storeColumns: TableColumnsType = [
  {
    title: '#',
    dataIndex: 'id',
    key: 'id',
    width: 50,
    align: 'center',
    customRender: ({ record }: { record: CompanyStore }) => {
      return record.id >= 999999 ? 'New' : record.id
    },
  },
  {
    title: 'Warehouse Name',
    key: 'name',
    width: 150,
  },
  {
    title: 'Location',
    key: 'location',
    width: 150,
  },
  {
    title: 'Manager',
    key: 'manager',
    width: 120,
  },
  {
    title: 'Phone',
    key: 'phone',
    width: 120,
  },
  {
    title: 'Default',
    key: 'setDefault',
    width: 80,
    align: 'center',
  },
  {
    title: 'Status',
    key: 'status',
    width: 80,
    align: 'center',
  },
  {
    title: 'Actions',
    key: 'actions',
    width: 80,
    align: 'center',
  },
]

// Debit code columns
const debitCodeColumns: TableColumnsType = [
  {
    title: '#',
    dataIndex: 'id',
    key: 'id',
    width: 50,
    align: 'center',
    customRender: ({ record }: { record: DebitCode }) => {
      return record.id >= 999999 ? 'New' : record.id
    },
  },
  {
    title: 'Code',
    key: 'code',
    width: 120,
  },
  {
    title: 'Name',
    key: 'name',
    width: 150,
  },
  {
    title: 'Description',
    key: 'description',
    width: 250,
  },
  {
    title: 'Active',
    key: 'isActive',
    width: 80,
    align: 'center',
  },
  {
    title: 'Actions',
    key: 'actions',
    width: 80,
    align: 'center',
  },
]

// Validation methods
const validateField = (field: string) => {
  const value = companyInfo[field as keyof typeof companyInfo]

  switch (field) {
    case 'name':
      if (!value.trim()) {
        validation.name = { status: 'error', message: 'Company name is required' }
      } else {
        validation.name = { status: 'success', message: '' }
      }
      break
    case 'taxCode':
      if (!value.trim()) {
        validation.taxCode = { status: 'error', message: 'Tax code is required' }
      } else if (!/^\d{10,13}$/.test(value.trim())) {
        validation.taxCode = { status: 'error', message: 'Tax code must be 10-13 digits' }
      } else {
        validation.taxCode = { status: 'success', message: '' }
      }
      break
    case 'address':
      if (!value.trim()) {
        validation.address = { status: 'error', message: 'Address is required' }
      } else {
        validation.address = { status: 'success', message: '' }
      }
      break
    case 'phone':
      if (!value.trim()) {
        validation.phone = { status: 'error', message: 'Phone is required' }
      } else if (!/^[\+]?[0-9\s\-\(\)]{10,15}$/.test(value.trim())) {
        validation.phone = { status: 'error', message: 'Invalid phone number format' }
      } else {
        validation.phone = { status: 'success', message: '' }
      }
      break
  }
}

const validateModalField = (field: string) => {
  const value = editingCompanyInfo[field as keyof typeof editingCompanyInfo]

  switch (field) {
    case 'name':
      if (!value.trim()) {
        modalValidation.name = { status: 'error', message: 'Company name is required' }
      } else {
        modalValidation.name = { status: 'success', message: '' }
      }
      break
    case 'taxCode':
      if (!value.trim()) {
        modalValidation.taxCode = { status: 'error', message: 'Tax code is required' }
      } else if (!/^\d{10,13}$/.test(value.trim())) {
        modalValidation.taxCode = { status: 'error', message: 'Tax code must be 10-13 digits' }
      } else {
        modalValidation.taxCode = { status: 'success', message: '' }
      }
      break
    case 'address':
      if (!value.trim()) {
        modalValidation.address = { status: 'error', message: 'Address is required' }
      } else {
        modalValidation.address = { status: 'success', message: '' }
      }
      break
    case 'phone':
      if (!value.trim()) {
        modalValidation.phone = { status: 'error', message: 'Phone is required' }
      } else if (!/^[\+]?[0-9\s\-\(\)]{10,15}$/.test(value.trim())) {
        modalValidation.phone = { status: 'error', message: 'Invalid phone number format' }
      } else {
        modalValidation.phone = { status: 'success', message: '' }
      }
      break
  }
}

// Helper methods
const setDefaultStore = (store: CompanyStore) => {
  // Set all stores to non-default first
  stores.value.forEach((s) => {
    s.isDefault = false
  })
  // Set selected store as default
  store.isDefault = true
  markStoreAsEdited(store)
}

const toggleStoreStatus = (store: CompanyStore, checked: boolean) => {
  store.status = checked ? 'active' : 'inactive'
  markStoreAsEdited(store)
}

const handleSaveCompanyInfo = async () => {
  try {
    savingCompanyInfo.value = true

    // Validate all fields
    validateModalField('name')
    validateModalField('taxCode')
    validateModalField('address')
    validateModalField('phone')

    // Check if there are validation errors
    const hasErrors = Object.values(modalValidation).some((field) => field.status === 'error')
    if (hasErrors) {
      message.error('Please fix validation errors before saving')
      return
    }

    // Update company info
    Object.assign(companyInfo, editingCompanyInfo)
    showCompanyInfoModal.value = false
    message.success('Company information updated successfully')
  } catch (error) {
    message.error('Failed to update company information')
    console.error('Error updating company info:', error)
  } finally {
    savingCompanyInfo.value = false
  }
}

const handleCancelCompanyInfo = () => {
  showCompanyInfoModal.value = false
  // Reset editing data
  Object.assign(editingCompanyInfo, companyInfo)
  // Reset validation
  Object.keys(modalValidation).forEach((key) => {
    modalValidation[key as keyof typeof modalValidation] = { status: '', message: '' }
  })
}

// Methods
const loadCompanyProfile = async () => {
  try {
    loading.value = true
    const profile = await getCompanyProfile()
    companyProfile.value = profile

    // Load company info
    Object.assign(companyInfo, profile.companyInfo)
    Object.assign(originalCompanyInfo, profile.companyInfo)
    Object.assign(editingCompanyInfo, profile.companyInfo)

    stores.value = [...profile.stores]
    debitCodes.value = [...profile.debitCodes]

    // Reset change tracking
    companyInfoChanged.value = false

    // Update default store name in header
    const defaultStore = stores.value.find((store) => store.isDefault)
    if (defaultStore && defaultStore.name) {
      updateDefaultStoreName(defaultStore.name)
    }
  } catch (error) {
    message.error('Failed to load company profile')
    console.error('Error loading company profile:', error)
  } finally {
    loading.value = false
  }
}

const markStoreAsEdited = (store: CompanyStore) => {
  // Only mark as edited if it's not a new store (temp IDs are >= 999999)
  if (store.id < 999999) {
    editedStores.value.add(store.id)
  }
}

const markDebitCodeAsEdited = (code: DebitCode) => {
  // Only mark as edited if it's not a new code (temp IDs are >= 999999)
  if (code.id < 999999) {
    editedDebitCodes.value.add(code.id)
  }
}

const canDeleteStore = (store: CompanyStore) => {
  // Cannot delete if store is default
  return !store.isDefault
}

const addNewStore = () => {
  const newStore: Omit<CompanyStore, 'id'> = {
    name: '',
    location: '',
    manager: '',
    phone: '',
    status: 'active',
    isDefault: false,
  }
  const tempStore = createTempCompanyStore(newStore)
  stores.value.unshift(tempStore) // Add to top instead of bottom

  // Track as new store
  newStores.value.add(tempStore.id)

  message.success('New store added. Click Save Changes to persist.')
}

const addNewDebitCode = () => {
  const newCode: Omit<DebitCode, 'id'> = {
    code: '',
    name: '',
    description: '',
    isActive: true,
    isUsed: false,
  }
  const tempCode = createTempDebitCode(newCode)
  debitCodes.value.unshift(tempCode) // Add to top instead of bottom

  // Track as new debit code
  newDebitCodes.value.add(tempCode.id)

  message.success('New debit code added. Click Save Changes to persist.')
}

const canDeleteDebitCode = (code: DebitCode) => {
  // Cannot delete if code is used
  return !code.isUsed
}

const toggleDebitCodeStatus = (code: DebitCode) => {
  code.isActive = !code.isActive
  markDebitCodeAsEdited(code)
}

const deleteStore = async (id: number) => {
  try {
    // If it's a temp store (ID >= 999999), just remove from UI
    if (id >= 999999) {
      stores.value = stores.value.filter((s) => s.id !== id)
      newStores.value.delete(id)
      message.success('Store removed successfully')
    } else {
      // If it's a real store, delete from database
      await deleteStoreService(id)
      stores.value = stores.value.filter((s) => s.id !== id)
      editedStores.value.delete(id)
      message.success('Store deleted successfully')
    }
  } catch (error) {
    message.error('Failed to delete store')
    console.error('Error deleting store:', error)
  }
}

const deleteDebitCode = async (id: number) => {
  try {
    // If it's a temp code (ID >= 999999), just remove from UI
    if (id >= 999999) {
      debitCodes.value = debitCodes.value.filter((c) => c.id !== id)
      newDebitCodes.value.delete(id)
      message.success('Debit code removed successfully')
    } else {
      // If it's a real code, delete from database
      await deleteDebitCodeService(id)
      debitCodes.value = debitCodes.value.filter((c) => c.id !== id)
      editedDebitCodes.value.delete(id)
      message.success('Debit code deleted successfully')
    }
  } catch (error) {
    message.error('Failed to delete debit code')
    console.error('Error deleting debit code:', error)
  }
}

const handleSave = async () => {
  try {
    saving.value = true

    // Validate all fields
    validateField('name')
    validateField('taxCode')
    validateField('address')
    validateField('phone')

    // Check if there are validation errors
    const hasErrors = Object.values(validation).some((field) => field.status === 'error')
    if (hasErrors) {
      message.error('Please fix validation errors before saving')
      return
    }

    // Validate new stores
    for (const storeId of newStores.value) {
      const store = stores.value.find((s) => s.id === storeId)
      if (store && (!store.name || !store.location || !store.manager || !store.phone)) {
        message.error('Please fill all required fields for new stores')
        return
      }
    }

    // Validate edited stores
    for (const storeId of editedStores.value) {
      const store = stores.value.find((s) => s.id === storeId)
      if (store && (!store.name || !store.location || !store.manager || !store.phone)) {
        message.error('Please fill all required fields for edited stores')
        return
      }
    }

    // Validate new debit codes
    for (const codeId of newDebitCodes.value) {
      const code = debitCodes.value.find((c) => c.id === codeId)
      if (code && (!code.code || !code.name || !code.description)) {
        message.error('Please fill all required fields for new debit codes')
        return
      }
    }

    // Validate edited debit codes
    for (const codeId of editedDebitCodes.value) {
      const code = debitCodes.value.find((c) => c.id === codeId)
      if (code && (!code.code || !code.name || !code.description)) {
        message.error('Please fill all required fields for edited debit codes')
        return
      }
    }

    if (!companyProfile.value) {
      message.error('No company profile to save')
      return
    }

    // Add new stores to database
    for (const storeId of newStores.value) {
      const store = stores.value.find((s) => s.id === storeId)
      if (store) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { id, ...storeData } = store // Remove temp ID
        const newStore = await addCompanyStore(storeData)
        // Replace temp store with real store
        const index = stores.value.findIndex((s) => s.id === storeId)
        if (index !== -1) {
          stores.value[index] = newStore
        }
      }
    }

    // Add new debit codes to database
    for (const codeId of newDebitCodes.value) {
      const code = debitCodes.value.find((c) => c.id === codeId)
      if (code) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { id, ...codeData } = code // Remove temp ID
        const newCode = await addDebitCode(codeData)
        // Replace temp code with real code
        const index = debitCodes.value.findIndex((c) => c.id === codeId)
        if (index !== -1) {
          debitCodes.value[index] = newCode
        }
      }
    }

    // Update edited stores individually
    for (const storeId of editedStores.value) {
      const store = stores.value.find((s) => s.id === storeId)
      if (store) {
        await updateCompanyStore(store)
      }
    }

    // Update edited debit codes individually
    for (const codeId of editedDebitCodes.value) {
      const code = debitCodes.value.find((c) => c.id === codeId)
      if (code) {
        await updateDebitCode(code)
      }
    }

    // Update company info if changed
    if (companyInfoChanged.value) {
      await updateCompanyInfo(companyInfo)
    }

    // Update company profile
    const updatedProfile: CompanyProfile = {
      ...companyProfile.value,
      companyInfo: { ...companyInfo },
      stores: stores.value,
      debitCodes: debitCodes.value,
    }

    await updateCompanyProfile(updatedProfile)

    // Reload all data to ensure consistency
    await loadCompanyProfile()

    // Clear all tracking sets
    editedStores.value.clear()
    editedDebitCodes.value.clear()
    newStores.value.clear()
    newDebitCodes.value.clear()
    companyInfoChanged.value = false

    message.success('Company profile saved successfully')
  } catch (error) {
    message.error('Failed to save company profile')
    console.error('Error saving company profile:', error)
  } finally {
    saving.value = false
  }
}

// Watch for company info changes
watch(
  () => ({ ...companyInfo }),
  (newInfo) => {
    const hasChanged = Object.keys(newInfo).some((key) => {
      return (
        newInfo[key as keyof typeof newInfo] !==
        originalCompanyInfo[key as keyof typeof originalCompanyInfo]
      )
    })
    companyInfoChanged.value = hasChanged
  },
  { deep: true },
)

onMounted(async () => {
  await initializeCompanyProfileDB()
  loadCompanyProfile()
})
</script>

<style scoped>
.content-wrapper {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px;
}

.content-wrapper :deep(.ant-spin-container) {
  height: 100%;
}

.content-wrapper::-webkit-scrollbar {
  width: 6px;
}

.content-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.content-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.content-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.profile-section {
  margin-bottom: 10px;
  background: #fafafa;
  border-radius: 8px;
  padding: 8px 20px;
  border: 1px solid #f0f0f0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  margin-bottom: 4px;
}

.section-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  position: relative;
  padding-bottom: 8px;
  display: inline-block;
}

.company-info-section {
  margin-bottom: 20px;
}

.debit-codes-table :deep(.ant-table-body) {
  padding-bottom: 20px; /* Add padding bottom to table body */
}

.debit-codes-table :deep(.ant-table-tbody) {
  padding-bottom: 20px; /* Add padding bottom to table tbody */
}

.company-info-form {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.company-info-form :deep(.ant-form-item) {
  margin-bottom: 20px;
}

.company-info-form :deep(.ant-form-item-label) {
  font-weight: 600;
  color: #262626;
  font-size: 14px;
  margin-bottom: 8px;
}

.company-info-form :deep(.ant-input) {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  padding: 8px 12px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.company-info-form :deep(.ant-input:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.company-info-form :deep(.ant-form-item-has-error .ant-input) {
  border-color: #ff4d4f;
}

.company-info-form :deep(.ant-form-item-has-success .ant-input) {
  border-color: #52c41a;
}

.company-info-form :deep(.ant-form-item-explain) {
  font-size: 12px;
  margin-top: 4px;
}

.edit-icon-btn {
  margin-left: 8px;
  color: #1890ff;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.edit-icon-btn:hover {
  opacity: 1;
  color: #40a9ff;
}

.company-info-display {
  background: #fff;
  border-radius: 8px;
  padding: 20px 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.info-left {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.info-right {
  flex-shrink: 0;
  min-width: 100px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
}

.info-item label {
  font-weight: 600;
  color: #262626;
  font-size: 14px;
  margin: 0;
}

.info-item span {
  color: #595959;
  font-size: 14px;
}

.info-divider {
  width: 1px;
  height: 20px;
  background-color: #e8e8e8;
  margin: 0 4px;
}

.company-info-modal-form :deep(.ant-form-item) {
  margin-bottom: 16px;
}

.company-info-modal-form :deep(.ant-form-item-label) {
  font-weight: 600;
  color: #262626;
  font-size: 14px;
}

.debit-codes-display {
  min-height: 32px;
  width: 100%;
}

.no-codes {
  width: 100%;
}

.selected-codes {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
  max-height: 60px;
  overflow-y: auto;
}

.selected-codes.scrollable {
  max-height: 60px;
  overflow-y: auto;
  padding-right: 4px;
}

.selected-codes.scrollable::-webkit-scrollbar {
  width: 4px;
}

.selected-codes.scrollable::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.selected-codes.scrollable::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.selected-codes.scrollable::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.code-tag {
  margin: 0;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.add-more-btn {
  padding: 0 4px;
  height: 22px;
  font-size: 12px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
}

.debit-code-selector {
  max-height: 400px;
  overflow-y: auto;
}

.code-checkbox-group {
  width: 100%;
}

.code-checkbox-item {
  display: block;
  width: 100%;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.code-checkbox-item:last-child {
  border-bottom: none;
}

.code-info {
  display: flex;
  flex-direction: column;
  margin-left: 8px;
}

.code-name {
  font-weight: 600;
  color: #262626;
  font-size: 14px;
}

.code-desc {
  color: #8c8c8c;
  font-size: 12px;
  margin-top: 2px;
}

.table-container {
  background: #fff;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px; /* Add margin for better spacing */
}

.scrollable-table {
  max-height: 500px;
  overflow: auto;
}

.scrollable-table :deep(.ant-table-body) {
  overflow-y: auto;
  overflow-x: auto;
}

.editable-table {
  border-radius: 6px;
}

.compact-table :deep(.ant-table-thead > tr > th) {
  padding: 8px 6px;
  font-size: 13px;
}

.compact-table :deep(.ant-table-tbody > tr > td) {
  padding: 6px;
  font-size: 13px;
}

.compact-table :deep(.ant-input) {
  padding: 4px 6px;
  font-size: 13px;
}

.compact-table :deep(.ant-select-selector) {
  padding: 2px 6px;
}

.compact-table :deep(.ant-switch) {
  min-width: 36px;
  height: 18px;
}

.compact-table :deep(.ant-radio) {
  margin: 0;
}

/* Required field styling */
.required-field {
  border-color: #ff7875 !important;
  background-color: #fff2f0 !important;
}

.required-field:focus {
  border-color: #ff4d4f !important;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
}

.required-field::placeholder {
  color: #ff7875 !important;
}

/* Table styling */
:deep(.ant-table) {
  font-size: 13px;
}

:deep(.ant-table-thead > tr > th) {
  background: #f8f9fa;
  font-weight: 600;
  color: #262626;
  border-bottom: 2px solid #e9ecef;
  padding: 12px 8px;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

/* Input styling */
:deep(.ant-input) {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 13px;
  padding: 4px 8px;
}

:deep(.ant-input:focus) {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

:deep(.ant-input-number) {
  width: 100%;
  font-size: 13px;
}

:deep(.ant-select) {
  font-size: 13px;
}

:deep(.ant-select-selector) {
  border-radius: 4px;
  padding: 0 8px;
}

/* Button styling */
:deep(.ant-btn-sm) {
  font-size: 12px;
  height: 28px;
  padding: 0 12px;
}

/* Checkbox styling */
:deep(.ant-checkbox-wrapper) {
  display: flex;
  justify-content: center;
}

/* Action buttons */
:deep(.ant-btn-text.ant-btn-dangerous) {
  color: #ff4d4f;
}

:deep(.ant-btn-text.ant-btn-dangerous:hover) {
  color: #ff7875;
  background: rgba(255, 77, 79, 0.1);
}

/* Responsive design */
@media (max-width: 1200px) {
  .table-container {
    overflow-x: auto;
  }

  :deep(.ant-table) {
    min-width: 800px;
  }
}
</style>
