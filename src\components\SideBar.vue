<script setup lang="ts">
import { h, reactive, ref, computed, watchEffect } from 'vue'
import { theme } from 'ant-design-vue'
import type { MenuMode, MenuTheme, ItemType } from 'ant-design-vue'
import Logo from '@IVC/assets/logo.png'
import LogoNittsu from '@IVC/assets/logo_nittsu.svg'
import router from '@IVC/router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const collapsed = ref<boolean>(false)
const authStore = useAuthStore()

const { defaultAlgorithm, defaultSeed } = theme
const mapToken = defaultAlgorithm(defaultSeed)

const state = reactive({
  mode: 'inline' as MenuMode,
  theme: 'light' as MenuTheme,
  selectedKeys: [] as string[], // Initialize as empty, will be set by watchEffect
  openKeys: [] as string[], // Consider managing openKeys based on route as well if needed
})

// Check if user has access to route based on role
const hasAccess = (route: RouteRecordRaw): boolean => {
  const requiredRole = route.meta?.requiredRole
  const userRole = authStore.user?.role

  if (!requiredRole) {
    return true // No role requirement
  }

  if (userRole === 'admin') {
    return true // Admin can access everything
  }

  if (userRole === 'manager' && requiredRole !== 'admin') {
    return true // Manager can access manager and user routes
  }

  if (userRole === 'user' && requiredRole === 'user') {
    return true // User can only access user routes
  }

  return false // Access denied
}

const transformRouteToMenuItem = (
  route: RouteRecordRaw,
  basePath: string = '',
): ItemType | null => {
  // Ensure route.name exists, otherwise, this route cannot be a menu item.
  if (!route.name || route.meta?.hidden || !hasAccess(route)) {
    return null
  }

  // After the check above, route.name is guaranteed to be defined.
  const key = route.name.toString()
  const label = route.meta?.title ?? key // Use the guaranteed key if title is missing
  const fullPath = `${basePath}/${route.path}`.replace(/\/\//g, '/')

  let iconComponent
  if (route.meta?.icon) {
    // Assuming route.meta.icon is a valid component or VNode for h()
    // Add 'as any' if TypeScript has trouble inferring the type of route.meta.icon for h()
    iconComponent = h(route.meta.icon)
  }

  const childItems = route.children
    ?.map((childRoute) => transformRouteToMenuItem(childRoute, fullPath))
    .filter((item): item is ItemType => item !== null) // Type guard for clarity

  const hasChildren = childItems && childItems.length > 0

  if (hasChildren) {
    const subMenuItem = {
      // Type inference will handle this
      key,
      label,
      icon: iconComponent,
      children: childItems, // childItems is ItemType[] which is assignable
    }
    return subMenuItem
  } else {
    const menuItem = {
      // Type inference will handle this
      key,
      label,
      icon: iconComponent,
      onClick: () => router.push({ name: key }), // Navigate by name using the derived key
    }
    return menuItem
  }
}

const items = computed(() => {
  const menuItems = router.options.routes
    .map((route: RouteRecordRaw) => transformRouteToMenuItem(route))
    .filter((item: ItemType | null) => item !== null) as ItemType[]

  // Fallback menu items if no items are generated
  if (menuItems.length === 0) {
    const fallbackItems: ItemType[] = [
      {
        key: 'home',
        label: 'Home',
        onClick: () => router.push('/'),
      },
    ]

    // Add User Management for admin
    if (authStore.user?.role === 'admin') {
      fallbackItems.push({
        key: 'users',
        label: 'User Management',
        onClick: () => router.push('/users'),
      })
    }

    return fallbackItems
  }

  return menuItems
})

// Handle menu click
const handleMenuClick = ({ key }: { key: string }) => {
  if (key === 'home') {
    router.push('/')
  } else if (key === 'users') {
    router.push('/users')
  } else if (key === 'simulation') {
    router.push('/simulation')
  }
  // Add more navigation logic as needed
}

// Watch for route changes and update selectedKeys
watchEffect(() => {
  const currentRoute = router.currentRoute.value
  console.log({ currentRoute })
  if (currentRoute.name) {
    // Handle special cases for hidden routes that should highlight their parent menu item
    if (currentRoute.name === 'ProductImport') {
      // ProductImport is hidden, so highlight the Product menu item instead
      state.selectedKeys = ['product']
    } else if (currentRoute.name === 'Import Inbound') {
      state.selectedKeys = ['Inbound List']
    } else if (currentRoute.name === 'Import Outbound') {
      state.selectedKeys = ['Outbound List']
    } else {
      // For all other routes, use the route name as selected key
      state.selectedKeys = [currentRoute.name.toString()]
    }
  }
})
</script>

<template>
  <a-layout-sider
    :style="{
      overflow: 'auto',
      height: 'calc(100vh - 32px)',
      position: 'relative',
      left: '16px',
      top: '16px',
      bottom: 0,
      borderRadius: mapToken.borderRadiusLG + 'px',
    }"
    :theme="state.theme"
    collapsible
    v-model:collapsed="collapsed"
  >
    <div class="logo">
      <img
        :src="collapsed ? Logo : LogoNittsu"
        alt="Application Logo"
        style="max-width: 9rem; width: 100%"
      />
    </div>
    <a-menu
      v-model:openKeys="state.openKeys"
      v-model:selectedKeys="state.selectedKeys"
      :mode="state.mode"
      :items="items"
      :theme="state.theme"
      style="border: none"
      @click="handleMenuClick"
    ></a-menu>
  </a-layout-sider>
</template>

<style scoped>
.logo {
  text-align: center;
  padding: 1.6rem;
}
</style>
