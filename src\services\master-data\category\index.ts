// src/services/master-data/index.ts
// This file will now manage both simple Categories and Workouts.
// Ideally, these would be in separate files (e.g., categoryService.ts and workoutService.ts)

import { useSQLite } from '@IVC/hooks/useSQLite'
import type { Category, Workout, WorkoutCalculationType } from '@IVC/types/MasterDataTypes/Category'
import type { Unit } from '@IVC/types/MasterDataTypes/Unit'

const { executeQuery, tables } = useSQLite()

// --- Category Types and Functions ---
// Category interface is now typically imported from where it's defined, e.g., CategoryView.vue or a central types file.
// For this example, assuming it might be co-located or imported. If it's in CategoryView.vue, direct import might be tricky due to component specifics.

export async function getAllCategories(): Promise<Category[]> {
  // Renamed
  const query = `SELECT id, code, name, status FROM ${tables.categories} ORDER BY name ASC`
  const result = await executeQuery(query)
  if (!result.result.resultRows) {
    return []
  }
  return result.result.resultRows.map((row: unknown[]) => ({
    id: row[0] as number,
    code: row[1] as string,
    name: row[2] as string,
    status: row[3] as Category['status'],
  }))
}

export async function addCategory(category: Omit<Category, 'id'>): Promise<{ id: number }> {
  // Renamed
  const { code, name, status } = category
  await executeQuery(`INSERT INTO ${tables.categories} (code, name, status) VALUES (?, ?, ?)`, [
    code,
    name,
    status || 'active',
  ])
  const idResult = await executeQuery('SELECT last_insert_rowid() as id')
  const firstRow = idResult.result.resultRows?.[0]
  if (firstRow && typeof firstRow[0] === 'number') {
    return { id: firstRow[0] }
  }
  throw new Error('Failed to retrieve ID after category insertion.')
}

export async function updateCategory(category: Category): Promise<void> {
  // Renamed
  const { id, code, name, status } = category
  await executeQuery(
    `UPDATE ${tables.categories} SET code = ?, name = ?, status = ? WHERE id = ?`,
    [code, name, status || 'active', id],
  )
}

export async function deleteCategory(id: number): Promise<void> {
  // Renamed
  await executeQuery(`DELETE FROM ${tables.categories} WHERE id = ?`, [id])
}

export async function getAllWorkouts(): Promise<Workout[]> {
  // Join workout table with units and simple categories
  const query = `
    SELECT w.id, w.code, w.name, w.unit_id, w.unit_price, w.status, w.vat, w.category_id, w.unit_price_is_percentage, w.calculation_type, w.linked_workout_id, w.unit_price_sign, w.unit_price_unit, w.linked_auto_focus_code, w.debit_code_id,
           u.code as unitCode, u.name as unitName,
           sc.name as categoryName,
           lw.name as linkedWorkoutName
    FROM ${tables.workout} w
    LEFT JOIN ${tables.units} u ON w.unit_id = u.id
    LEFT JOIN ${tables.categories} sc ON w.category_id = sc.id
    LEFT JOIN ${tables.workout} lw ON w.linked_workout_id = lw.id
  `
  const result = await executeQuery(query)
  if (!result.result.resultRows) {
    return []
  }
  return result.result.resultRows.map((row: unknown[]) => ({
    id: row[0] as number,
    code: row[1] as string,
    name: row[2] as string,
    unitId: row[3] as number | null,
    unitPrice: row[4] as number | null,
    status: row[5] as Workout['status'],
    vat: row[6] as number,
    categoryId: row[7] as number,
    unitPriceIsPercentage: !!row[8], // Convert 0/1 to false/true
    calculationType: row[9] as WorkoutCalculationType,
    linkedWorkoutId: row[10] as number | null,
    unitPriceSign: row[11] as '+' | '-',
    unitPriceUnit: row[12] as 'VND' | 'PERCENT',
    autoFocusCode: row[13] as string | null, // Map the new column
    debitCodeId: row[14] as number | null, // Map the debit_code_id column
    unitCode: row[15] as string | null,
    unitName: row[16] as string | null,
    categoryName: row[17] as string | null,
    linkedWorkoutName: row[18] as string | null,
  }))
}

export async function getWorkoutsByCategoryId(categoryId: number): Promise<Workout[]> {
  const query = `
    SELECT w.id, w.code, w.name, w.unit_id, w.unit_price, w.status, w.vat, w.category_id, w.unit_price_is_percentage, w.calculation_type, w.linked_workout_id, w.unit_price_sign, w.unit_price_unit, w.linked_auto_focus_code, w.debit_code_id,
           u.code as unitCode, u.name as unitName,
           sc.name as categoryName,
           lw.name as linkedWorkoutName
    FROM ${tables.workout} w
    LEFT JOIN ${tables.units} u ON w.unit_id = u.id
    LEFT JOIN ${tables.categories} sc ON w.category_id = sc.id
    LEFT JOIN ${tables.workout} lw ON w.linked_workout_id = lw.id
    WHERE w.category_id = ?
  `
  const result = await executeQuery(query, [categoryId])
  if (!result.result.resultRows) {
    return []
  }
  return result.result.resultRows.map((row: unknown[]) => ({
    id: row[0] as number,
    code: row[1] as string,
    name: row[2] as string,
    unitId: row[3] as number | null,
    unitPrice: row[4] as number | null,
    status: row[5] as Workout['status'],
    vat: row[6] as number,
    categoryId: row[7] as number,
    unitPriceIsPercentage: !!row[8], // Convert 0/1 to false/true
    calculationType: row[9] as WorkoutCalculationType,
    linkedWorkoutId: row[10] as number | null,
    unitPriceSign: row[11] as '+' | '-',
    unitPriceUnit: row[12] as 'VND' | 'PERCENT',
    autoFocusCode: row[13] as string | null, // Map the new column
    debitCodeId: row[14] as number | null, // Map the debit_code_id column
    unitCode: row[15] as string | null,
    unitName: row[16] as string | null,
    categoryName: row[17] as string | null,
    linkedWorkoutName: row[18] as string | null,
  }))
}

export async function addWorkout(
  workout: Omit<Workout, 'id' | 'unitCode' | 'unitName' | 'categoryName'>,
): Promise<{ id: number }> {
  const {
    code,
    name,
    unitId,
    unitPrice,
    unitPriceSign, // Added
    unitPriceUnit, // Added
    status,
    vat,
    categoryId,
    unitPriceIsPercentage,
    calculationType,
    linkedWorkoutId,
    autoFocusCode, // Added
    debitCodeId, // Added
  } = workout

  // Ensure unitPrice is always positive or null for storage
  const priceToStore = unitPrice === null || unitPrice === undefined ? null : Math.abs(unitPrice)

  await executeQuery(
    `INSERT INTO ${tables.workout} (code, name, unit_id, unit_price, unit_price_sign, unit_price_unit, status, vat, category_id, unit_price_is_percentage, calculation_type, linked_workout_id, linked_auto_focus_code, debit_code_id)
     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
    [
      code,
      name,
      unitId ?? null,
      priceToStore, // Store absolute value
      unitPriceSign || '+', // Default to '+' if not provided
      unitPriceUnit || 'VND', // Default to 'VND' if not provided
      status,
      vat,
      categoryId,
      unitPriceIsPercentage ? 1 : 0,
      calculationType,
      linkedWorkoutId ?? null,
      calculationType === 'Auto' ? (autoFocusCode ?? null) : null, // Save autoFocusCode if type is 'Auto'
      debitCodeId ?? null, // Save debitCodeId
    ],
  )
  const idResult = await executeQuery('SELECT last_insert_rowid() as id')
  const firstRow = idResult.result.resultRows?.[0]
  if (firstRow && typeof firstRow[0] === 'number') {
    return { id: firstRow[0] }
  }
  throw new Error('Failed to retrieve ID after workout insertion.')
}

export async function updateWorkout(workout: Partial<Workout> & { id: number }): Promise<void> {
  const { id, ...fieldsToUpdate } = workout
  // Remove joined fields that are not part of the workout table
  const validFields = { ...fieldsToUpdate }
  delete validFields.unitCode
  delete validFields.unitName
  delete validFields.categoryName
  delete validFields.autoFocusCode // This will be handled by linked_auto_focus_code
  delete validFields.linkedWorkoutName // This is a derived field

  // Ensure unitPrice is stored as absolute value if it's being updated
  if (validFields.unitPrice !== undefined && validFields.unitPrice !== null) {
    validFields.unitPrice = Math.abs(validFields.unitPrice)
  }

  // Prepare fields for update, including linked_auto_focus_code
  const updateData: Partial<Workout> = { ...validFields }
  if (fieldsToUpdate.calculationType === 'Auto') {
    updateData.autoFocusCode = fieldsToUpdate.autoFocusCode ?? null
  } else if (fieldsToUpdate.calculationType) {
    // If type is being set to 'Manual' or 'Link', clear autoFocusCode
    updateData.autoFocusCode = null
  }

  const fieldEntries = Object.entries(updateData)
  if (fieldEntries.length === 0) return

  const setClause = fieldEntries
    .map(([key]) => {
      let dbColumnName = key.replace(/([A-Z])/g, '_$1').toLowerCase()
      if (key === 'autoFocusCode') {
        dbColumnName = 'linked_auto_focus_code' // Explicitly map to the correct DB column
      }
      if (key === 'debitCodeId') {
        dbColumnName = 'debit_code_id' // Explicitly map to the correct DB column
      }
      // Handle boolean conversion for unit_price_is_percentage
      return `${dbColumnName} = ?`
    })
    .join(', ')
  const values = fieldEntries.map(([key, value]) => {
    return key === 'unitPriceIsPercentage' ? (value ? 1 : 0) : value === undefined ? null : value
  })
  await executeQuery(`UPDATE ${tables.workout} SET ${setClause} WHERE id = ?`, [...values, id])
}

export async function deleteWorkout(id: number): Promise<void> {
  await executeQuery(`DELETE FROM ${tables.workout} WHERE id = ?`, [id])
}

// --- Utility Functions (e.g., for fetching Units) ---
export async function getAllUnits(): Promise<Unit[]> {
  const query = `SELECT id, code, name, status FROM ${tables.units} WHERE status = 'active'` // Chỉ lấy unit active
  const result = await executeQuery(query)
  if (!result.result.resultRows) {
    return []
  }
  // Map array of arrays to array of Unit objects
  return result.result.resultRows.map((row: unknown[]) => ({
    id: row[0] as number,
    code: row[1] as string,
    name: row[2] as string,
    status: row[3] as 'active' | 'inactive',
  }))
}
