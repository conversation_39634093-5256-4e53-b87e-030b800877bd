<script setup lang="ts">
import { ref, watch } from 'vue'
import { Modal, Form, Input, InputNumber, message } from 'ant-design-vue'
import type { Stock } from '@IVC/services/master-data/stock'
import { createOrUpdateStock } from '@IVC/services/master-data/stock'

interface Props {
  visible: boolean
  stock: Stock | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref()
const loading = ref(false)
const formData = ref({
  avrRtpcQty: 0,
  avrRtpcQtyTemp: 0,
  location: '',
})

// Watch for stock changes to populate form
watch(
  () => props.stock,
  (newStock) => {
    if (newStock) {
      formData.value = {
        avrRtpcQty: newStock.avrRtpcQty,
        avrRtpcQtyTemp: newStock.avrRtpcQtyTemp,
        location: newStock.location || '',
      }
    }
  },
  { immediate: true },
)

const handleCancel = () => {
  emit('update:visible', false)
}

const handleOk = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true

    if (!props.stock) {
      message.error('No stock data available')
      return
    }

    // Update stock with form data
    await createOrUpdateStock({
      productCode: props.stock.productCode,
      customerId: props.stock.customerId,
      avrRtpcQty: formData.value.avrRtpcQty,
      avrRtpcQtyTemp: formData.value.avrRtpcQtyTemp,
      location: formData.value.location || null,
    })

    message.success('Stock updated successfully')
    emit('success')
    emit('update:visible', false)
  } catch (error) {
    message.error(`Failed to update stock: ${(error as Error).message}`)
  } finally {
    loading.value = false
  }
}

const rules = {
  avrRtpcQty: [
    { required: true, message: 'Quantity is required' },
    { type: 'number' as const, min: 0, message: 'Quantity must be non-negative' },
  ],
  avrRtpcQtyTemp: [
    { required: true, message: 'Temp quantity is required' },
    { type: 'number' as const, min: 0, message: 'Temp quantity must be non-negative' },
  ],
  status: [{ required: true, message: 'Status is required' }],
}
</script>

<template>
  <Modal
    :visible="visible"
    title="Edit Stock"
    :confirm-loading="loading"
    @ok="handleOk"
    @cancel="handleCancel"
    width="600px"
  >
    <Form ref="formRef" :model="formData" :rules="rules" layout="vertical" v-if="stock">
      <!-- Read-only fields -->
      <div class="mb-4 p-3 bg-gray-50 rounded">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <div class="text-sm font-medium text-gray-600">Customer ID:</div>
            <div class="text-sm text-gray-800">{{ stock.customerId }}</div>
          </div>
          <div>
            <div class="text-sm font-medium text-gray-600">Product Code:</div>
            <div class="text-sm text-gray-800">{{ stock.productCode }}</div>
          </div>
          <div class="col-span-2">
            <div class="text-sm font-medium text-gray-600">Product Name:</div>
            <div class="text-sm text-gray-800">{{ stock.productName }}</div>
          </div>
          <div>
            <div class="text-sm font-medium text-gray-600">Pieces Per Carton:</div>
            <div class="text-sm text-gray-800">{{ stock.productPpcNum || 'N/A' }}</div>
          </div>
          <div>
            <div class="text-sm font-medium text-gray-600">Packaging Hierarchy:</div>
            <div class="text-sm text-gray-800">{{ stock.productHrc1 || 'N/A' }}</div>
          </div>
          <div>
            <div class="text-sm font-medium text-gray-600">Product Category:</div>
            <div class="text-sm text-gray-800">{{ stock.productHrc3 || 'N/A' }}</div>
          </div>
          <div>
            <div class="text-sm font-medium text-gray-600">Piece Cubic Meters:</div>
            <div class="text-sm text-gray-800">{{ stock.productPcm3 || 'N/A' }}</div>
          </div>
        </div>
      </div>

      <!-- Editable fields -->
      <Form.Item label="AVR RTPC Quantity" name="avrRtpcQty">
        <InputNumber
          v-model:value="formData.avrRtpcQty"
          :min="0"
          style="width: 100%"
          placeholder="Enter quantity"
        />
      </Form.Item>

      <Form.Item label="AVR RTPC Quantity Temp" name="avrRtpcQtyTemp">
        <InputNumber
          v-model:value="formData.avrRtpcQtyTemp"
          :min="0"
          style="width: 100%"
          placeholder="Enter temporary quantity"
        />
      </Form.Item>

      <Form.Item label="Location" name="location">
        <Input v-model:value="formData.location" placeholder="Enter location" />
      </Form.Item>
    </Form>
  </Modal>
</template>

<style scoped>
.mb-4 {
  margin-bottom: 1rem;
}

.p-3 {
  padding: 0.75rem;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.rounded {
  border-radius: 0.375rem;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.gap-4 {
  gap: 1rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.font-medium {
  font-weight: 500;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-800 {
  color: #1f2937;
}
</style>
