<template>
  <a-modal
    :open="visible"
    :title="`Workout Charges for ${customerName}${divisionId ? ' - ' + divisionName : ''}`"
    width="1200px"
    :footer="null"
    @cancel="handleCancel"
    :mask-closable="false"
  >
    <div class="workout-charge-list-modal">
      <!-- Header Actions -->
      <div class="header-actions">
        <div class="header-info">
          <p>
            {{
              divisionId
                ? 'Manage all workout charges for this customer division'
                : 'Manage all workout charges for this customer (no divisions)'
            }}
          </p>
        </div>
      </div>

      <a-divider />

      <!-- Workout Charges Table -->
      <div v-if="workoutCharges.length > 0" class="workout-charges-table">
        <a-table
          :columns="columns"
          :data-source="workoutCharges"
          :loading="loading"
          row-key="id"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'id'">
              <a-tag color="blue">{{ record.id.substring(0, 8) }}...</a-tag>
            </template>

            <template v-else-if="column.key === 'created_at'">
              <span class="date-text">{{ formatDate(record.created_at) }}</span>
            </template>

            <template v-else-if="column.key === 'updated_at'">
              <span class="date-text">{{ formatDate(record.updated_at) }}</span>
            </template>

            <template v-else-if="column.key === 'actions'">
              <div class="action-buttons-cell">
                <a-button type="text" size="small" @click="editWorkoutCharge(record)">
                  <template #icon>
                    <EditOutlined />
                  </template>
                  Edit
                </a-button>
                <a-popconfirm
                  title="Delete Workout Charge"
                  description="Are you sure you want to delete this workout charge?"
                  ok-text="Yes, Delete"
                  cancel-text="Cancel"
                  @confirm="deleteWorkoutCharge(record)"
                  :ok-button-props="{ danger: true }"
                >
                  <a-button type="text" danger size="small">
                    <template #icon>
                      <DeleteOutlined />
                    </template>
                    Delete
                  </a-button>
                </a-popconfirm>
              </div>
            </template>
          </template>
        </a-table>
      </div>

      <!-- Empty State -->
      <div v-if="!loading && workoutCharges.length === 0" class="empty-state">
        <a-empty description="No workout charges found for this customer division">
          <template #default>
            <a-button type="primary" @click="openCreateModal">
              <template #icon>
                <PlusOutlined />
              </template>
              Create First Workout Charge
            </a-button>
          </template>
        </a-empty>
      </div>
    </div>

    <!-- Workout Charge Details Modal -->
    <WorkoutChargeHierarchical
      :visible="createModalVisible"
      :customer-id="customerId"
      :division-id="divisionId"
      :customer-name="customerName"
      :division-name="divisionName"
      :existing-data="selectedWorkoutCharge"
      @close="handleCreateModalClose"
      @save="handleWorkoutChargeSave"
    />
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import type { TableColumnsType } from 'ant-design-vue'
import type { WorkoutCustomerCharge } from '../../types/WorkoutCustomerCharge'
import {
  getWorkoutCustomerCharges,
  deleteWorkoutCustomerChargeById,
} from '../../services/master-data/workoutCustomerCharge/workoutCustomerChargeService'
import WorkoutChargeHierarchical from './WorkoutChargeModalHierarchical.vue'

// Props
interface Props {
  visible: boolean
  customerId: number
  divisionId: number
  customerName: string
  divisionName: string
}

const props = defineProps<Props>()
const emit = defineEmits(['close', 'refresh'])

// Reactive data
const loading = ref(false)
const workoutCharges = ref<WorkoutCustomerCharge[]>([])
const createModalVisible = ref(false)
const selectedWorkoutCharge = ref<WorkoutCustomerCharge | null>(null)

// Table columns
const columns: TableColumnsType = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 120,
  },
  {
    title: 'Created',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 150,
  },
  {
    title: 'Last Updated',
    dataIndex: 'updated_at',
    key: 'updated_at',
    width: 150,
  },
  {
    title: 'Actions',
    key: 'actions',
    width: 200,
    align: 'center',
  },
]

// Helper functions
const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString)
    return date.toLocaleString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch (error) {
    return dateString
  }
}

// Methods
const loadWorkoutCharges = async () => {
  try {
    loading.value = true
    console.log(
      'Loading workout charges for customer/division:',
      props.customerId,
      props.divisionId,
    )

    // Don't force reinitialize - it drops existing data!
    // Table should already exist from save operations
    console.log('Skipping database reinitialization to preserve data')

    const filters: any = {
      customer_id: props.customerId,
    }

    // Only add division_id filter if it's not null (for customers without divisions)
    if (props.divisionId) {
      filters.division_id = props.divisionId
    }

    const data = await getWorkoutCustomerCharges(filters)
    workoutCharges.value = data

    console.log('Loaded workout charges from SQLite database:', data)
  } catch (error) {
    console.error('Error loading workout charges from database:', error)
    workoutCharges.value = []

    // Don't show error message if it's just "no data found" or table doesn't exist
    if (!error.message?.includes('no such table') && !error.message?.includes('no data')) {
      message.error('Failed to load workout charges from database')
    }
  } finally {
    loading.value = false
  }
}

const openCreateModal = () => {
  console.log('=== OPENING CREATE MODAL ===')
  console.log('Customer ID:', props.customerId)
  console.log('Division ID:', props.divisionId)
  console.log('Selected workout charge:', selectedWorkoutCharge.value)
  createModalVisible.value = true
  console.log('Create modal visible set to:', createModalVisible.value)
}

const handleCreateModalClose = () => {
  console.log('Add/Edit modal closing...')
  createModalVisible.value = false
  selectedWorkoutCharge.value = null // Clear selected data
}

const handleWorkoutChargeSave = async (savedData: WorkoutCustomerCharge | null) => {
  console.log('handleWorkoutChargeSave called with:', savedData)

  // Refresh the list after save
  console.log('Refreshing workout charges list...')
  await loadWorkoutCharges()

  // Close ONLY the add/edit modal (not the list modal)
  console.log('Closing add/edit modal...')
  createModalVisible.value = false
  selectedWorkoutCharge.value = null

  // Emit refresh to parent (but don't close this list modal)
  emit('refresh')

  console.log('Save handling completed - ONLY add dialog closed, list dialog stays open')
}

const editWorkoutCharge = (record: WorkoutCustomerCharge) => {
  // Set the selected record for editing
  selectedWorkoutCharge.value = record
  // Open create modal which will detect existing data and switch to edit mode
  openCreateModal()
}

const deleteWorkoutCharge = async (record: WorkoutCustomerCharge) => {
  try {
    loading.value = true
    console.log('=== DELETING WORKOUT CHARGE ===')
    console.log('Record to delete:', record)
    console.log('Record ID:', record.id)
    console.log('Customer ID:', record.customer_id)
    console.log('Division ID:', record.division_id)

    // Use the simplified delete function by ID
    await deleteWorkoutCustomerChargeById(record.id)

    console.log('Delete operation completed successfully')
    message.success('Workout charge deleted successfully')

    // Force reload the list to reflect changes
    console.log('Reloading workout charges list...')
    await loadWorkoutCharges()

    console.log('List reloaded, emitting refresh event...')
    emit('refresh')

    console.log('Delete operation fully completed')
  } catch (error) {
    console.error('Error deleting workout charge:', error)
    message.error(`Failed to delete workout charge: ${error.message}`)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('close')
}

// Watch for props changes
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      loadWorkoutCharges()
    }
  },
)

// Initialize
onMounted(() => {
  if (props.visible) {
    loadWorkoutCharges()
  }
})
</script>

<script lang="ts">
export default {
  name: 'WorkoutChargeListModal',
}
</script>

<style scoped>
.workout-charge-list-modal {
  padding: 0;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.header-info h3 {
  margin: 0 0 8px 0;
  color: #1890ff;
  font-size: 18px;
  font-weight: 600;
}

.header-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.workout-charges-table {
  margin-bottom: 16px;
}

.action-buttons-cell {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.total-items {
  font-weight: 600;
  color: #52c41a;
}

.date-text {
  font-size: 12px;
  color: #666;
}

.empty-state {
  text-align: center;
  padding: 20px;
}

:deep(.ant-modal-body) {
  padding: 24px;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 8px 12px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-badge-count) {
  font-size: 12px;
  min-width: 20px;
  height: 20px;
  line-height: 18px;
}
</style>
