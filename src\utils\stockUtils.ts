// Stock utility functions for frontend calculations

export type StockStatus = 'Normal' | 'Low Stock' | 'Out of Stock'

/**
 * Determine stock status based on quantity
 * This function was moved from backend to frontend for dynamic calculation
 */
export function determineStockStatus(quantity: number): StockStatus {
  if (quantity === 0) return 'Out of Stock'
  if (quantity <= 10) return 'Low Stock'
  return 'Normal'
}

/**
 * Get color for stock status display
 */
export function getStatusColor(status: StockStatus): string {
  switch (status) {
    case 'Normal':
      return 'green'
    case 'Low Stock':
      return 'orange'
    case 'Out of Stock':
      return 'red'
    default:
      return 'default'
  }
}

/**
 * Get color based on quantity value
 */
export function getQuantityColor(quantity: number): string {
  const status = determineStockStatus(quantity)
  return getStatusColor(status)
}
