<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue'
import { Modal, Form, Input, Select, InputNumber, message, Row, Col } from 'ant-design-vue'
import type { SelectValue } from 'ant-design-vue/es/select'
import type { Unit } from '@IVC/types/MasterDataTypes/Unit'
import type { EntityStatus } from '@IVC/types/common'
import { getAllUnits, getAllWorkouts } from '@IVC/services/master-data/category' // Removed getAllSimpleCategories
import type { Workout, WorkoutCalculationType } from '@IVC/types/MasterDataTypes/Category'
import { getAutoFocusItems } from '@IVC/services/master-data/autoFunction/autoFunctionService'
import type { AutoFunction } from '@IVC/types/AutoFunction'
import type { DebitCode } from '@IVC/types/CompanyProfile'
import { getCompanyProfile } from '@IVC/services/master-data/companyProfile/companyProfileService'

interface Props {
  visible: boolean
  workoutData?: Partial<Workout> | null // Allow id to be undefined for new workouts
}

const props = defineProps<Props>()
const emit = defineEmits(['close', 'save'])

interface WorkoutFormData {
  id?: number // To know if we are editing
  code: string
  name: string
  categoryId: number | undefined
  unitId: number | undefined // For Select, undefined means not selected
  unitPrice: number | undefined // For InputNumber, undefined means empty. Can be positive or negative.
  unitPriceSign: '+' | '-' // To explicitly set if the price is positive or negative adjustment
  unitPriceUnit: 'VND' | 'PERCENT' // Simplified to VND or PERCENT
  vat: number | undefined // VAT as percentage for form input
  status: EntityStatus
  calculationType: WorkoutCalculationType
  linkedWorkoutId: number | null | undefined // For "Link" type
  autoFocusCode?: string | undefined // For "Auto" type, Select expects undefined for empty, not null
  debitCodeId: number | undefined // For Debit Code selection
}

const formModel = ref<WorkoutFormData>({
  code: '',
  name: '',
  categoryId: undefined,
  unitId: undefined,
  unitPrice: undefined,
  unitPriceSign: '+', // Default to positive
  unitPriceUnit: 'VND', // Default to VND, or choose your preferred default
  vat: 0, // Default VAT percentage
  status: 'active',
  calculationType: 'Manual',
  linkedWorkoutId: undefined,
  autoFocusCode: undefined,
  debitCodeId: undefined,
})

// Pass formModel to useForm. Rules are defined in the template.
const { resetFields, validate, validateInfos } = Form.useForm(formModel)
const isLoading = ref(false)
// const simpleCategories = ref<SimpleCategory[]>([]) // Removed as categoryId is now passed in
const units = ref<Unit[]>([])
const linkableWorkouts = ref<Workout[]>([]) // For "Link" type
const autoFocusItems = ref<AutoFunction[]>([]) // For "Auto" type
const debitCodes = ref<DebitCode[]>([]) // For "Debit Code" selection

const modalTitle = computed(() => (props.workoutData?.id ? 'Edit Workout' : 'Add New Workout'))

const fetchDropdownData = async () => {
  try {
    isLoading.value = true
    // Fetch units, auto focus items, and debit codes
    const [unitsResult, autoFocusResult, companyProfile] = await Promise.all([
      getAllUnits(),
      getAutoFocusItems(),
      getCompanyProfile(),
    ])
    units.value = unitsResult.filter((unit) => unit.status === 'active') // Only active units
    autoFocusItems.value = autoFocusResult
    debitCodes.value = companyProfile.debitCodes.filter((code) => code.isActive) // Only active debit codes

    // Mocking linkable workouts for now if getAllWorkouts is not ready
    // Replace with actual API call: const workoutsResult = await getAllWorkouts();
    // linkableWorkouts.value = workoutsResult.filter(w => w.id !== props.workoutData?.id);
  } catch (err) {
    message.error(`Failed to load data for form: ${(err as Error).message}`)
    console.error(err)
  } finally {
    isLoading.value = false
  }
}

const fetchLinkableWorkouts = async () => {
  try {
    const results = await getAllWorkouts()
    if (props.workoutData?.id) {
      linkableWorkouts.value = results.filter((w) => w.id !== props.workoutData?.id)
    } else {
      linkableWorkouts.value = results
    }
  } catch (err) {
    message.error(`Failed to load linkable workouts: ${(err as Error).message}`)
    linkableWorkouts.value = [] // Ensure it's an empty array on error
  }
}

// Helper functions to reduce complexity
const normalizeNullToUndefined = <T,>(value: T | null): T | undefined =>
  value === null ? undefined : value

const getAutoFocusCodeForForm = (workoutData: Partial<Workout>) => {
  if (workoutData.calculationType !== 'Auto') {
    return undefined
  }
  return normalizeNullToUndefined(workoutData.autoFocusCode)
}

const populateFormFromWorkoutData = (workoutData: Partial<Workout>) => {
  formModel.value = {
    id: workoutData.id,
    code: workoutData.code || '',
    name: workoutData.name || '',
    categoryId: workoutData.categoryId,
    unitId: normalizeNullToUndefined(workoutData.unitId),
    unitPrice: normalizeNullToUndefined(workoutData.unitPrice),
    unitPriceSign: (workoutData.unitPrice || 0) < 0 ? '-' : '+',
    unitPriceUnit: workoutData.unitPriceIsPercentage ? 'PERCENT' : 'VND',
    vat: workoutData.vat != null ? workoutData.vat * 100 : undefined,
    status: workoutData.status || 'active',
    calculationType: workoutData.calculationType || 'Manual',
    linkedWorkoutId: normalizeNullToUndefined(workoutData.linkedWorkoutId),
    autoFocusCode: getAutoFocusCodeForForm(workoutData),
    debitCodeId: normalizeNullToUndefined(workoutData.debitCodeId),
  }
}

const resetFormToDefaults = () => {
  formModel.value = {
    id: undefined,
    code: '',
    name: '',
    categoryId: undefined,
    unitId: undefined,
    unitPrice: undefined,
    unitPriceSign: '+',
    unitPriceUnit: 'VND',
    vat: 0,
    status: 'active',
    calculationType: 'Manual',
    linkedWorkoutId: undefined,
    autoFocusCode: undefined,
    debitCodeId: undefined,
  }
  resetFields()
}

const checkAndRefreshDropdownData = () => {
  if (units.value.length === 0 || autoFocusItems.value.length === 0) {
    fetchDropdownData()
  }
}

onMounted(() => {
  fetchDropdownData()
  fetchLinkableWorkouts()
})

watch(
  () => props.visible,
  (isVisible) => {
    if (isVisible) {
      if (props.workoutData) {
        populateFormFromWorkoutData(props.workoutData)
      } else {
        resetFormToDefaults()
      }

      fetchLinkableWorkouts()
      checkAndRefreshDropdownData()
    }
  },
)

const validateCalculationTypeRequirements = (): string[] => {
  const errors: string[] = []

  if (formModel.value.calculationType === 'Link' && !formModel.value.linkedWorkoutId) {
    errors.push('Please select a workout to link for "Link" calculation type.')
  }

  if (formModel.value.calculationType === 'Auto' && !formModel.value.autoFocusCode) {
    errors.push('Please select an Auto Focus item for "Auto" calculation type.')
  }

  return errors
}

const validateRequiredFields = (): string[] => {
  const errors: string[] = []

  if (!formModel.value.categoryId) {
    errors.push('Category ID is missing. Cannot save workout.')
  }

  if (!formModel.value.code?.trim()) {
    errors.push('Workout code is required and cannot be empty.')
  }

  if (!formModel.value.name?.trim()) {
    errors.push('Workout name is required and cannot be empty.')
  }

  return errors
}

const validateNumericFields = (): string[] => {
  const errors: string[] = []

  // Validate unit price when provided
  if (formModel.value.unitPrice !== undefined && formModel.value.unitPrice !== null) {
    if (formModel.value.unitPrice < 0) {
      errors.push('Unit price cannot be negative. Use the +/- selector instead.')
    }
    if (formModel.value.unitPriceUnit === 'PERCENT' && formModel.value.unitPrice > 100) {
      errors.push('Percentage unit price cannot exceed 100%.')
    }
  }

  // Validate VAT percentage
  if (formModel.value.vat !== undefined && formModel.value.vat !== null) {
    if (formModel.value.vat < 0 || formModel.value.vat > 100) {
      errors.push('VAT percentage must be between 0 and 100.')
    }
  }

  return errors
}

const calculateFinalUnitPrice = (): number | null => {
  if (formModel.value.unitPrice === undefined || formModel.value.unitPrice === null) {
    return null
  }
  const price = Math.abs(formModel.value.unitPrice)
  return formModel.value.unitPriceSign === '-' ? -price : price
}

const calculateLinkedWorkoutId = (): number | null => {
  if (formModel.value.calculationType === 'Link') {
    return formModel.value.linkedWorkoutId === undefined ? null : formModel.value.linkedWorkoutId
  }
  return null
}

const calculateAutoFocusCode = (): string | null => {
  return formModel.value.calculationType === 'Auto' ? formModel.value.autoFocusCode || null : null
}

const buildDataToSave = (): Omit<Workout, 'unitCode' | 'unitName' | 'categoryName'> & {
  calculationType: WorkoutCalculationType
  autoFocusCode?: string | null
} => ({
  id: formModel.value.id as number,
  code: formModel.value.code.trim(),
  name: formModel.value.name.trim(),
  categoryId: formModel.value.categoryId as number,
  status: formModel.value.status,
  unitPriceIsPercentage: formModel.value.unitPriceUnit === 'PERCENT',
  vat: formModel.value.vat != null ? formModel.value.vat / 100 : 0,
  unitId: formModel.value.unitId === undefined ? null : formModel.value.unitId,
  unitPrice: calculateFinalUnitPrice(),
  unitPriceSign: formModel.value.unitPriceSign,
  unitPriceUnit: formModel.value.unitPriceUnit,
  calculationType: formModel.value.calculationType,
  linkedWorkoutId: calculateLinkedWorkoutId(),
  autoFocusCode: calculateAutoFocusCode(),
  debitCodeId: formModel.value.debitCodeId === undefined ? null : formModel.value.debitCodeId,
})

const handleOk = async () => {
  try {
    isLoading.value = true

    // Form validation
    await validate()

    // Business logic validation
    const validationErrors = [
      ...validateCalculationTypeRequirements(),
      ...validateRequiredFields(),
      ...validateNumericFields(),
    ]

    if (validationErrors.length > 0) {
      validationErrors.forEach((error) => message.error(error))
      isLoading.value = false
      return
    }

    const dataToSave = buildDataToSave()

    emit('save', dataToSave as Partial<Workout>)
    // Success message will be handled by parent component
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
    message.error('Please correct the form errors.')
  } finally {
    // isLoading is handled by the parent component after save
  }
}

const handleCancel = () => {
  emit('close')
}

const resetFormAndLoading = () => {
  resetFields() // Resets the form based on formModel
  isLoading.value = false
}

defineExpose({
  resetFormAndLoading,
})
</script>

<template>
  <Modal
    :visible="props.visible"
    :title="modalTitle"
    :confirm-loading="isLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :destroy-on-close="true"
    width="600px"
  >
    <Form :model="formModel" layout="vertical" name="workoutForm">
      <Form.Item
        name="code"
        label="Workout Code"
        :rules="[{ required: true, message: 'Please input the workout code!' }]"
        :validate-status="validateInfos.code?.validateStatus"
        :help="validateInfos.code?.help"
      >
        <Input v-model:value="formModel.code" />
      </Form.Item>

      <Form.Item
        name="name"
        label="Workout Name"
        :rules="[{ required: true, message: 'Please input the workout name!' }]"
        :validate-status="validateInfos.name?.validateStatus"
        :help="validateInfos.name?.help"
      >
        <Input v-model:value="formModel.name" />
      </Form.Item>

      <Form.Item
        name="unitId"
        label="Unit"
        :rules="[{ required: true, message: 'Please select a unit!' }]"
        :validate-status="validateInfos.unitId?.validateStatus"
        :help="validateInfos.unitId?.help"
      >
        <Select
          v-model:value="formModel.unitId"
          placeholder="Select a unit (optional)"
          allow-clear
          :loading="isLoading"
          show-search
          option-filter-prop="label"
        >
          <Select.Option v-for="unit in units" :key="unit.id" :value="unit.id" :label="unit.name">
            {{ unit.name }} ({{ unit.code }})
          </Select.Option>
        </Select>
      </Form.Item>
      <Row :gutter="16">
        <Col :span="24">
          <Form.Item
            name="unitPrice"
            label="Unit Price"
            :rules="[{ required: true, message: 'Please input the unit price!' }]"
            :validate-status="validateInfos.unitPrice?.validateStatus"
            :help="validateInfos.unitPrice?.help"
            style="margin-bottom: 0"
          >
            <InputNumber
              v-model:value="formModel.unitPrice"
              style="width: 100%"
              placeholder="Enter value"
              :min="0"
              @change="
                (currentValue: number | string | null) => {
                  if (typeof currentValue === 'number') {
                    // If the input is a valid number, store its absolute value.
                    // This will also be reflected in the v-model binding.
                    formModel.unitPrice = Math.abs(currentValue)
                  } else if (currentValue === null) {
                    // If the input is cleared (null), set formModel.unitPrice to undefined.
                    formModel.unitPrice = undefined
                  }
                  // If currentValue is a string (e.g., invalid input or empty string during typing),
                  // v-model and InputNumber's internal logic will typically handle setting formModel.unitPrice to undefined.
                  // No explicit action for Math.abs() is needed here for strings.
                }
              "
            >
              <template #addonBefore>
                <Select v-model:value="formModel.unitPriceSign" style="width: 60px">
                  <Select.Option value="+">+</Select.Option>
                  <Select.Option value="-">-</Select.Option>
                </Select>
              </template>
              <template #addonAfter>
                <Select v-model:value="formModel.unitPriceUnit" style="width: 90px">
                  <Select.Option value="VND">VNĐ</Select.Option>
                  <Select.Option value="PERCENT">%</Select.Option>
                </Select>
              </template>
            </InputNumber>
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        name="calculationType"
        label="Calculation Type"
        :rules="[{ required: true, message: 'Please select a calculation type!' }]"
      >
        <Select v-model:value="formModel.calculationType" placeholder="Select calculation type">
          <Select.Option value="Manual">Manual</Select.Option>
          <Select.Option value="Link">Link</Select.Option>
          <Select.Option value="Auto">Auto</Select.Option>
        </Select>
      </Form.Item>

      <Form.Item
        v-if="formModel.calculationType === 'Link'"
        name="linkedWorkoutId"
        label="Link to Workout"
        :rules="[
          {
            required: formModel.calculationType === 'Link',
            message: 'Please select a workout to link!',
          },
        ]"
        :validate-status="validateInfos.linkedWorkoutId?.validateStatus"
        :help="validateInfos.linkedWorkoutId?.help"
      >
        <Select
          :value="formModel.linkedWorkoutId === null ? undefined : formModel.linkedWorkoutId"
          @update:value="
            (selectedValue: SelectValue) => {
              if (selectedValue === undefined || selectedValue === null) {
                formModel.linkedWorkoutId = undefined
              } else if (typeof selectedValue === 'string') {
                const parsed = parseInt(selectedValue, 10)
                formModel.linkedWorkoutId = isNaN(parsed) ? undefined : parsed
              } else if (typeof selectedValue === 'number') {
                formModel.linkedWorkoutId = selectedValue
              } else if (typeof selectedValue === 'object' && 'value' in selectedValue) {
                // Handle LabeledValue
                const value = selectedValue.value
                if (typeof value === 'string') {
                  const parsed = parseInt(value, 10)
                  formModel.linkedWorkoutId = isNaN(parsed) ? undefined : parsed
                } else if (typeof value === 'number') {
                  formModel.linkedWorkoutId = value
                } else {
                  formModel.linkedWorkoutId = undefined
                }
              } else {
                formModel.linkedWorkoutId = undefined
              }
            }
          "
          placeholder="Select a workout to link its price"
          allow-clear
          show-search
          option-filter-prop="label"
          :loading="isLoading"
        >
          <Select.Option
            v-for="wo in linkableWorkouts"
            :key="wo.id"
            :value="wo.id"
            :label="wo.name"
          >
            {{ wo.name }} ({{ wo.code }})
          </Select.Option>
        </Select>
      </Form.Item>

      <Form.Item
        v-if="formModel.calculationType === 'Auto'"
        name="autoFocusCode"
        label="Link to Auto Focus Item"
        :rules="[
          {
            required: formModel.calculationType === 'Auto',
            message: 'Please select an Auto Focus item!',
          },
        ]"
      >
        <Select
          v-model:value="formModel.autoFocusCode"
          placeholder="Select an Auto Focus item"
          allow-clear
          show-search
          option-filter-prop="label"
          :loading="isLoading"
        >
          <Select.Option
            v-for="afItem in autoFocusItems"
            :key="afItem.id"
            :value="afItem.code"
            :label="afItem.name"
          >
            {{ afItem.name }} ({{ afItem.code }})
          </Select.Option>
        </Select>
      </Form.Item>

      <Form.Item
        name="vat"
        label="VAT (%)"
        :rules="[{ required: true, message: 'Please input VAT percentage!' }]"
        :validate-status="validateInfos.vat?.validateStatus"
        :help="validateInfos.vat?.help"
      >
        <InputNumber
          v-model:value="formModel.vat"
          style="width: 100%"
          placeholder="Enter VAT percentage (e.g., 7 for 7%)"
          :min="0"
          :max="100"
        />
      </Form.Item>

      <Form.Item
        name="debitCodeId"
        label="Debit Code"
        :rules="[{ required: false, message: 'Please select a debit code!' }]"
        :validate-status="validateInfos.debitCodeId?.validateStatus"
        :help="validateInfos.debitCodeId?.help"
      >
        <Select
          v-model:value="formModel.debitCodeId"
          placeholder="Select a debit code (optional)"
          allow-clear
          :loading="isLoading"
          show-search
          option-filter-prop="label"
        >
          <Select.Option
            v-for="debitCode in debitCodes"
            :key="debitCode.id"
            :value="debitCode.id"
            :label="debitCode.name"
          >
            {{ debitCode.name }} ({{ debitCode.code }})
          </Select.Option>
        </Select>
      </Form.Item>

      <Form.Item
        name="status"
        label="Status"
        :rules="[{ required: true, message: 'Please select a status!' }]"
        :validate-status="validateInfos.status?.validateStatus"
        :help="validateInfos.status?.help"
      >
        <Select v-model:value="formModel.status" placeholder="Select status">
          <Select.Option value="active">Active</Select.Option>
          <Select.Option value="inactive">Inactive</Select.Option>
        </Select>
      </Form.Item>
    </Form>
  </Modal>
</template>

<style scoped>
/* Add any specific styles for WorkoutForm if needed */
.ant-form-item-label {
  padding-bottom: 4px; /* Optional: Adjust spacing for labels */
}
.ant-input-number {
  width: 100%;
}
</style>
