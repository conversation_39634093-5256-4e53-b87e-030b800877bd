import type { CompanyProfile, CompanyStore, DebitCode, CompanyInfo } from '../types/CompanyProfile'

// Mock data for demonstration
const mockCompanyProfile: CompanyProfile = {
  id: 1,
  companyInfo: {
    name: 'Nippon Express',
    address: ' <PERSON><PERSON>/189/8 Đ. T<PERSON>, KP, D<PERSON>, Bình Dương',
    phone: '0274 3739 762',
    taxCode: '0123456789',
  },
  stores: [
    {
      id: 1,
      name: 'L.A Logistics Center',
      location: 'Di An District, Binh Duong Province, Vietnam',
      manager: '<PERSON>',
      phone: '+84 28-3812-2922',
      status: 'active',
      isDefault: true,
    },
    {
      id: 2,
      name: 'Song Than Logistics Center',
      location: 'Di An District, Binh Duong Province, Vietnam',
      manager: '<PERSON>',
      phone: '+84 ***********',
      status: 'active',
      isDefault: false,
    },
    {
      id: 3,
      name: 'HPE Hanoi Minni GSD SC warehouse',
      location: 'Hanoi city, Hanoi, Vietnam',
      manager: '<PERSON>',
      phone: '+84 ***********',
      status: 'active',
      isDefault: false,
    },
  ],
  debitCodes: [
    {
      id: 1,
      code: 'DC001',
      name: 'Cash Payment',
      description: 'Direct cash payment method',
      isActive: true,
      isUsed: true,
    },
    {
      id: 2,
      code: 'DC002',
      name: 'Credit Card',
      description: 'Credit/Debit card payment',
      isActive: true,
      isUsed: true,
    },
    {
      id: 3,
      code: 'DC003',
      name: 'Bank Transfer',
      description: 'Electronic bank transfer',
      isActive: true,
      isUsed: true,
    },
  ],
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-15T00:00:00Z',
}

export const getCompanyProfile = async (): Promise<CompanyProfile> => {
  // Simulate API call
  await new Promise((resolve) => setTimeout(resolve, 500))
  return mockCompanyProfile
}

export const updateCompanyProfile = async (profile: CompanyProfile): Promise<CompanyProfile> => {
  // Simulate API call
  await new Promise((resolve) => setTimeout(resolve, 500))

  // Update company info
  mockCompanyProfile.companyInfo = profile.companyInfo

  // Update stores
  mockCompanyProfile.stores = profile.stores

  // Update debit codes and track usage
  mockCompanyProfile.debitCodes = profile.debitCodes
  updateDebitCodeUsage()

  mockCompanyProfile.updatedAt = new Date().toISOString()
  return mockCompanyProfile
}

// Helper function to update debit code usage
const updateDebitCodeUsage = () => {
  mockCompanyProfile.debitCodes.forEach((code) => {
    code.isUsed = false // For now, no stores use debit codes directly
  })
}

export const addCompanyStore = async (store: Omit<CompanyStore, 'id'>): Promise<CompanyStore> => {
  // Simulate API call
  await new Promise((resolve) => setTimeout(resolve, 300))
  const newStore: CompanyStore = {
    ...store,
    id: Math.max(...mockCompanyProfile.stores.map((s) => s.id)) + 1,
  }
  mockCompanyProfile.stores.push(newStore)
  updateDebitCodeUsage()
  return newStore
}

export const updateCompanyStore = async (store: CompanyStore): Promise<CompanyStore> => {
  // Simulate API call
  await new Promise((resolve) => setTimeout(resolve, 300))
  const index = mockCompanyProfile.stores.findIndex((s) => s.id === store.id)
  if (index !== -1) {
    mockCompanyProfile.stores[index] = store
  }
  updateDebitCodeUsage()
  return store
}

export const deleteCompanyStore = async (id: number): Promise<void> => {
  // Simulate API call
  await new Promise((resolve) => setTimeout(resolve, 300))
  const index = mockCompanyProfile.stores.findIndex((s) => s.id === id)
  if (index !== -1) {
    mockCompanyProfile.stores.splice(index, 1)
  }
  updateDebitCodeUsage()
}

export const addDebitCode = async (code: Omit<DebitCode, 'id'>): Promise<DebitCode> => {
  // Simulate API call
  await new Promise((resolve) => setTimeout(resolve, 300))
  const newCode: DebitCode = {
    ...code,
    id: Math.max(...mockCompanyProfile.debitCodes.map((c) => c.id)) + 1,
  }
  mockCompanyProfile.debitCodes.push(newCode)
  return newCode
}

export const updateDebitCode = async (code: DebitCode): Promise<DebitCode> => {
  // Simulate API call
  await new Promise((resolve) => setTimeout(resolve, 300))
  const index = mockCompanyProfile.debitCodes.findIndex((c) => c.id === code.id)
  if (index !== -1) {
    mockCompanyProfile.debitCodes[index] = code
  }
  return code
}

export const deleteDebitCode = async (id: number): Promise<void> => {
  // Simulate API call
  await new Promise((resolve) => setTimeout(resolve, 300))
  const index = mockCompanyProfile.debitCodes.findIndex((c) => c.id === id)
  if (index !== -1) {
    mockCompanyProfile.debitCodes.splice(index, 1)
  }
}
