import { ref } from 'vue'

// Global state for default store name
const defaultStoreName = ref('Warehouse Management System')

export const useDefaultStore = () => {
  const updateDefaultStoreName = (name: string) => {
    defaultStoreName.value = name
  }

  const getDefaultStoreName = () => {
    return defaultStoreName.value
  }

  return {
    defaultStoreName,
    updateDefaultStoreName,
    getDefaultStoreName,
  }
}
