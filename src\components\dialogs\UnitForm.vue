<template>
  <a-modal
    :title="formState.id ? 'Edit Unit' : 'Add Unit'"
    :open="visible"
    :confirm-loading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formState" layout="vertical" name="unit_form">
      <a-form-item
        name="code"
        label="Code"
        :rules="[{ required: true, message: 'Please input the unit code!' }]"
      >
        <a-input v-model:value="formState.code" />
      </a-form-item>
      <a-form-item
        name="name"
        label="Name"
        :rules="[{ required: true, message: 'Please input the unit name!' }]"
      >
        <a-input v-model:value="formState.name" />
      </a-form-item>
      <a-form-item
        name="status"
        label="Status"
        :rules="[{ required: true, message: 'Please select the unit status!' }]"
      >
        <a-select v-model:value="formState.status" placeholder="Select status">
          <a-select-option value="active">Active</a-select-option>
          <a-select-option value="inactive">Inactive</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, toRaw } from 'vue'
import type { FormInstance } from 'ant-design-vue'
import type { Unit, EntityStatus } from '@IVC/types/MasterDataTypes/Unit'

interface Props {
  visible: boolean
  unitData?: Unit | null // Changed from customerData to unitData
}

const props = defineProps<Props>()
const emit = defineEmits(['close', 'save'])

const formRef = ref<FormInstance>()
const confirmLoading = ref(false)

const initialFormState: Omit<Unit, 'id' | 'status'> & { id?: number; status?: EntityStatus } = {
  id: undefined,
  code: '',
  name: '',
  status: 'active', // Default status for a new unit
}
const formState = reactive({ ...initialFormState })

watch(
  () => props.unitData,
  (newData) => {
    if (newData) {
      Object.assign(formState, newData)
    } else {
      Object.assign(formState, initialFormState)
      formState.id = undefined // Ensure id is reset for new unit
      // Ensure status is also reset to the default for a new form
      // This is important if the form was previously used for editing an inactive unit
      formState.status = initialFormState.status
    }
  },
  { immediate: true, deep: true },
)

const handleOk = async () => {
  try {
    await formRef.value?.validate()
    confirmLoading.value = true
    const dataToSave: Partial<Unit> = { ...toRaw(formState) }
    if (!formState.id) {
      delete dataToSave.id // Remove id if it's a new unit
    }
    emit('save', dataToSave)
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
    // confirmLoading.value = false; // Reset loading on validation failure
  }
  // confirmLoading is reset in the parent component's finally block or on successful save/close
}

const handleCancel = () => {
  emit('close')
}

const resetFormAndLoading = () => {
  formRef.value?.resetFields()
  Object.assign(formState, initialFormState)
  // Ensure status is explicitly reset to the initialFormState's status
  formState.id = undefined
  confirmLoading.value = false
}

defineExpose({ resetFormAndLoading })
</script>
