export const getFromStorage = <T>(key: string): T[] => {
  const data = localStorage.getItem(key)
  return data ? JSON.parse(data) : []
}

export const saveToStorage = <T>(key: string, data: T[]): void => {
  localStorage.setItem(key, JSON.stringify(data))
}

export const saveToStorageCamelCase = <T extends Record<string, any>>(
  key: string,
  data: T | T[],
): void => {
  const camelData = convertDataToCamelCase(data)
  localStorage.setItem(key, JSON.stringify(camelData))
}

export const clearStorage = (): void => {
  localStorage.clear()
}

function toCamelCase(s: string): string {
  return s.replace(/_([a-z])/g, (_, c) => c.toUpperCase())
}

function convertRecordToCamelCase<T extends Record<string, any>>(record: T): Record<string, any> {
  const result: Record<string, any> = {}
  for (const key in record) {
    if (Object.prototype.hasOwnProperty.call(record, key)) {
      result[toCamelCase(key)] = record[key]
    }
  }
  return result
}

export function convertDataToCamelCase<T extends Record<string, any>>(data: T | T[]): T | T[] {
  if (Array.isArray(data)) {
    return data.map(convertRecordToCamelCase) as T[]
  } else {
    return convertRecordToCamelCase(data) as T
  }
}
