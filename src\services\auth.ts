import { useAuthStore } from '@IVC/stores/auth'
import { useRouter } from 'vue-router'
import { type LoginCredentials, type LoginResponse } from '@IVC/types/login.ts'
import { axiosClient } from '@IVC/common/utils/axiosClient'

export const useAuthService = () => {
  const authStore = useAuthStore()
  const router = useRouter()

  const login = async (credentials: LoginCredentials): Promise<LoginResponse> => {
    authStore.setLoading(true)

    try {
      const response = await axiosClient.post('/auth/login', {
        username: credentials.username,
        password: credentials.password,
      })

      const { status, data } = response.data

      if (status === 'success' && data) {
        const { token_type, access_token, expires_in, user } = data

        // Set auth data in store
        authStore.setAuth(user, access_token)

        return {
          success: true,
          user,
          token: access_token,
          token_type,
          expires_in,
          message: `Welcome back, ${user.username}!`,
        }
      } else {
        return {
          success: false,
          message: 'Invalid username or password',
        }
      }
    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        message: 'Login failed. Please try again.',
      }
    } finally {
      authStore.setLoading(false)
    }
  }

  const logout = async () => {
    authStore.setLoading(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 500))

      // Clear auth data
      authStore.clearAuth()

      // Redirect to login
      await router.push('/login')
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      authStore.setLoading(false)
    }
  }

  /*const getDefaultCredentials = async () => {
    try {
      const response = await axiosClient.get('/auth/default-credentials')
      return response.data
    } catch (error) {
      console.error('Error getting default credentials:', error)
      return []
    }
  }*/

  return {
    login,
    logout,
    // getDefaultCredentials,
  }
}
