{"name": "npe", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "playwright test", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/", "prepare": "husky"}, "lint-staged": {"src/**/*.{js,ts,vue,json,md}": "prettier --write"}, "dependencies": {"@ant-design/plots": "^2.4.0", "@sqlite.org/sqlite-wasm": "^3.49.2-build1", "ant-design-vue": "^4.2.6", "axios": "^1.9.0", "echarts": "^5.6.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-i18n": "^11.1.5", "vue-router": "^4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@playwright/test": "^1.51.1", "@tsconfig/node22": "^22.0.1", "@types/jsdom": "^21.1.7", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vitest/eslint-plugin": "^1.1.39", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-oxlint": "^0.16.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-vue": "~10.0.0", "husky": "^9.1.7", "jiti": "^2.4.2", "jsdom": "^26.0.0", "lint-staged": "^16.1.0", "npm-run-all2": "^7.0.2", "oxlint": "^0.16.0", "prettier": "3.5.3", "typescript": "~5.8.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.1.1", "vue-tsc": "^2.2.8"}}