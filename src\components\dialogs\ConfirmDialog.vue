<template>
  <a-modal :visible="visible" :title="title" @cancel="onCancel" footer="null">
    <p>{{ message }}</p>
    <template #footer>
      <a-button
        v-for="(btn, index) in buttons"
        :key="index"
        :type="btn.type || 'default'"
        @click="emit(btn.event)"
      >
        {{ btn.label }}
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue'

const props = defineProps<{
  visible: boolean
  title: string
  message: string
  buttons: {
    label: string
    type?: 'primary' | 'default' | 'danger'
    event: string
  }[]
}>()

const emit = defineEmits()

function onCancel() {
  emit('cancel')
}
</script>
