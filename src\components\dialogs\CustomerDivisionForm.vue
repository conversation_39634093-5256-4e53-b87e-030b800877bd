<template>
  <a-modal
    :visible="props.visible"
    :title="modalTitle"
    :confirm-loading="isLoading"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formState" layout="vertical" name="customer_division_form">
      <a-form-item
        name="code"
        label="Division Code"
        :rules="[{ required: true, message: 'Please input the division code!' }]"
      >
        <a-input v-model:value="formState.code" />
      </a-form-item>
      <a-form-item
        name="name"
        label="Division Name"
        :rules="[{ required: true, message: 'Please input the division name!' }]"
      >
        <a-input v-model:value="formState.name" />
      </a-form-item>
      <a-form-item name="description" label="Description">
        <a-textarea v-model:value="formState.description" :rows="3" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, toRaw, computed } from 'vue'
import type { FormInstance } from 'ant-design-vue'
import { type CustomerDivision } from '@IVC/types/MasterDataTypes/Customer'

// Represents the data fields managed by this form for a new or existing division
type CustomerDivisionFormFields = Omit<CustomerDivision, 'id' | 'customerId'> & {
  id?: number // id is optional for new, required for edit
  customerId?: number // customerId is set by parent for new, part of data for edit
}

interface Props {
  visible: boolean
  customerIdForNew?: number | null // For adding a new division to a specific customer
  divisionToEdit?: CustomerDivision | null // For editing an existing division
}

const props = defineProps<Props>()
const emit = defineEmits(['close', 'save'])

const formRef = ref<FormInstance>()
const isLoading = ref(false)

const modalTitle = computed(() => {
  return props.divisionToEdit ? 'Edit Customer Division' : 'Add Customer Division'
})

const initialFormState: CustomerDivisionFormFields = {
  id: undefined,
  name: '',
  description: '',
  code: '',
  customerId: undefined,
  status: 'new',
}

const formState = reactive<CustomerDivisionFormFields>({ ...initialFormState })

watch(
  () => props.visible,
  (newValue) => {
    if (newValue) {
      resetForm()
      if (props.divisionToEdit) {
        // If editing, populate form with existing division data
        Object.assign(formState, {
          id: props.divisionToEdit.id,
          code: props.divisionToEdit.code,
          name: props.divisionToEdit.name,
          description: props.divisionToEdit.description || '',
          customerId: props.divisionToEdit.customerId,
        })
      } else if (props.customerIdForNew) {
        // If adding, set the customerId
        formState.customerId = props.customerIdForNew
      }
    } else {
      // Clear form when modal is closed, especially if it was for editing
      Object.assign(formState, initialFormState)
    }
  },
)

const resetForm = () => {
  formRef.value?.resetFields()
  // Reset formState to its initial state, then re-apply customerId if adding
  Object.assign(formState, initialFormState)
  if (props.visible && !props.divisionToEdit && props.customerIdForNew) {
    formState.customerId = props.customerIdForNew
  }
  isLoading.value = false
}

const handleOk = async () => {
  if (!formRef.value) return

  // If adding a new division, ensure customerIdForNew is valid (not null/undefined).
  // For editing, props.divisionToEdit.customerId is assumed to be part of the divisionToEdit object.
  if (
    !props.divisionToEdit &&
    (props.customerIdForNew === null || props.customerIdForNew === undefined)
  ) {
    console.error('Customer ID for new division is not provided. Cannot save.')
    // Optionally, inform the user with a message, e.g., using Ant Design's message component
    // import { message } from 'ant-design-vue';
    // message.error("Cannot add division: Customer ID is missing.");
    return
  }

  try {
    isLoading.value = true
    await formRef.value.validate()

    // formState should have id and customerId correctly populated by the watcher
    // based on props.divisionToEdit or props.customerIdForNew.
    const dataToSave = toRaw(formState)

    // The parent component (CustomerView) will handle the distinction between add/update.
    // It uses currentCustomerIdForDivisionAction for 'add' and data from the form for 'edit'.
    emit('save', dataToSave as CustomerDivision) // Parent decides add/update
  } catch (errorInfo) {
    console.log('Failed validation:', errorInfo)
    isLoading.value = false // Ensure isLoading is reset on validation failure.
  }
}

const handleCancel = () => {
  emit('close')
  // Resetting form on cancel is handled by the watch on props.visible
  // or can be explicitly done here if preferred.
  // resetForm();
}

defineExpose({ resetFormAndLoading: resetForm })
</script>
