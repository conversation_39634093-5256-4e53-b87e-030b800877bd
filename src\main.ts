import './assets/main.css'
import 'ant-design-vue/dist/reset.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createI18n } from 'vue-i18n'
import en from '@IVC/i18n/en.json'
import jp from '@IVC/i18n/jp.json'
import App from '@IVC/App.vue'
import router from '@IVC/router'
import Antd from 'ant-design-vue'
import './plugins/dayjs'

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  fallbackLocale: 'en',
  messages: {
    en,
    jp,
  },
})

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(i18n)
app.use(Antd)

app.mount('#app')
