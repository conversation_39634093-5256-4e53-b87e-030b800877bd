<script setup lang="ts">
import HelloWorld from '@IVC/components/HelloWorld.vue'
import { useSQLite } from '@IVC/hooks/useSQLite'
import { ref } from 'vue'

const { isLoading, error, executeQuery, isInitialized, initialize } = useSQLite()

const sqlQuery = ref('SELECT * FROM users')
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const queryResult = ref<any[]>([])
const columnNames = ref<string[]>([])
const queryError = ref<string | null>(null)

async function initializeDatabase() {
  try {
    queryError.value = null
    await initialize()
    console.log('Database initialized successfully')
  } catch (err) {
    console.error('Database initialization error:', err)
    queryError.value = err instanceof Error ? err.message : 'Database initialization failed'
  }
}

const exampleQueries = [
  // Basic queries
  { title: 'Select all', query: 'SELECT * FROM test_table' },
  { title: 'Select users', query: 'SELECT * FROM users' },
  { title: 'Select inbound', query: 'SELECT * FROM customer_inbound' },

  // Invoice Auto Calculation Queries
  {
    title: 'Get Auto Function Value',
    query: `SELECT
    w.code as workout_code,
    w.linked_auto_focus_code,
    afd.value,
    afd.year_month,
    afd.customer_id,
    afd.division_id
FROM workout w
LEFT JOIN auto_function_customer_detail afd
  ON w.linked_auto_focus_code = afd.auto_function_id
  AND afd.customer_id = 1
  AND afd.division_id = 2
  AND afd.year_month = '2025-04'
WHERE w.calculation_type = 'Auto'`,
  },

  // Linked Workout Queries
  {
    title: 'Get Linked Workouts',
    query: `SELECT
    w.id,
    w.code,
    w.name,
    w.calculation_type,
    w.linked_workout_id,
    lw.name as linked_workout_name,
    u.code as unit_code,
    u.name as unit_name,
    w.unit_price,
    w.unit_price_sign,
    w.unit_price_unit,
    w.unit_price_is_percentage,
    w.vat
FROM workout w
LEFT JOIN workout lw ON w.linked_workout_id = lw.id
LEFT JOIN units u ON w.unit_id = u.id
WHERE w.calculation_type = 'Link'`,
  },

  // Invoice Category & Charges
  {
    title: 'Get Invoice Categories & Items',
    query: `SELECT
    i.id as invoice_id,
    i.customer_id,
    i.customer_division_id,
    ii.category_id,
    c.name as category_name,
    COUNT(ii.id) as total_items,
    SUM(ii.amount) as category_total,
    SUM(ii.vat_amount) as category_vat
FROM invoices i
JOIN invoice_items ii ON i.id = ii.invoice_id
JOIN categories c ON ii.category_id = c.id
WHERE i.id = 'INV-1748932253411'
GROUP BY i.id, ii.category_id, c.name`,
  },

  {
    title: 'Get Invoice Items Detail',
    query: `SELECT
    ii.id,
    ii.workout_id,
    w.name as workout_name,
    w.calculation_type,
    u.code as unit_code,
    u.name as unit_name,
    ii.unit_price,
    ii.quantity,
    ii.amount,
    ii.vat,
    ii.vat_amount,
    d.code as debit_code,
    d.name as debit_code_name,
    ii.description,
    ii.status,
    ii.is_edited
FROM invoice_items ii
JOIN workout w ON ii.workout_id = w.id
JOIN units u ON ii.unit_id = u.id
LEFT JOIN debit_codes d ON ii.debit_code_id = d.id
WHERE ii.invoice_id = 'INV-1748932253411'`,
  },

  // Workout Customer Charges
  {
    title: 'Get Customer Monthly Charges',
    query: `SELECT
    wcc.id,
    wcc.customer_id,
    wcc.division_id,
    c.name as category_name,
    w.name as workout_name,
    w.calculation_type,
    wcc.year,
    wcc.month_1,
    wcc.month_2,
    wcc.month_3,
    wcc.month_4,
    wcc.month_5,
    wcc.month_6,
    wcc.month_7,
    wcc.month_8,
    wcc.month_9,
    wcc.month_10,
    wcc.month_11,
    wcc.month_12
FROM workout_customer_charges wcc
JOIN categories c ON wcc.category_id = c.id
JOIN workout w ON wcc.workout_id = w.id
WHERE wcc.customer_id = 2
AND wcc.year = 2025
ORDER BY c.name, w.name`,
  },

  // Insert examples
  {
    title: 'Insert Invoice',
    query: `INSERT INTO invoices (
    id,
    customer_id,
    customer_division_id,
    total_amount,
    status,
    notes,
    created_at,
    updated_at
  ) VALUES (
    'INV-2024-001',
    1,
    1,
    0,
    'DRAFT',
    'New invoice for January 2024',
    datetime('now'),
    datetime('now')
  )`,
  },

  {
    title: 'Insert Invoice Item',
    query: `INSERT INTO invoice_items (
    id,
    invoice_id,
    category_id,
    workout_id,
    unit_id,
    unit_price,
    quantity,
    amount,
    vat,
    vat_amount,
    debit_code_id,
    description,
    status,
    created_at,
    updated_at,
    is_edited
  ) VALUES (
    'ITEM-2024-001',
    'INV-2024-001',
    1,
    1,
    1,
    110000,
    6,
    660000,
    0.08,
    52800,
    1,
    'Fixed Space Charge',
    'DRAFT',
    datetime('now'),
    datetime('now'),
    0
  )`,
  },

  // Update & Delete
  {
    title: 'Update Invoice Status',
    query: `UPDATE invoices
SET status = 'PENDING',
    updated_at = datetime('now')
WHERE id = 'INV-2024-001'`,
  },
  {
    title: 'Delete Draft Invoice',
    query: `DELETE FROM invoices
WHERE id = 'INV-2024-001'
AND status = 'DRAFT'`,
  },
]

async function runQuery() {
  queryError.value = null
  queryResult.value = []
  columnNames.value = []

  try {
    console.log('Executing query:', sqlQuery.value)
    const result = await executeQuery(sqlQuery.value)
    const isSelect = sqlQuery.value.trim().toLowerCase().startsWith('select')

    console.log('Full result:', result)
    console.log('Result type:', result?.type)
    console.log('Result data:', result?.result)

    if (isSelect) {
      const rows = result?.result?.resultRows || []
      console.log('Rows from result:', rows)

      // Extract column names from SELECT query
      const selectQuery = sqlQuery.value.trim()
      console.log('Select Query:', selectQuery)
      if (selectQuery.toLowerCase().startsWith('select')) {
        try {
          // Normalize query by replacing newlines and multiple spaces with single space
          const normalizedQuery = selectQuery.replace(/\s+/g, ' ')
          console.log('Normalized Query:', normalizedQuery)

          // Get the part between SELECT and FROM, handling case insensitivity
          const fromIndex = normalizedQuery.toLowerCase().indexOf(' from ')
          if (fromIndex === -1) throw new Error('Invalid SELECT query: missing FROM clause')

          let columnsSection = normalizedQuery.substring(6, fromIndex).trim()
          console.log('Columns Section:', columnsSection)

          // Handle SELECT *
          if (columnsSection === '*') {
            const tableMatch = sqlQuery.value.match(/from\s+(\w+)/i)
            if (tableMatch) {
              const tableName = tableMatch[1]
              const pragmaResult = await executeQuery(`PRAGMA table_info(${tableName})`)
              const pragmaRows = pragmaResult?.result?.resultRows || []
              columnNames.value = pragmaRows.map((row: unknown[]) => String(row[1]))
            }
          } else {
            // Split by commas, but respect parentheses
            const columns: string[] = []
            let currentColumn = ''
            let parenthesesCount = 0

            for (let i = 0; i < columnsSection.length; i++) {
              const char = columnsSection[i]
              if (char === '(') {
                parenthesesCount++
                currentColumn += char
              } else if (char === ')') {
                parenthesesCount--
                currentColumn += char
              } else if (char === ',' && parenthesesCount === 0) {
                columns.push(currentColumn.trim())
                currentColumn = ''
              } else {
                currentColumn += char
              }
            }
            if (currentColumn.trim()) {
              columns.push(currentColumn.trim())
            }

            // Process each column
            columnNames.value = columns.map((col) => {
              col = col.trim()

              // Handle CASE statements
              if (col.toLowerCase().includes('case when')) {
                const asMatch = col.match(/as\s+["']?([^"',\s]+)["']?\s*$/i)
                return asMatch ? asMatch[1] : 'case_result'
              }

              // Handle aggregate functions
              if (col.toLowerCase().match(/^(count|sum|avg|min|max)\s*\(/i)) {
                const asMatch = col.match(/as\s+["']?([^"',\s]+)["']?\s*$/i)
                return asMatch ? asMatch[1] : col.split('(')[0].trim() + '_result'
              }

              // Handle column aliases
              if (col.toLowerCase().includes(' as ')) {
                return col
                  .split(/\s+as\s+/i)[1]
                  .replace(/["']/g, '')
                  .trim()
              }

              // Handle table aliases with column
              if (col.includes('.')) {
                const parts = col.split('.')
                return parts[parts.length - 1].trim()
              }

              return col
            })
          }
        } catch (e) {
          console.warn('Could not parse column names from SELECT query:', e)
          // Fallback: use first row keys if available
          if (rows.length > 0 && Array.isArray(rows[0])) {
            columnNames.value = Array(rows[0].length)
              .fill(0)
              .map((_, i) => `column_${i}`)
          }
        }
      }

      // Convert to objects
      if (rows.length > 0) {
        queryResult.value = rows.map((row: unknown[]) => {
          const obj: Record<string, unknown> = {}
          columnNames.value.forEach((colName, index) => {
            obj[colName] = row[index]
          })
          return obj
        })
      }

      console.log('Column Names:', JSON.stringify(columnNames.value))
      console.log('Final Query Result:', JSON.stringify(queryResult.value))
    } else {
      // After mutation, refresh test_table data
      const selectResult = await executeQuery('SELECT * FROM test_table')
      const rows = selectResult?.result?.resultRows || []

      if (rows.length > 0) {
        try {
          const pragmaResult = await executeQuery('PRAGMA table_info(test_table)')
          const pragmaRows = pragmaResult?.result?.resultRows || []
          columnNames.value = pragmaRows.map((row: unknown[]) => String(row[1]))
        } catch (e) {
          console.warn('Could not get table info:', e)
        }

        if (columnNames.value.length > 0) {
          queryResult.value = rows.map((row: unknown[]) => {
            const obj: Record<string, unknown> = {}
            columnNames.value.forEach((colName, index) => {
              obj[colName] = row[index]
            })
            return obj
          })
        } else {
          queryResult.value = rows.map((row: unknown[]) => {
            const obj: Record<string, unknown> = {}
            row.forEach((value, index) => {
              obj[`column_${index}`] = value
            })
            return obj
          })
        }
      }
    }
  } catch (err) {
    console.error('Query error:', err)
    queryError.value = err instanceof Error ? err.message : 'An error occurred'
  }
}
</script>

<template>
  <main>
    <div style="max-width: 80rem; padding: 1rem">
      <div style="margin-bottom: 24px">
        <h2 style="font-size: 1.5rem; line-height: 2rem; font-weight: 700">Store Usage Sample</h2>
        <HelloWorld />
      </div>
      <div>
        <h2 style="font-size: 1.5rem; line-height: 2rem; font-weight: 700">SQLite Playground</h2>

        <!-- Example queries -->
        <div style="margin-top: 1rem">
          <h3 style="font-size: 0.875rem; line-height: 1.25rem; font-weight: 500">
            Example Queries:
          </h3>
          <div style="display: flex; gap: 0.5rem; margin-top: 0.5rem; flex-wrap: wrap">
            <a-button
              style="
                padding-left: 0.75rem;
                padding-right: 0.75rem;
                padding-top: 0.25rem;
                padding-bottom: 0.25rem;
                font-size: 0.875rem;
                line-height: 1.25rem;
                border-radius: 9999px;
                background-color: #dc2626;
                color: white;
                border: none;
              "
              :disabled="isLoading"
              @click="initializeDatabase"
            >
              {{ isInitialized ? 'Initialized ✓' : 'Initialize' }}
            </a-button>
            <a-button
              v-for="example in exampleQueries"
              :key="example.title"
              style="
                padding-left: 0.75rem;
                padding-right: 0.75rem;
                padding-top: 0.25rem;
                padding-bottom: 0.25rem;
                font-size: 0.875rem;
                line-height: 1.25rem;
                border-radius: 9999px;
                background-color: #f3f4f6;
              "
              @click="sqlQuery = example.query"
            >
              {{ example.title }}
            </a-button>
          </div>
        </div>

        <!-- Query input -->
        <div style="margin-top: 1.5rem">
          <a-textarea
            v-model:value="sqlQuery"
            rows="4"
            style="
              width: 100%;
              padding-left: 1rem;
              padding-right: 1rem;
              padding-top: 0.75rem;
              padding-bottom: 0.75rem;
              border-radius: 0.5rem;
              font-family:
                ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',
                'Courier New', monospace;
              font-size: 0.875rem;
              line-height: 1.25rem;
            "
            :disabled="isLoading"
          />
          <a-button
            :disabled="isLoading"
            type="primary"
            style="margin-top: 0.5rem; border-radius: 0.5rem"
            @click="runQuery"
          >
            {{ isLoading ? 'Running...' : 'Run Query' }}
          </a-button>
        </div>

        <!-- Error display -->
        <div
          v-if="error || queryError"
          style="
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 0.5rem;
            background-color: #fef2f2;
            color: #dc2626;
          "
        >
          {{ error?.message || queryError }}
        </div>

        <!-- Results table -->
        <div v-if="queryResult.length > 0 || columnNames.length > 0" style="margin-top: 1rem">
          <h3 style="font-size: 1.125rem; line-height: 1.75rem; font-weight: 600">Results:</h3>
          <div style="margin-top: 0.5rem; overflow-x: auto">
            <table style="width: 100%; border-collapse: collapse; border: 1px solid #e5e7eb">
              <thead>
                <tr style="background-color: #f9fafb">
                  <th
                    v-for="column in columnNames.length > 0
                      ? columnNames
                      : queryResult.length > 0
                        ? Object.keys(queryResult[0])
                        : []"
                    :key="column"
                    style="
                      padding-left: 1rem;
                      padding-right: 1rem;
                      padding-top: 0.75rem;
                      padding-bottom: 0.75rem;
                      text-align: left;
                      font-weight: 600;
                      border: 1px solid #e5e7eb;
                      background-color: #f3f4f6;
                    "
                  >
                    {{ column }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr v-if="queryResult.length === 0 && columnNames.length > 0">
                  <td
                    :colspan="columnNames.length"
                    style="
                      padding-left: 1rem;
                      padding-right: 1rem;
                      padding-top: 1rem;
                      padding-bottom: 1rem;
                      text-align: center;
                      color: #6b7280;
                      font-style: italic;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    No data found
                  </td>
                </tr>
                <tr
                  v-else
                  v-for="(row, index) in queryResult"
                  :key="index"
                  style="border-bottom: 1px solid #e5e7eb"
                >
                  <td
                    v-for="column in Object.keys(row)"
                    :key="column"
                    style="
                      padding-left: 1rem;
                      padding-right: 1rem;
                      padding-top: 0.5rem;
                      padding-bottom: 0.5rem;
                      border: 1px solid #e5e7eb;
                    "
                  >
                    {{ row[column] }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </main>
</template>
