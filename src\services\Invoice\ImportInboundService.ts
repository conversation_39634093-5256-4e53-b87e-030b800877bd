/**
 * @fileoverview Import Inbound Service - Database operations for inbound invoice data import
 *
 * This service handles all database operations related to importing inbound invoice data
 * from Excel files. It provides comprehensive functionality for customer management,
 * product data processing, auto function calculations, and data validation.
 *
 * @module ImportInboundService
 * @version 1.0.0
 * <AUTHOR> Development Team
 * @since 2024
 *
 * Key Features:
 * - Customer and division data management
 * - Product data lookup with extended calculations
 * - Auto function processing for various metrics
 * - Column mapping management for Excel imports
 * - Complete data import workflow with error handling
 * - Database initialization and maintenance
 *
 * Dependencies:
 * - @IVC/hooks/useSQLite - SQLite database operations
 *
 * Database Tables:
 * - customers - Customer master data
 * - customer_divisions - Division information per customer
 * - imported_products - Product master data with specifications
 * - customer_inbound - Main inbound transaction records
 * - auto_function - Auto calculation function definitions
 * - auto_function_customers - Customer-specific auto functions
 * - auto_function_customer_detail - Auto function calculation results
 * - customer_import_mapping - Column mapping configurations
 */

import { useSQLite } from '@IVC/hooks/useSQLite'

const { executeQuery, initialize } = useSQLite()

// ============================================================================
// INTERFACE DEFINITIONS
// ============================================================================

/**
 * Customer entity interface
 * @interface Customer
 */
export interface Customer {
  /** Unique customer identifier */
  id: string
  /** Customer display name */
  name: string
}

/**
 * Division entity interface for customer organizational units
 * @interface Division
 */
export interface Division {
  /** Unique division identifier */
  id: string
  /** Division display name */
  name: string
  /** Division code for business operations */
  code: string
}

/**
 * Auto function entity interface for automated calculations
 * @interface AutoFunction
 */
export interface AutoFunction {
  /** Unique auto function identifier */
  id: number
  /** Function code for programmatic identification */
  code: string
  /** Human-readable function name */
  name: string
}

/**
 * Excel row data interface for import processing
 * @interface ExcelRowData
 */
export interface ExcelRowData {
  /** Dynamic properties based on Excel columns */
  [key: string]: string | number | boolean | null
  /** Row identifier key */
  key: number
}

// ============================================================================
// CUSTOMER AND DIVISION MANAGEMENT
// ============================================================================

/**
 * Loads all available customers from the database
 *
 * @returns {Promise<Customer[]>} Array of customer objects with id and name
 * @throws {Error} When database query fails or connection issues occur
 *
 * @example
 * ```typescript
 * try {
 *   const customers = await loadCustomers();
 *   console.log(`Found ${customers.length} customers`);
 * } catch (error) {
 *   console.error('Failed to load customers:', error);
 * }
 * ```
 *
 * @since 1.0.0
 */
export const loadCustomers = async (): Promise<Customer[]> => {
  try {
    const result = await executeQuery('SELECT id, name FROM customers')
    return (
      result?.result?.resultRows?.map((row: unknown[]) => ({
        id: String(row[0]),
        name: String(row[1]),
      })) || []
    )
  } catch (error) {
    console.error('Error loading customers:', error)
    throw new Error('Failed to load customers')
  }
}

/**
 * Loads divisions for a specific customer
 *
 * @param {string} customerId - The customer ID to load divisions for
 * @returns {Promise<Division[]>} Array of division objects belonging to the customer
 * @throws {Error} When database query fails or customer not found
 *
 * @example
 * ```typescript
 * try {
 *   const divisions = await loadDivisions('CUST001');
 *   divisions.forEach(div => {
 *     console.log(`Division: ${div.name} (${div.code})`);
 *   });
 * } catch (error) {
 *   console.error('Failed to load divisions:', error);
 * }
 * ```
 *
 * @since 1.0.0
 */
export const loadDivisions = async (customerId: string): Promise<Division[]> => {
  try {
    const result = await executeQuery(
      'SELECT id, code, name FROM customer_divisions WHERE customer_id = ? AND status = ?',
      [customerId, 'active'],
    )
    return (
      result?.result?.resultRows?.map((row: unknown[]) => ({
        id: String(row[0]),
        code: String(row[1]),
        name: String(row[2]),
      })) || []
    )
  } catch (error) {
    console.error('Error loading divisions:', error)
    throw new Error('Failed to load divisions')
  }
}

// ============================================================================
// PRODUCT DATA LOOKUP
// ============================================================================

/**
 * Retrieves basic product data by product code
 *
 * @param {string | number} productCode - The product code to search for
 * @returns {Promise<Object>} Product data object with specifications
 * @returns {string | number | null} returns.prodPpcNum - Product PPC number
 * @returns {string | number | null} returns.prodHrc1 - Product HRC1 specification
 * @returns {string | number | null} returns.prodPcm3 - Product PCM3 volume specification
 * @throws {Error} When database query fails
 *
 * @example
 * ```typescript
 * const productData = await getProductData('PROD001');
 * console.log('PPC Number:', productData.prodPpcNum);
 * console.log('HRC1:', productData.prodHrc1);
 * console.log('PCM3:', productData.prodPcm3);
 * ```
 *
 * @since 1.0.0
 */
export const getProductData = async (
  productCode: string | number,
): Promise<{
  prodPpcNum: string | number | null
  prodHrc1: string | number | null
  prodPcm3: string | number | null
}> => {
  try {
    const productQuery = `SELECT product_ppc_num, product_hrc1, product_pcm3 FROM imported_products WHERE product_code = ?`
    const productResults = await executeQuery(productQuery, [productCode])

    if (productResults?.result?.resultRows && productResults.result.resultRows.length > 0) {
      const productData = productResults.result.resultRows[0]
      return {
        prodPpcNum: (productData[0] as string | number) || null,
        prodHrc1: (productData[1] as string | number) || null,
        prodPcm3: (productData[2] as string | number) || null,
      }
    }

    return {
      prodPpcNum: null,
      prodHrc1: null,
      prodPcm3: null,
    }
  } catch (error) {
    console.error(`Error fetching product data for code ${productCode}:`, error)
    return {
      prodPpcNum: null,
      prodHrc1: null,
      prodPcm3: null,
    }
  }
}

/**
 * Retrieves extended product data with calculated values for Excel processing
 *
 * This function extends basic product data lookup by calculating additional
 * values needed for inbound processing, including AI values and inner master
 * calculations based on HRC1 specifications.
 *
 * @param {string} productCode - The product code to search for
 * @returns {Promise<Object>} Extended product data with calculated values
 * @returns {string | number | null} returns.prodPpcNum - Product PPC number
 * @returns {string | number | null} returns.prodHrc1 - Product HRC1 specification
 * @returns {string | number | null} returns.prodPcm3 - Product PCM3 volume specification
 * @returns {string | number} returns.aiValue - AI value derived from HRC1 or default '-'
 * @returns {number} returns.innerMaster - Inner master count extracted from HRC1 parentheses
 * @throws {Error} When database query fails or data processing error occurs
 *
 * @example
 * ```typescript
 * const extendedData = await getProductDataExtended('PROD001');
 * console.log('AI Value:', extendedData.aiValue);
 * console.log('Inner Master:', extendedData.innerMaster);
 * // If HRC1 is "Product(12)", innerMaster will be 12
 * ```
 *
 * @since 1.0.0
 */
export const getProductDataExtended = async (
  productCode: string,
): Promise<{
  prodPpcNum: string | number | null
  prodHrc1: string | number | null
  prodPcm3: string | number | null
  aiValue: string | number
  innerMaster: number
}> => {
  try {
    const productQuery = `SELECT product_ppc_num, product_hrc1, product_pcm3 FROM imported_products WHERE product_code = ?`
    const productResults = await executeQuery(productQuery, [productCode])

    let prodPpcNum: string | number | null = null
    let prodHrc1: string | number | null = null
    let prodPcm3: string | number | null = null
    let aiValue: string | number = '-'
    let innerMaster = 0

    if (productResults?.result?.resultRows && productResults.result.resultRows.length > 0) {
      const productData = productResults.result.resultRows[0]
      prodPpcNum = (productData[0] as string | number) || null
      prodHrc1 = (productData[1] as string | number) || null
      prodPcm3 = (productData[2] as string | number) || null

      // Set ai value to prod_hrc1 if found, otherwise keep default '-'
      aiValue = prodHrc1 || '-'

      // Calculate inner_master based on prod_hrc1 value
      if (prodHrc1) {
        const hrc1String = String(prodHrc1)
        // Extract any number within parentheses regardless of prefix text
        const numberMatch = hrc1String.match(/\((\d+)\)/)
        if (numberMatch) {
          innerMaster = parseInt(numberMatch[1], 10)
        }
        // For any value without parentheses or numbers, inner_master remains 0 (default)
      }
    }

    return { prodPpcNum, prodHrc1, prodPcm3, aiValue, innerMaster }
  } catch (error) {
    console.error('Error getting extended product data:', error)
    throw error
  }
}

// ============================================================================
// DATA IMPORT FUNCTIONS
// ============================================================================

/**
 * Inserts a new inbound record into the database
 *
 * @param {Array<string | number | null>} values - Array of values matching database column order
 * @param {{ [key: string]: string }} columnMapping - Column mapping from Excel to database columns
 * @returns {Promise<void>} Resolves when record is successfully inserted
 * @throws {Error} When database insertion fails
 *
 * @example
 * ```typescript
 * const recordValues = [
 *   'CUST001', 'DIV001', 'AS001', 1, 1, '2024-01-15',
 *   'PROD001', 'Product Name', 100, 'PPC001', 'HRC1(24)',
 *   1.5, 24, 10, 240, 2, 48, 10, 240, 360, 8640
 * ];
 * const columnMapping = { 'Col A': 'avr_as_num', 'Col B': 'avr_prod_cod' };
 * await insertInboundRecord(recordValues, columnMapping);
 * ```
 *
 * @since 1.0.0
 */
export const insertInboundRecord = async (
  values: (string | number | null)[],
  columnMapping: { [key: string]: string },
): Promise<void> => {
  // Get the database columns that are mapped
  const mappedDbColumns = Object.values(columnMapping)
  console.log('columnMapping:', columnMapping)
  // Add customer_id, division_id, product data columns, and calculated columns
  const allDbColumns = [
    'customer_id',
    'division_id',
    ...mappedDbColumns,
    'prod_ppc_num',
    'prod_hrc1',
    'ai',
    'inner_master',
    'carton',
    'pcs',
    'inner_carton',
    'inner_pcs',
    'total_carton',
    'total_pcs',
    'total_ai',
    'total_m3',
  ]

  // Create INSERT statement
  const placeholders = allDbColumns.map(() => '?').join(', ')
  const insertSQL = `INSERT INTO customer_inbound (${allDbColumns.join(', ')}) VALUES (${placeholders})`
  console.log('insertSQL :', insertSQL)
  console.log('insertSQL values:', values)

  await executeQuery(insertSQL, values)
}

// ============================================================================
// AUTO FUNCTION MANAGEMENT
// ============================================================================

/**
 * Retrieves auto functions configured for a specific customer
 *
 * @param {string} customerId - The customer ID to get auto functions for
 * @returns {Promise<AutoFunction[]>} Array of auto function configurations
 * @throws {Error} When database query fails
 *
 * @example
 * ```typescript
 * const autoFunctions = await getAutoFunctions('CUST001');
 * autoFunctions.forEach(func => {
 *   console.log(`Function: ${func.name} (${func.code})`);
 * });
 * ```
 *
 * @since 1.0.0
 */
export const getAutoFunctions = async (customerId: string): Promise<AutoFunction[]> => {
  try {
    const autoFunctionsQuery = `
      SELECT af.id, af.code, af.name
      FROM auto_function af
      INNER JOIN auto_function_customers afc ON af.id = afc.auto_function_id
      WHERE afc.customer_id = ?
    `

    const autoFunctionsResult = await executeQuery(autoFunctionsQuery, [customerId])

    return (
      autoFunctionsResult?.result?.resultRows?.map((row: unknown[]) => ({
        id: Number(row[0]),
        code: String(row[1]),
        name: String(row[2]),
      })) || []
    )
  } catch (error) {
    console.error('Error fetching auto functions:', error)
    throw error
  }
}

// ============================================================================
// CALCULATION FUNCTIONS FOR AUTO FUNCTIONS
// ============================================================================

/**
 * Calculates the sum of total cartons for a customer/division in a specific month
 *
 * @param {string} customerId - The customer ID to calculate for
 * @param {string | null} divisionId - Optional division ID for filtering
 * @param {string} yearMonth - Year-month in YYYY-MM format
 * @returns {Promise<number>} Sum of total cartons
 * @throws {Error} When database query fails
 *
 * @example
 * ```typescript
 * const totalCartons = await calculateSumCarton('CUST001', 'DIV001', '2024-01');
 * console.log(`Total cartons for January: ${totalCartons}`);
 * ```
 *
 * @since 1.0.0
 */
export const calculateSumCarton = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    let query = `
      SELECT COALESCE(SUM(total_carton), 0) as sum_carton
      FROM customer_inbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', avh_arv_ymd) = ?
    `

    const params = [customerId, yearMonth]

    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum carton query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumCarton = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum carton result: ${sumCarton}`)
      return sumCarton
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum carton:', error)
    throw error
  }
}

/**
 * Calculates the sum of total pieces for a customer/division in a specific month
 *
 * @param {string} customerId - The customer ID to calculate for
 * @param {string | null} divisionId - Optional division ID for filtering
 * @param {string} yearMonth - Year-month in YYYY-MM format
 * @returns {Promise<number>} Sum of total pieces
 * @throws {Error} When database query fails
 *
 * @example
 * ```typescript
 * const totalPcs = await calculateSumTotalPcs('CUST001', null, '2024-01');
 * console.log(`Total pieces: ${totalPcs}`);
 * ```
 *
 * @since 1.0.0
 */
export const calculateSumTotalPcs = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    let query = `
      SELECT COALESCE(SUM(total_pcs), 0) as sum_total_pcs
      FROM customer_inbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', avh_arv_ymd) = ?
    `

    const params = [customerId, yearMonth]

    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_pcs query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalPcs = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_pcs result: ${sumTotalPcs}`)
      return sumTotalPcs
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_pcs:', error)
    throw error
  }
}

/**
 * Calculates the sum of total AI values for a customer/division in a specific month
 *
 * @param {string} customerId - The customer ID to calculate for
 * @param {string | null} divisionId - Optional division ID for filtering
 * @param {string} yearMonth - Year-month in YYYY-MM format
 * @returns {Promise<number>} Sum of total AI values
 * @throws {Error} When database query fails
 *
 * @example
 * ```typescript
 * const totalAi = await calculateSumTotalAi('CUST001', 'DIV001', '2024-01');
 * console.log(`Total AI: ${totalAi}`);
 * ```
 *
 * @since 1.0.0
 */
export const calculateSumTotalAi = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    let query = `
      SELECT COALESCE(SUM(total_ai), 0) as sum_total_ai
      FROM customer_inbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', avh_arv_ymd) = ?
    `

    const params = [customerId, yearMonth]

    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_ai query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalAi = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_ai result: ${sumTotalAi}`)
      return sumTotalAi
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_ai:', error)
    throw error
  }
}

/**
 * Calculates the sum of total M3 (cubic meters) for a customer/division in a specific month
 *
 * @param {string} customerId - The customer ID to calculate for
 * @param {string | null} divisionId - Optional division ID for filtering
 * @param {string} yearMonth - Year-month in YYYY-MM format
 * @returns {Promise<number>} Sum of total M3 values
 * @throws {Error} When database query fails
 *
 * @example
 * ```typescript
 * const totalM3 = await calculateSumTotalM3('CUST001', null, '2024-01');
 * console.log(`Total cubic meters: ${totalM3}`);
 * ```
 *
 * @since 1.0.0
 */
export const calculateSumTotalM3 = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    let query = `
      SELECT COALESCE(SUM(total_m3), 0) as sum_total_m3
      FROM customer_inbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', avh_arv_ymd) = ?
    `

    const params = [customerId, yearMonth]

    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_m3 query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalM3 = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_m3 result: ${sumTotalM3}`)
      return sumTotalM3
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_m3:', error)
    throw error
  }
}

// ============================================================================
// AUTO FUNCTION PROCESSING
// ============================================================================

/**
 * Calculates and processes all auto functions for a customer/division/month combination
 *
 * This function orchestrates the entire auto function calculation process by:
 * 1. Retrieving configured auto functions for the customer
 * 2. Calculating values based on function codes
 * 3. Saving results to the database
 *
 * Supported auto function codes:
 * - N_A_IN_SUM_CARTON: Sum of total cartons
 * - N_A_IN_SUM_PCS: Sum of total pieces
 * - N_A_IN_SUM_AI: Sum of total AI values
 * - N_A_IN_SUM_M3: Sum of total cubic meters
 *
 * @param {string} customerId - The customer ID to process
 * @param {string | null} divisionId - Optional division ID for filtering
 * @param {string} yearMonth - Year-month in YYYY-MM format
 * @returns {Promise<boolean>} True if processing completed successfully
 * @throws {Error} When auto function processing fails
 *
 * @example
 * ```typescript
 * const success = await calculateAutoFunction('CUST001', 'DIV001', '2024-01');
 * if (success) {
 *   console.log('Auto functions calculated successfully');
 * }
 * ```
 *
 * @since 1.0.0
 */
export const calculateAutoFunction = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<boolean> => {
  try {
    if (!customerId) {
      console.warn('No customer selected for auto function calculation')
      return false
    }

    if (!yearMonth) {
      console.warn('No month selected for auto function calculation')
      return false
    }

    // Get auto functions for the selected customer
    const autoFunctions = await getAutoFunctions(customerId)

    if (autoFunctions.length === 0) {
      console.log('No auto functions found for selected customer')
      return true // Not an error if no auto functions are configured
    }

    console.log('Found auto functions:', autoFunctions)

    // Process each auto function
    for (const autoFunction of autoFunctions) {
      let calculatedValue = 0

      // Calculate value based on function code
      switch (autoFunction.code) {
        case 'N_A_IN_SUM_CARTON':
          calculatedValue = await calculateSumCarton(customerId, divisionId, yearMonth)
          break
        case 'N_A_IN_SUM_PCS':
          calculatedValue = await calculateSumTotalPcs(customerId, divisionId, yearMonth)
          break
        case 'N_A_IN_SUM_AI':
          calculatedValue = await calculateSumTotalAi(customerId, divisionId, yearMonth)
          break
        case 'N_A_IN_SUM_M3':
          calculatedValue = await calculateSumTotalM3(customerId, divisionId, yearMonth)
          break
        default:
          console.warn(`Unknown auto function code: ${autoFunction.code}`)
          continue
      }

      console.log(`Auto function ${autoFunction.code} calculated value: ${calculatedValue}`)

      // Save the calculated result
      await saveAutoFunctionResult(
        autoFunction.id,
        customerId,
        divisionId,
        yearMonth,
        calculatedValue,
      )
    }

    console.log('Auto function calculation completed successfully')
    return true
  } catch (error) {
    console.error('Error in auto function calculation:', error)
    throw error
  }
}

/**
 * Saves auto function calculation result to the database
 *
 * This function handles both INSERT and UPDATE operations based on whether
 * a record already exists for the given combination of parameters.
 *
 * @param {number} autoFunctionId - The auto function ID
 * @param {string} customerId - The customer ID
 * @param {string | null} divisionId - Optional division ID
 * @param {string} yearMonth - Year-month in YYYY-MM format
 * @param {number} value - The calculated value to save
 * @returns {Promise<void>} Resolves when result is saved
 * @throws {Error} When database operation fails
 *
 * @example
 * ```typescript
 * await saveAutoFunctionResult(1, 'CUST001', 'DIV001', '2024-01', 1500);
 * console.log('Auto function result saved');
 * ```
 *
 * @since 1.0.0
 */
export const saveAutoFunctionResult = async (
  autoFunctionId: number,
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
  value: number,
): Promise<void> => {
  try {
    // Check if record already exists
    let checkQuery = `
      SELECT id FROM auto_function_customer_detail
      WHERE auto_function_id = ? AND customer_id = ? AND year_month = ?
    `

    const checkParams = [autoFunctionId, customerId, yearMonth]

    // Add division filter to check query
    if (divisionId) {
      checkQuery += ' AND division_id = ?'
      checkParams.push(divisionId)
    } else {
      checkQuery += ' AND division_id IS NULL'
    }

    const existingRecord = await executeQuery(checkQuery, checkParams)
    const existingRecords = existingRecord?.result?.resultRows || []

    if (existingRecords.length > 0) {
      // Update existing record
      let updateQuery = `
        UPDATE auto_function_customer_detail
        SET value = ?, updated_at = CURRENT_TIMESTAMP
        WHERE auto_function_id = ? AND customer_id = ? AND year_month = ?
      `

      const updateParams = [value, autoFunctionId, customerId, yearMonth]

      if (divisionId) {
        updateQuery += ' AND division_id = ?'
        updateParams.push(divisionId)
      } else {
        updateQuery += ' AND division_id IS NULL'
      }

      await executeQuery(updateQuery, updateParams)
      console.log('Updated existing auto function detail record')
    } else {
      // Insert new record
      const insertQuery = `
        INSERT INTO auto_function_customer_detail (
          auto_function_id, customer_id, division_id, year_month, value, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `

      const insertParams = [autoFunctionId, customerId, divisionId, yearMonth, value]

      await executeQuery(insertQuery, insertParams)
      console.log('Inserted new auto function detail record')
    }
  } catch (error) {
    console.error('Error saving auto function result:', error)
    throw error
  }
}

// ============================================================================
// COLUMN MAPPING MANAGEMENT
// ============================================================================

/**
 * Saves column mapping configuration for Excel import
 *
 * This function stores the mapping between Excel columns and database columns
 * for a specific customer's inbound import configuration.
 *
 * @param {string} customerId - The customer ID to save mapping for
 * @param {Object} columnMapping - Object mapping Excel columns to database columns
 * @returns {Promise<void>} Resolves when mapping is saved
 * @throws {Error} When database operation fails
 *
 * @example
 * ```typescript
 * const mapping = {
 *   'Column A': 'customer_id',
 *   'Column B': 'avr_as_num',
 *   'Column C': 'avr_prod_cod'
 * };
 * await saveColumnMapping('CUST001', mapping);
 * ```
 *
 * @since 1.0.0
 */
export const saveColumnMapping = async (
  customerId: string,
  columnMapping: { [key: string]: string },
): Promise<void> => {
  try {
    await initialize()

    // Delete existing mappings for this customer and import type
    await executeQuery(
      `DELETE FROM customer_import_mapping WHERE customer_id = ? AND import_type = 'inbound'`,
      [customerId],
    )

    // Insert new mappings
    const insertPromises = Object.entries(columnMapping).map(([excelColumn, dbColumn]) => {
      return executeQuery(
        `INSERT INTO customer_import_mapping (customer_id, import_type, excel_column, db_column) VALUES (?, 'inbound', ?, ?)`,
        [customerId, excelColumn, dbColumn],
      )
    })

    await Promise.all(insertPromises)
  } catch (error) {
    console.error('Error saving mapping:', error)
    throw error
  }
}

/**
 * Loads saved column mapping configuration for a customer
 *
 * @param {string} customerId - The customer ID to load mapping for
 * @returns {Promise<Object>} Object containing Excel to database column mappings
 * @throws {Error} When database query fails
 *
 * @example
 * ```typescript
 * const savedMapping = await loadSavedMappings('CUST001');
 * console.log('Saved mappings:', savedMapping);
 * // Output: { 'Column A': 'customer_id', 'Column B': 'avr_as_num' }
 * ```
 *
 * @since 1.0.0
 */
export const loadSavedMappings = async (customerId: string): Promise<{ [key: string]: string }> => {
  try {
    await initialize()

    const result = await executeQuery(
      `SELECT excel_column, db_column FROM customer_import_mapping WHERE customer_id = ? AND import_type = 'inbound'`,
      [customerId],
    )

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const savedMappings: { [key: string]: string } = {}
      result.result.resultRows.forEach((row: unknown[]) => {
        const excelColumn = row[0] as string
        const dbColumn = row[1] as string
        savedMappings[excelColumn] = dbColumn
      })
      console.log('Loaded saved column mappings for inbound:', savedMappings)
      return savedMappings
    }

    return {}
  } catch (error) {
    console.error('Error loading saved mappings:', error)
    throw error
  }
}

// ============================================================================
// DATABASE INITIALIZATION
// ============================================================================

/**
 * Initializes the SQLite database connection
 *
 * @returns {Promise<void>} Resolves when database is initialized
 * @throws {Error} When database initialization fails
 *
 * @example
 * ```typescript
 * try {
 *   await initializeDatabase();
 *   console.log('Database initialized successfully');
 * } catch (error) {
 *   console.error('Database initialization failed:', error);
 * }
 * ```
 *
 * @since 1.0.0
 */
export const initializeDatabase = async (): Promise<void> => {
  try {
    await initialize()
  } catch (error) {
    console.error('Error initializing database:', error)
    throw new Error('Failed to initialize database')
  }
}

// ============================================================================
// STOCK MANAGEMENT
// ============================================================================

/**
 * Adds and updates stock quantities based on inbound operations
 *
 * This function handles stock management for inbound operations by either updating
 * existing stock records or creating new ones. It manages the temporary quantity
 * field (avr_rtpc_qty_temp) based on the incoming quantity (avr_rtpc_qty).
 *
 * @param {string} productCode - The product code to update stock for
 * @param {string} customerId - The customer ID for the stock record
 * @param {number} avrRtpcQty - The quantity to add to stock
 * @returns {Promise<boolean>} True if stock operation completed successfully
 * @throws {Error} When database operation fails
 *
 * @example
 * ```typescript
 * const success = await addStock('PROD001', 'CUST001', 100);
 * if (success) {
 *   console.log('Stock updated successfully');
 * }
 * ```
 *
 * @since 1.0.0
 */
export const addStock = async (
  productCode: string,
  customerId: string,
  avrRtpcQty: number,
): Promise<boolean> => {
  try {
    await initialize()

    // Step 2: Look up existing stock record
    const selectQuery = `
      SELECT id, avr_rtpc_qty_temp 
      FROM stock 
      WHERE product_code = ? 
        AND customer_id = ? 
        AND is_active = 1
    `

    const selectResult = await executeQuery(selectQuery, [productCode, customerId])

    if (selectResult?.result?.resultRows && selectResult.result.resultRows.length > 0) {
      // Step 3: Record exists - update it
      const existingRecord = selectResult.result.resultRows[0]
      const stockId = existingRecord[0]
      const currentTempQty = Number(existingRecord[1]) || 0
      const newTempQty = currentTempQty + avrRtpcQty

      const updateQuery = `
        UPDATE stock 
        SET avr_rtpc_qty_temp = ?, 
            operation_source = 'Inbound' ,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `

      await executeQuery(updateQuery, [newTempQty, stockId])
      console.log(
        `Stock updated for product ${productCode}: ${currentTempQty} + ${avrRtpcQty} = ${newTempQty}`,
      )
    } else {
      // Step 4: No record exists - insert new one
      const insertQuery = `
        INSERT INTO stock (
          product_code,
          customer_id,
          operation_source,
          avr_rtpc_qty_temp,
          is_active,
          created_at,
          updated_at
        ) VALUES (?, ?, 'Inbound', ?, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `

      await executeQuery(insertQuery, [productCode, customerId, avrRtpcQty])
      console.log(`New stock record created for product ${productCode} with quantity ${avrRtpcQty}`)
    }

    return true
  } catch (error) {
    console.error('Error in addStock:', error)
    throw error
  }
}

// ============================================================================
// COMPLETE DATA IMPORT PROCESS
// ============================================================================

/**
 * Processes complete data import from Excel to database
 *
 * This is the main orchestration function that handles the entire import workflow:
 * 1. Validates and processes each Excel row
 * 2. Looks up product data and calculates extended values
 * 3. Applies column mappings and data transformations
 * 4. Inserts records into the database
 * 5. Handles errors and provides detailed feedback
 *
 * @param {ExcelRowData[]} excelData - Array of Excel row data objects
 * @param {Object} columnMapping - Mapping of Excel columns to database columns
 * @param {string} customerId - The customer ID for the import
 * @param {string | null} divisionId - Optional division ID
 * @param {string} yearMonth - Year-month for the import data
 * @returns {Promise<Object>} Import result summary
 * @returns {number} returns.successCount - Number of successfully imported records
 * @returns {number} returns.errorCount - Number of failed records
 * @returns {string[]} returns.errors - Array of error messages
 * @throws {Error} When the import process fails critically
 *
 * @example
 * ```typescript
 * const result = await processDataImport(
 *   excelData,
 *   { 'Col A': 'customer_id', 'Col B': 'avr_as_num' },
 *   'CUST001',
 *   'DIV001',
 *   '2024-01'
 * );
 * console.log(`Imported: ${result.successCount}, Failed: ${result.errorCount}`);
 * ```
 *
 * @since 1.0.0
 */
export const processDataImport = async (
  excelData: ExcelRowData[],
  columnMapping: { [key: string]: string },
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<{ successCount: number; errorCount: number; errors: string[] }> => {
  try {
    let successCount = 0
    let errorCount = 0
    const errors: string[] = []

    for (let i = 0; i < excelData.length; i++) {
      try {
        const row = excelData[i]

        // Get the product code column mapping
        const productCodeColumn = Object.keys(columnMapping).find(
          (key) => columnMapping[key] === 'avr_prod_cod',
        )

        // Get extended product data if product code exists
        let prodPpcNum: string | number | null = null
        let prodHrc1: string | number | null = null
        let prodPcm3: string | number | null = null
        let aiValue: string | number = '-'
        let innerMaster = 0

        if (productCodeColumn && row[productCodeColumn]) {
          const productCode = row[productCodeColumn]
          const productData = await getProductDataExtended(String(productCode))
          prodPpcNum = productData.prodPpcNum
          prodHrc1 = productData.prodHrc1
          prodPcm3 = productData.prodPcm3
          aiValue = productData.aiValue
          innerMaster = productData.innerMaster
        }

        // Build values array based on column mapping
        const values: (string | number | null)[] = []
        // Map Excel columns to database columns and build values array

        // Add customer_id first
        values.push(customerId)

        // Add division_id (optional - can be null)
        values.push(divisionId)

        Object.keys(columnMapping).forEach((excelColumn) => {
          if (excelColumn && row[excelColumn] !== undefined) {
            const dbColumn = columnMapping[excelColumn]
            let value = row[excelColumn]

            // Handle date formatting for date columns
            if (dbColumn === 'avh_arv_ymd' && typeof value === 'string') {
              // Convert date format if needed (mm/dd/yyyy to yyyy-mm-dd)
              const dateMatch = value.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/)
              if (dateMatch) {
                const [, month, day, year] = dateMatch
                value = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
              }
            }

            values.push(value as string | number | null)
          } else {
            values.push(null) // Default value for unmapped columns
          }
        })

        // Calculate additional columns with error handling
        let carton: number = 0
        let pcs: number = 0
        let innerCarton: number = 0
        let innerPcs: number = 0
        let totalCarton: number = 0
        let totalPcs: number = 0
        let totalAi: number = 0
        let totalM3: number = 0

        try {
          // Get avr_rtpc_qty value for calculations
          const avrRtpcQtyColumn = Object.keys(columnMapping).find(
            (key) => columnMapping[key] === 'avr_rtpc_qty',
          )
          const avrRtpcQty =
            avrRtpcQtyColumn && row[avrRtpcQtyColumn] ? Number(row[avrRtpcQtyColumn]) : 0

          // Basic calculations: carton = avr_rtpc_qty/prod_ppc_num, pcs = avr_rtpc_qty - (carton * prod_ppc_num)
          if (prodPpcNum && Number(prodPpcNum) > 0 && avrRtpcQty > 0) {
            carton = Math.floor(avrRtpcQty / Number(prodPpcNum))
            pcs = avrRtpcQty - carton * Number(prodPpcNum)
          }

          // Conditional calculations based on prod_hrc1 containing "INNER"
          if (prodHrc1 && String(prodHrc1).toUpperCase().includes('INNER')) {
            if (innerMaster > 0 && carton > 0) {
              innerCarton = Math.floor(carton / innerMaster)
              innerPcs = carton - innerCarton * innerMaster
            }
            totalCarton = innerCarton
            totalPcs =
              innerCarton * innerMaster * Number(prodPpcNum || 0) +
              innerPcs * Number(prodPpcNum || 0) +
              pcs
          } else {
            totalCarton = carton
            totalPcs = avrRtpcQty
          }

          // Special calculation for total_ai based on ai column value being "AI"
          if (aiValue && String(aiValue).toUpperCase() === 'AI') {
            totalAi = totalPcs
          }

          // Calculate total_m3 = avr_rtpc_qty * product_pcm3
          if (avrRtpcQty > 0 && prodPcm3 && Number(prodPcm3) > 0) {
            totalM3 = avrRtpcQty * Number(prodPcm3)
          } else {
            totalM3 = 0
          }
        } catch (calcError) {
          console.warn(`Warning: Error in calculations for row ${i + 1}:`, calcError)
          // Keep default values (0) if calculation fails
        }
        // Add product data columns
        values.push(prodPpcNum)
        values.push(prodHrc1)
        values.push(aiValue)
        values.push(innerMaster)

        // Add calculated columns
        values.push(carton)
        values.push(pcs)
        values.push(innerCarton)
        values.push(innerPcs)
        values.push(totalCarton)
        values.push(totalPcs)
        values.push(totalAi)
        values.push(totalM3)

        // Insert the record
        await insertInboundRecord(values, columnMapping)

        console.log('import inbound successfully')

        //add Stock
        if (productCodeColumn && row[productCodeColumn]) {
          const productCode = String(row[productCodeColumn])
          const qtyColumn = Object.keys(columnMapping).find(
            (key) => columnMapping[key] === 'avr_rtpc_qty',
          )
          const avrRtpcQty = Number(row[qtyColumn || ''] || 0)

          const successStock = await addStock(productCode, customerId, avrRtpcQty)
          if (successStock) {
            console.log('Stock updated successfully')
          }
        }

        successCount++
      } catch (error) {
        console.error(`Error processing row ${i + 1}:`, error)
        errorCount++
        errors.push(`Row ${i + 1}: ${error instanceof Error ? error.message : String(error)}`)
      }
    }

    return { successCount, errorCount, errors }
  } catch (error) {
    console.error('Error in processDataImport:', error)
    throw error
  }
}
