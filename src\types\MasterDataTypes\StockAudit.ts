// filepath: d:\02. Projects\06. NPE\Source\npe\src\types\MasterDataTypes\StockAudit.ts

export type StockOperationType = 'CREATE' | 'UPDATE' | 'DELETE'
export type StockOperationSource = 'MANUAL' | 'IMPORT' | 'CALCULATION' | 'SYSTEM'

export interface StockAuditBatch {
  id: number
  operationType: StockOperationType
  operationSource: StockOperationSource
  performedByUserId?: number
  performedAt: string
  notes?: string
  affectedCount: number
}

export interface StockAuditHistory {
  id: number
  auditBatchId: number
  stockId?: number
  customerId: number
  productCode: string
  operationType: StockOperationType
  fieldName?: string
  oldValue?: string
  newValue?: string
  createdAt: string
}

export interface StockAuditEntry {
  batch: StockAuditBatch
  changes: StockAuditHistory[]
}

export interface StockFieldChange {
  fieldName: string
  oldValue: string | number | null
  newValue: string | number | null
}

export interface CreateStockAuditBatchData {
  operationType: StockOperationType
  operationSource: StockOperationSource
  performedByUserId?: number
  notes?: string
  affectedCount?: number
}

export interface CreateStockAuditHistoryData {
  auditBatchId: number
  stockId?: number
  operationType: StockOperationType
  fieldName?: string
  oldValue?: string | number | null
  newValue?: string | number | null
}
