<template>
  <a-dropdown :trigger="['click']" placement="bottomRight">
    <div
      style="
        display: flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
        padding: 6px 10px;
        border-radius: 8px;
        transition: all 0.2s;
        height: 42px;
      "
      class="user-dropdown-trigger"
    >
      <a-avatar :style="{ backgroundColor: getRoleColor(user?.role || 'user') }" size="default">
        {{ getRoleInitials(user?.role || 'user') }}
      </a-avatar>
      <div
        style="
          display: flex;
          flex-direction: column;
          justify-content: center;
          min-width: 0;
          flex: 1;
          line-height: 1.2;
        "
      >
        <span
          style="
            font-weight: 500;
            color: #262626;
            font-size: 13px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 140px;
            margin-bottom: 1px;
          "
        >
          {{ user?.name || 'User' }}
        </span>
        <span
          style="
            font-size: 11px;
            color: #8c8c8c;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 140px;
          "
        >
          {{ user?.email || '<EMAIL>' }}
        </span>
      </div>
      <svg
        width="10"
        height="10"
        viewBox="0 0 12 12"
        fill="none"
        style="flex-shrink: 0; opacity: 0.6"
      >
        <path
          d="M3 4.5L6 7.5L9 4.5"
          stroke="currentColor"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </div>
    <template #overlay>
      <a-menu @click="handleMenuClick" style="min-width: 160px">
        <a-menu-item key="changePassword">
          <div style="display: flex; align-items: center; gap: 8px">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path
                d="M6 10V8C6 5.79086 7.79086 4 10 4H14C16.2091 4 18 5.79086 18 8V10"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
              />
              <rect
                x="4"
                y="10"
                width="16"
                height="10"
                rx="2"
                stroke="currentColor"
                stroke-width="2"
              />
              <circle cx="12" cy="15" r="1" fill="currentColor" />
            </svg>
            <span>Change Password</span>
          </div>
        </a-menu-item>
        <a-menu-divider />
        <a-menu-item key="logout">
          <div style="display: flex; align-items: center; gap: 8px; color: #ff4d4f">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path
                d="M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M16 17L21 12L16 7"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M21 12H9"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <span>Logout</span>
          </div>
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
</template>

<script setup lang="ts">
interface User {
  name?: string
  email?: string
  role?: 'admin' | 'manager' | 'user'
}

interface Props {
  user?: User
}

interface Emits {
  (e: 'changePassword'): void
  (e: 'logout'): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

// Get role color for avatar
const getRoleColor = (role: string): string => {
  const colors: Record<string, string> = {
    admin: '#ff4d4f', // Red for admin
    manager: '#fa8c16', // Orange for manager
    user: '#1890ff', // Blue for user
  }
  return colors[role.toLowerCase()] || '#1890ff'
}

// Get role initials for avatar
const getRoleInitials = (role: string): string => {
  const roleInitials: Record<string, string> = {
    admin: 'AD',
    manager: 'MG',
    user: 'US',
  }
  return roleInitials[role.toLowerCase()] || 'US'
}

// Handle menu click
const handleMenuClick = ({ key }: { key: string }) => {
  if (key === 'logout') {
    emit('logout')
  } else if (key === 'changePassword') {
    emit('changePassword')
  }
}
</script>

<style scoped>
.user-dropdown-trigger {
  max-width: 220px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.user-dropdown-trigger:hover {
  background: rgba(0, 0, 0, 0.02);
  border-color: rgba(0, 0, 0, 0.12);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

.user-dropdown-trigger:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.user-dropdown-trigger svg {
  color: #8c8c8c;
  transition:
    transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    color 0.2s;
  flex-shrink: 0;
}

.user-dropdown-trigger:hover svg {
  color: #1890ff;
}

/* Dropdown arrow animation */
:deep(.ant-dropdown-open) .user-dropdown-trigger svg {
  transform: rotate(180deg);
}

/* Avatar styling - following Gravity UI principles */
:deep(.ant-avatar) {
  font-weight: 600;
  font-size: 12px;
  flex-shrink: 0;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

/* Text styling improvements */
.user-dropdown-trigger span {
  letter-spacing: -0.01em;
}

/* Focus states for accessibility */
.user-dropdown-trigger:focus-visible {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .user-dropdown-trigger {
    max-width: 170px;
    height: 38px;
    padding: 5px 8px;
    gap: 8px;
  }

  .user-dropdown-trigger span:first-child {
    font-size: 12px;
  }

  .user-dropdown-trigger span:last-child {
    font-size: 10px;
  }
}
</style>
