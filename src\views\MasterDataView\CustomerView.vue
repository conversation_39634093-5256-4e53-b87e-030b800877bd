<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useSQLite } from '@IVC/hooks/useSQLite' // Assuming ProjectOutlined is available or choose another
import CustomerForm from '@IVC/components/dialogs/CustomerForm.vue'
import { Modal, message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import ActionButtons from '@IVC/components/ActionButtons.vue'
import CustomerSettingsModal from '@IVC/components/dialogs/CustomerSettingsModal.vue' // Import the new modal
import CustomerDivisionForm from '@IVC/components/dialogs/CustomerDivisionForm.vue' // Ensure this path is correct
import WorkoutChargeModalHierarchical from '../../components/dialogs/WorkoutChargeModalHierarchical.vue'
import { type Customer, type CustomerDivision } from '@IVC/types/MasterDataTypes/Customer'
import {
  getAllCustomers,
  addCustomer,
  updateCustomer,
  deleteCustomer,
  addCustomerDivision,
  getCustomerDivisionsByCustomerId,
  updateCustomerDivision,
  deleteCustomerDivision,
} from '@IVC/services/master-data/customer/index'
import type { EntityStatus } from '@IVC/types/common'

const { isLoading } = useSQLite()

const customers = ref<Customer[] | null>([])
const isModalVisible = ref(false)
const isDivisionModalVisible = ref(false) // Single modal for add/edit division
const divisionModalMode = ref<'add' | 'edit'>('add')
const editingCustomer = ref<Customer | null>(null)
const currentDivisionForEditing = ref<CustomerDivision | null>(null)
const customerFormRef = ref<InstanceType<typeof CustomerForm> | null>(null)

const isSettingsModalVisible = ref(false)
const customerForSettings = ref<Customer | null>(null)

// Workout Charge Modal state
const isWorkoutChargeModalVisible = ref(false)
const workoutChargeCustomer = ref<Customer | null>(null)
const workoutChargeDivision = ref<CustomerDivision | null>(null)
const workoutChargeExistingData = ref<any>(null) // Store existing data for edit mode

// State for the new Manage Divisions Modal
const managingDivisionsForCustomer = ref<Customer | null>(null)
const divisionsInManagementModal = ref<CustomerDivision[]>([])
const wasDivisionModalOpenedFromManager = ref(false) // To track origin of division modal
const customerDivisionFormRef = ref<InstanceType<typeof CustomerDivisionForm> | null>(null)

const columns = [
  {
    title: 'Code',
    dataIndex: 'code',
    key: 'code',
    sorter: (a: Customer, b: Customer) => a.code.localeCompare(b.code),
  },
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
    sorter: (a: Customer, b: Customer) => a.name.localeCompare(b.name),
  },
  { title: 'Address', dataIndex: 'address', key: 'address' },
  { title: 'Tax Number', dataIndex: 'taxNumber', key: 'tax_number' },
  { title: 'Phone', dataIndex: 'phone', key: 'phone' },
  {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  { title: 'Action', key: 'action', width: 100 },
]

const divisionColumns = [
  {
    title: 'Division Code',
    dataIndex: 'code',
    key: 'code',
    sorter: (a: CustomerDivision, b: CustomerDivision) => a.code.localeCompare(b.code),
  },
  {
    title: 'Division Name',
    dataIndex: 'name',
    key: 'name',
    sorter: (a: CustomerDivision, b: CustomerDivision) => a.name.localeCompare(b.name),
  },
  { title: 'Description', dataIndex: 'description', key: 'description' },
  {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  { title: 'Action', key: 'divisionAction', width: 100 },
  // Add more columns for division details as needed
]

const fetchCustomers = async () => {
  customers.value = null
  try {
    const fetchedCustomers = await getAllCustomers()
    // For each customer, fetch their divisions
    const customersWithDivisions = await Promise.all(
      fetchedCustomers.map(async (customer) => {
        const divisions = await getCustomerDivisionsByCustomerId(customer.id)
        return { ...customer, divisions: divisions || [] } // Ensure divisions is an array
      }),
    )
    customers.value = customersWithDivisions
  } catch (err) {
    message.error(`Failed to load customers: ${(err as Error).message}`)
    console.error(err)
  }
}

// Helper to refresh divisions for a specific customer (used by expanded row and potentially management modal)
const refreshDivisionsForCustomer = async (customerId: number) => {
  try {
    const updatedDivisions = await getCustomerDivisionsByCustomerId(customerId)
    const customerIndex = customers.value?.findIndex((c) => c.id === customerId)
    if (customers.value && customerIndex !== undefined && customerIndex > -1) {
      customers.value[customerIndex].divisions = updatedDivisions || []
    }
  } catch (err) {
    message.error(
      `Failed to refresh divisions for customer ${customerId}: ${(err as Error).message}`,
    )
    console.error(err)
  }
}

onMounted(() => {
  fetchCustomers()
})

const showAddModal = () => {
  editingCustomer.value = null
  isModalVisible.value = true
}

const showEditModal = (customer: Customer) => {
  editingCustomer.value = { ...customer } // Create a copy to avoid direct mutation
  isModalVisible.value = true
}

const handleModalClose = () => {
  isModalVisible.value = false
  editingCustomer.value = null
  customerFormRef.value?.resetFormAndLoading()
}

const handleModalSave = async (customerData: Partial<Customer>) => {
  try {
    if (customerData.id) {
      // Editing existing customer
      await updateCustomer(customerData) // Cast needed as id is present
      message.success('Customer updated successfully!')
    } else {
      // Adding new customer
      await addCustomer(customerData)
      message.success('Customer added successfully!')
    }
    await fetchCustomers() // Refresh the list
    handleModalClose()
  } catch (err) {
    message.error(`Failed to save customer: ${(err as Error).message}`)
    console.error(err)
  } finally {
    customerFormRef.value?.resetFormAndLoading() // Ensure loading state is reset
  }
}

const confirmDeleteCustomer = (id: number) => {
  Modal.confirm({
    title: 'Are you sure you want to delete this customer?',
    content: 'This action will permanently delete the customer data.',
    okText: 'Delete',
    okType: 'danger',
    cancelText: 'Cancel',
    async onOk() {
      await handleDelete(id)
    },
  })
}

const currentCustomerIdForDivisionAction = ref<number | null>(null)

const openAddDivisionModal = (customerId: number) => {
  divisionModalMode.value = 'add'
  currentDivisionForEditing.value = null
  currentCustomerIdForDivisionAction.value = customerId
  // wasDivisionModalOpenedFromManager is set by the caller if needed
  isDivisionModalVisible.value = true
}

const openEditDivisionModal = (division: CustomerDivision) => {
  divisionModalMode.value = 'edit'
  currentDivisionForEditing.value = { ...division } // Create a copy
  currentCustomerIdForDivisionAction.value = division.customerId! // Assuming customerId is always present

  // If this is called from within the Manage Divisions modal, hide it first
  if (managingDivisionsForCustomer.value?.id === division.customerId) {
    // isManageDivisionsModalVisible.value = false; // No longer hide the parent modal
    wasDivisionModalOpenedFromManager.value = true
  } else {
    wasDivisionModalOpenedFromManager.value = false // Reset if opened from elsewhere (e.g., expanded row)
  }

  isDivisionModalVisible.value = true
}

const refreshDivisionsForManagementModal = async () => {
  if (managingDivisionsForCustomer.value?.id) {
    try {
      const fetchedDivisions = await getCustomerDivisionsByCustomerId(
        managingDivisionsForCustomer.value.id,
      )
      divisionsInManagementModal.value = fetchedDivisions || []
      // Also ensure the main customer list's divisions are up-to-date
      // This might be redundant if refreshDivisionsForCustomer is also called, but ensures consistency
      const customerInMainList = customers.value?.find(
        (c) => c.id === managingDivisionsForCustomer.value!.id,
      )
      if (customerInMainList) {
        customerInMainList.divisions = divisionsInManagementModal.value
      }
    } catch (err) {
      message.error(`Failed to refresh divisions in modal: ${(err as Error).message}`)
    }
  }
}

const handleSaveDivision = async (divisionData: CustomerDivision) => {
  try {
    if (divisionModalMode.value === 'edit' && currentDivisionForEditing.value?.id) {
      await updateCustomerDivision({ ...divisionData, id: currentDivisionForEditing.value.id })
      message.success('Customer division updated successfully!')
    } else if (divisionModalMode.value === 'add' && currentCustomerIdForDivisionAction.value) {
      // The divisionData from form might not have customerId if it's a new one,
      // ensure it's added from currentCustomerIdForDivisionAction
      const dataToSave = { ...divisionData, customerId: currentCustomerIdForDivisionAction.value }
      await addCustomerDivision(dataToSave)
      message.success('Customer division added successfully!')
    }
    isDivisionModalVisible.value = false
    // await fetchCustomers(); // Avoid full refresh
    const customerIdToUpdate =
      divisionModalMode.value === 'edit'
        ? currentDivisionForEditing.value?.customerId
        : currentCustomerIdForDivisionAction.value

    if (customerIdToUpdate) {
      await refreshDivisionsForCustomer(customerIdToUpdate)

      if (wasDivisionModalOpenedFromManager.value) {
        // If division modal was opened from manager, refresh manager and re-show it
        if (managingDivisionsForCustomer.value?.id === customerIdToUpdate) {
          await refreshDivisionsForManagementModal() // Refresh content for manager modal
        }
        // isManageDivisionsModalVisible.value = true; // No longer needed as parent modal was not hidden
        wasDivisionModalOpenedFromManager.value = false // Reset flag
      } else if (managingDivisionsForCustomer.value?.id === customerIdToUpdate) {
        // If manager modal is open for the same customer but wasn't the direct trigger
        await refreshDivisionsForManagementModal() // Just refresh its content
      }
    }
  } catch (err) {
    message.error(`Failed to save customer division: ${(err as Error).message}`)
    console.error(err)
  } finally {
    customerDivisionFormRef.value?.resetFormAndLoading() // Ensure loading state is reset
  }
}

const handleCloseDivisionModal = () => {
  isDivisionModalVisible.value = false
  currentDivisionForEditing.value = null
  currentCustomerIdForDivisionAction.value = null
  customerDivisionFormRef.value?.resetFormAndLoading()

  if (wasDivisionModalOpenedFromManager.value) {
    // If the division form was cancelled after being opened from the manager,
    // re-show the manager modal.
    if (managingDivisionsForCustomer.value) {
      // Ensure there's a customer context for the manager modal
      // isManageDivisionsModalVisible.value = true; // No longer needed as parent modal was not hidden
    }
    wasDivisionModalOpenedFromManager.value = false // Reset flag
  }
}

const confirmDeleteCustomerDivision = (division: CustomerDivision) => {
  Modal.confirm({
    title: 'Are you sure you want to delete this division?',
    content: `This action will permanently delete the division "${division.name}".`,
    okText: 'Delete',
    okType: 'danger',
    cancelText: 'Cancel',
    async onOk() {
      try {
        await deleteCustomerDivision(division.id)
        message.success('Customer division deleted successfully!')
        // await fetchCustomers(); // Avoid full refresh
        if (division.customerId) {
          await refreshDivisionsForCustomer(division.customerId)
          // If manager modal is open for this customer, refresh it.
          // No need to check wasDivisionModalOpenedFromManager here, as this is a direct delete action.
          // The manager modal might be open independently.
          if (managingDivisionsForCustomer.value?.id === division.customerId) {
            await refreshDivisionsForManagementModal()
          } else if (wasDivisionModalOpenedFromManager.value) {
            // This case should ideally not happen if delete is only from manager or expanded row directly
          }
        }
      } catch (err) {
        message.error(`Failed to delete customer division: ${(err as Error).message}`)
        console.error(err)
      }
    },
  })
}

const handleDelete = async (id: number) => {
  try {
    await deleteCustomer(id)
    message.success('Customer deleted successfully!')
    await fetchCustomers() // Refresh the list
  } catch (err) {
    const errorMessage = (err as Error).message || 'Unknown error'
    message.error(`Failed to delete customer: ${errorMessage}`)
    console.error(err)
  }
}

// --- Methods for Status Updates ---
const handleSetCustomerStatus = async (customer: Customer, newStatus: EntityStatus) => {
  try {
    await updateCustomer({ id: customer.id, status: newStatus })
    message.success(`Customer "${customer.name}" status updated to ${newStatus}.`)
    await fetchCustomers() // Refresh the list to show updated status
  } catch (err) {
    message.error(
      `Failed to update status for customer "${customer.name}": ${(err as Error).message}`,
    )
    console.error(err)
  }
}

const handleSetDivisionStatus = async (division: CustomerDivision, newStatus: EntityStatus) => {
  try {
    await updateCustomerDivision({ ...division, status: newStatus })
    message.success(`Division "${division.name}" status updated to ${newStatus}.`)
    if (division.customerId) {
      await refreshDivisionsForCustomer(division.customerId)
      if (managingDivisionsForCustomer.value?.id === division.customerId) {
        await refreshDivisionsForManagementModal()
      }
    }
  } catch (err) {
    message.error(
      `Failed to update status for division "${division.name}": ${(err as Error).message}`,
    )
    console.error(err)
  }
}

// --- Method for Customer Settings Action ---
const handleCustomerSettings = (customer: Customer) => {
  customerForSettings.value = customer
  isSettingsModalVisible.value = true
}

const handleCloseSettingsModal = () => {
  isSettingsModalVisible.value = false
  customerForSettings.value = null
}

// --- Methods for Workout Charge Modal ---
const handleWorkoutCharge = async (customer: Customer, division: CustomerDivision) => {
  workoutChargeCustomer.value = customer
  workoutChargeDivision.value = division

  // Check if data exists for this customer/division
  try {
    const { getWorkoutCustomerChargeByCustomerDivision } = await import(
      '../../services/master-data/workoutCustomerCharge/workoutCustomerChargeService'
    )
    const existingData = await getWorkoutCustomerChargeByCustomerDivision(customer.id, division.id)

    if (existingData) {
      console.log('Existing workout charge data found, opening edit modal directly')
      // Set existing data and open modal in edit mode
      workoutChargeExistingData.value = existingData
      isWorkoutChargeModalVisible.value = true
    } else {
      console.log('No workout charge data found, opening create modal')
      // Clear existing data and open modal in create mode
      workoutChargeExistingData.value = null
      isWorkoutChargeModalVisible.value = true
    }
  } catch (error) {
    console.error('Error checking workout charge data:', error)
    // Fallback to opening modal
    isWorkoutChargeModalVisible.value = true
  }
}

const handleWorkoutChargeForCustomer = async (customer: Customer) => {
  // For customers without divisions, use null division ID
  workoutChargeCustomer.value = customer
  workoutChargeDivision.value = {
    id: null,
    name: 'No Division',
    code: 'NO_DIV',
    customer_id: customer.id,
    status: 'active',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }

  // Check if data exists for this customer (no division)
  try {
    const { getWorkoutCustomerChargeByCustomerDivision } = await import(
      '../../services/master-data/workoutCustomerCharge/workoutCustomerChargeService'
    )
    const existingData = await getWorkoutCustomerChargeByCustomerDivision(customer.id, null)

    if (existingData) {
      console.log('Existing workout charge data found, opening edit modal directly')
      // Set existing data and open modal in edit mode
      workoutChargeExistingData.value = existingData
      isWorkoutChargeModalVisible.value = true
    } else {
      console.log('No workout charge data found, opening create modal')
      // Clear existing data and open modal in create mode
      workoutChargeExistingData.value = null
      isWorkoutChargeModalVisible.value = true
    }
  } catch (error) {
    console.error('Error checking workout charge data:', error)
    // Fallback to opening modal
    isWorkoutChargeModalVisible.value = true
  }
}

const handleCloseWorkoutChargeModal = () => {
  isWorkoutChargeModalVisible.value = false
  workoutChargeCustomer.value = null
  workoutChargeDivision.value = null
  workoutChargeExistingData.value = null
}

const handleWorkoutChargeSave = (savedData: any) => {
  console.log('Workout charge saved:', savedData)
  // Update existing data for future edits
  workoutChargeExistingData.value = savedData
  // Refresh customer list if needed
  fetchCustomers()
}

// --- Methods for Manage Divisions Modal ---
const openManageDivisionsModal = async (customer: Customer) => {
  managingDivisionsForCustomer.value = customer
  if (customer.id) {
    try {
      // Use divisions already fetched with the customer object if available and up-to-date
      // or fetch fresh
      const customerFromList = customers.value?.find((c) => c.id === customer.id)
      if (customerFromList && customerFromList.divisions) {
        divisionsInManagementModal.value = [...customerFromList.divisions]
      } else {
        divisionsInManagementModal.value =
          (await getCustomerDivisionsByCustomerId(customer.id)) || []
      }
    } catch {
      message.error('Failed to load divisions for management.')
      divisionsInManagementModal.value = []
    }
  }
}
</script>

<template>
  <div class="header-container-master">
    <a-page-header title="Customer Management" sub-title="Manage your customer details">
      <template #extra>
        <a-button type="primary" @click="showAddModal"> <PlusOutlined /> Add Customer </a-button>
      </template>
    </a-page-header>

    <a-table :columns="columns" :data-source="customers" :loading="isLoading" row-key="id">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <ActionButtons
            name="Customer"
            :record-status="record.status"
            :show-add-customer-divisions="true"
            :show-manage-divisions="true"
            :show-setting-button="true"
            :show-workout-charge="!record.divisions || record.divisions.length === 0"
            @edit="showEditModal(record)"
            @delete="confirmDeleteCustomer(record.id)"
            @add-customer-divisions="openAddDivisionModal(record.id)"
            @set-status="(newStatus: EntityStatus) => handleSetCustomerStatus(record, newStatus)"
            @manage-divisions="openManageDivisionsModal(record)"
            @setting="handleCustomerSettings(record)"
            @workout-charge="handleWorkoutChargeForCustomer(record)"
          />
        </template>
        <template v-else-if="column.key === 'status'">
          <a-tag
            :color="
              record.status === 'active' ? 'green' : record.status === 'inactive' ? 'red' : 'blue'
            "
          >
            {{ record.status.toUpperCase() }}
          </a-tag>
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <div class="expanded-row-content">
          <a-table
            v-if="record.divisions && record.divisions.length > 0"
            :columns="divisionColumns"
            :data-source="record.divisions"
            :pagination="false"
            row-key="id"
            size="small"
          >
            <template #bodyCell="{ column: divColumn, record: divRecord }">
              <template v-if="divColumn.key === 'divisionAction'">
                <ActionButtons
                  name="Division"
                  :record-status="divRecord.status"
                  :show-add-customer-divisions="false"
                  :show-edit="true"
                  :show-workout-charge="true"
                  @edit="openEditDivisionModal(divRecord)"
                  @delete="confirmDeleteCustomerDivision(divRecord)"
                  @workout-charge="handleWorkoutCharge(record, divRecord)"
                  @set-status="
                    (newStatus: EntityStatus) => handleSetDivisionStatus(divRecord, newStatus)
                  "
                />
              </template>
              <template v-else-if="divColumn.key === 'status'">
                <a-tag
                  :color="
                    divRecord.status === 'active'
                      ? 'green'
                      : divRecord.status === 'inactive'
                        ? 'red'
                        : 'blue'
                  "
                >
                  {{ divRecord.status.toUpperCase() }}
                </a-tag>
              </template>
            </template>
          </a-table>
          <div v-else style="color: #888; margin-top: 8px">
            No divisions added for this customer.
          </div>
        </div>
      </template>
    </a-table>

    <CustomerForm
      ref="customerFormRef"
      :visible="isModalVisible"
      :customer-data="editingCustomer"
      @close="handleModalClose"
      @save="handleModalSave"
    />

    <CustomerDivisionForm
      ref="customerDivisionFormRef"
      :visible="isDivisionModalVisible"
      :customer-id-for-new="divisionModalMode === 'add' ? currentCustomerIdForDivisionAction : null"
      :division-to-edit="divisionModalMode === 'edit' ? currentDivisionForEditing : null"
      @save="handleSaveDivision"
      @close="handleCloseDivisionModal"
    />

    <CustomerSettingsModal
      :visible="isSettingsModalVisible"
      :customer="customerForSettings"
      @close="handleCloseSettingsModal"
    />

    <WorkoutChargeModalHierarchical
      :visible="isWorkoutChargeModalVisible"
      :customer-id="workoutChargeCustomer?.id || 0"
      :division-id="workoutChargeDivision?.id || null"
      :customer-name="workoutChargeCustomer?.name || ''"
      :division-name="workoutChargeDivision?.name || ''"
      :existing-data="workoutChargeExistingData"
      @close="handleCloseWorkoutChargeModal"
      @save="handleWorkoutChargeSave"
    />
  </div>
</template>

<style scoped>
/* Styles for CustomerView (can reuse from previous suggestions if applicable) */

.expanded-row-content {
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin: 0;
}

.expanded-row-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}
</style>
