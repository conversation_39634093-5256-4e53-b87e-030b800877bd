<script setup lang="ts">
import { RouterView, useRoute } from 'vue-router'
import { theme } from 'ant-design-vue'
import { computed, onMounted, ref } from 'vue'
import SideBar from '@IVC/components/SideBar.vue'
import ChangePasswordModal from '@IVC/components/ChangePasswordModal.vue'
import UserDropdown from '@IVC/components/UserDropdown.vue'
import { useAuthStore } from '@IVC/stores/auth'
import { useAuthService } from '@IVC/services/auth'
import { seedInitialUsers } from '@IVC/services/master-data/user/index'
import { getCompanyProfile } from '@IVC/services/master-data/companyProfile/companyProfileService'
import { useDefaultStore } from '@IVC/stores/useDefaultStore'
import { initializeAutoFocusDB } from '@IVC/services/master-data/autoFunction/autoFunctionService'
import { initializeCODDB } from '@IVC/services/master-data/cod/codService'
import { useSQLite } from '@IVC/hooks/useSQLite'
import { initializeWorkoutCustomerChargeDB } from '@IVC/services/master-data/workoutCustomerCharge/workoutCustomerChargeService'

const { defaultAlgorithm, defaultSeed } = theme
const mapToken = defaultAlgorithm(defaultSeed)
const route = useRoute()
const authStore = useAuthStore()
const authService = useAuthService()
const { defaultStoreName, updateDefaultStoreName } = useDefaultStore()
const { dropAllTables } = useSQLite()

/**
 * Check if current route is login page
 * */
const isLoginPage = computed(() => route.name === 'login')

// Change password modal state
const changePasswordVisible = ref(false)

/**
 * Initialize auth on app start
 * */
onMounted(async () => {})

// Handle logout
const handleLogout = () => {
  authService.logout()
}

// Show change password modal
const showChangePasswordModal = () => {
  changePasswordVisible.value = true
}

// Handle change password success
const handleChangePasswordSuccess = () => {
  // Additional logic after password change if needed
  console.log('Password changed successfully from parent component')
}
</script>

<template>
  <a-config-provider
    :theme="{
      token: {
        colorPrimary: '#00b96b',
        algorithm: theme.defaultAlgorithm,
      },
    }"
  >
    <!-- Login Page Layout (Simple) -->
    <div v-if="isLoginPage" style="height: 100vh">
      <RouterView />
    </div>

    <!-- Dashboard Layout (Full) -->
    <a-layout v-else has-sider style="gap: 16px">
      <SideBar />
      <a-layout
        :style="{
          height: '100dvh',
          display: 'flex',
          flexDirection: 'column',
        }"
      >
        <a-layout-header
          style="
            background: #f5f5f5;
            padding: 0;
            margin: 0px 16px;
            display: flex;
            justify-content: space-between;
          "
        >
          <h2 style="margin: 0">{{ defaultStoreName }}</h2>
          <div style="display: flex; align-items: center; gap: 12px">
            <UserDropdown
              :user="authStore.user"
              @change-password="showChangePasswordModal"
              @logout="handleLogout"
            />
          </div>
        </a-layout-header>
        <a-layout-content :style="{ margin: '0px 16px' }">
          <div
            :style="{
              background: '#fff',
              height: '100%',
              overflow: 'auto',
              borderRadius: mapToken.borderRadiusLG + 'px',
            }"
          >
            <RouterView />
          </div>
        </a-layout-content>
        <a-layout-footer :style="{ textAlign: 'center' }">
          ISB VIETNAM COMPANY ©{{ new Date().getFullYear() }}
        </a-layout-footer>
      </a-layout>
    </a-layout>

    <!-- Change Password Modal -->
    <ChangePasswordModal
      v-model:open="changePasswordVisible"
      @success="handleChangePasswordSuccess"
    />
  </a-config-provider>
</template>

<style scoped></style>
