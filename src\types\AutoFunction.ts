export interface AutoFunction {
  id: number
  code: string
  name: string
  customerIds: number[] // Changed to array for multiple customers
  customerNames?: string[] // For display purposes
  note: string
  createdAt: string
  updatedAt: string
}

export interface AutoFunctionFormData {
  code: string
  name: string
  customerIds: number[] // Changed to array for multiple customers
  note: string
}

export interface AutoFunctionFilters {
  search?: string
  customerId?: number
}
