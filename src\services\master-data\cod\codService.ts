import { useSQLite } from '../../../hooks/useSQLite'
import type { COD, CODFormData, CODFilters } from '../../../types/COD'

// Helper function to get table name
const getCODTableName = () => {
  return 'cod' // Direct table name since tables.cod should return 'cod'
}

// Initialize COD tables
export const initializeCODDB = async (): Promise<void> => {
  const { executeQuery } = useSQLite()

  try {
    console.log('Initializing COD tables...')
    const tableName = getCODTableName()
    console.log('COD table name:', tableName)

    // Check if table already exists
    console.log('Checking if COD table exists...')
    try {
      const checkResult = await executeQuery(`SELECT COUNT(*) FROM ${tableName} LIMIT 1`)
      console.log('Table exists, skipping creation')

      // Table exists, just seed data if needed
      await seedCODData()
      console.log('COD database initialization completed (table already exists)')
      return
    } catch (tableError) {
      console.log('Table does not exist, creating new table...')
    }

    // Create cod table only if it doesn't exist
    console.log('Creating cod table...')
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS ${tableName} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT NOT NULL UNIQUE,
        name TEXT,
        address TEXT,
        code_type TEXT NOT NULL CHECK (code_type IN ('Store', 'Customer')),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `
    console.log('Create table SQL:', createTableSQL)
    const createResult = await executeQuery(createTableSQL)
    console.log('Create table result:', createResult)

    console.log('Table created successfully, seeding data...')
    await seedCODData()

    console.log('COD database initialized successfully')
  } catch (error) {
    console.error('Error initializing COD database:', error)
    throw error
  }
}

// Seed initial COD data
const seedCODData = async (): Promise<void> => {
  const { executeQuery } = useSQLite()

  try {
    const tableName = getCODTableName()

    // Check if data already exists
    const checkExisting = await executeQuery(`SELECT COUNT(*) as count FROM ${tableName}`)
    const count = (checkExisting?.result?.resultRows?.[0]?.[0] as number) || 0

    if (count > 0) {
      console.log('COD data already exists, skipping seed data')
      return
    }

    // Insert initial COD records
    const now = new Date().toISOString()

    const codData = [
      {
        code: '613001',
        name: 'SORA GARDEN',
        address: '',
        codeType: 'Store',
      },
      {
        code: '613002',
        name: 'VINCOM MEGAMALL',
        address: '',
        codeType: 'Store',
      },
      {
        code: '613003',
        name: 'VINCOM THẢO ĐIỀN',
        address: '',
        codeType: 'Store',
      },
      {
        code: '613004',
        name: 'NITORI LÊ THÁNH TÔN',
        address: '',
        codeType: 'Store',
      },
      {
        code: '681006',
        name: 'Giao hàng EWH - Lam Sơn',
        address: '',
        codeType: 'Customer',
      },
      {
        code: '682004',
        name: 'Giao hàng EWH - Nhất Tín',
        address: '',
        codeType: 'Customer',
      },
    ]

    console.log('Creating initial COD records...')

    for (const cod of codData) {
      await executeQuery(
        `
        INSERT INTO ${tableName} (code, name, address, code_type, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `,
        [cod.code, cod.name, cod.address, cod.codeType, now, now],
      )
    }

    console.log('Initial COD records created successfully')
  } catch (error) {
    console.error('Error seeding COD data:', error)
    throw error
  }
}

// Get all COD items with optional filters
export const getCODItems = async (filters?: CODFilters): Promise<COD[]> => {
  const { executeQuery } = useSQLite()

  try {
    const tableName = getCODTableName()

    let query = `
      SELECT
        c.id,
        c.code,
        c.name,
        c.address,
        c.code_type as codeType,
        c.created_at as createdAt,
        c.updated_at as updatedAt
      FROM ${tableName} c
    `

    const params: any[] = []
    const conditions: string[] = []

    if (filters?.search) {
      conditions.push('(c.code LIKE ? OR c.name LIKE ? OR c.address LIKE ?)')
      const searchTerm = `%${filters.search}%`
      params.push(searchTerm, searchTerm, searchTerm)
    }

    if (filters?.codeType) {
      conditions.push('c.code_type = ?')
      params.push(filters.codeType)
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ')
    }

    query += ' ORDER BY c.id ASC'

    const result = await executeQuery(query, params)

    if (!result?.result?.resultRows) {
      return []
    }

    const codItems: COD[] = []

    for (const row of result.result.resultRows) {
      codItems.push({
        id: row[0] as number,
        code: row[1] as string,
        name: row[2] as string,
        address: row[3] as string,
        codeType: row[4] as 'Store' | 'Customer',
        createdAt: row[5] as string,
        updatedAt: row[6] as string,
      })
    }

    return codItems
  } catch (error) {
    console.error('Error getting COD items:', error)
    throw error
  }
}

// Get COD item by ID
export const getCODById = async (id: number): Promise<COD | null> => {
  const { executeQuery } = useSQLite()

  try {
    const tableName = getCODTableName()

    console.log('Getting COD by ID:', id, 'from table:', tableName)

    const result = await executeQuery(
      `
      SELECT
        c.id,
        c.code,
        c.name,
        c.address,
        c.code_type as codeType,
        c.created_at as createdAt,
        c.updated_at as updatedAt
      FROM ${tableName} c
      WHERE c.id = ?
    `,
      [id],
    )

    console.log('getCODById result:', result)

    if (!result?.result?.resultRows?.[0]) {
      console.log('No COD found with ID:', id)
      return null
    }

    const row = result.result.resultRows[0]
    console.log('COD row data:', row)

    const codItem = {
      id: row[0] as number,
      code: row[1] as string,
      name: row[2] as string,
      address: row[3] as string,
      codeType: row[4] as 'Store' | 'Customer',
      createdAt: row[5] as string,
      updatedAt: row[6] as string,
    }

    console.log('Returning COD item:', codItem)
    return codItem
  } catch (error) {
    console.error('Error getting COD by ID:', error)
    throw error
  }
}

// Create new COD item
export const createCOD = async (data: CODFormData): Promise<COD> => {
  const { executeQuery } = useSQLite()

  try {
    const tableName = getCODTableName()

    console.log('Creating COD with data:', data)
    console.log('Using table name:', tableName)

    // Validate input data
    if (!data.code || !data.codeType) {
      throw new Error('Invalid input data: code and codeType are required')
    }

    const now = new Date().toISOString()

    // Insert COD record
    console.log('Inserting COD record...')
    const insertSQL = `
      INSERT INTO ${tableName} (code, name, address, code_type, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?)
    `
    const insertParams = [data.code, data.name || '', data.address || '', data.codeType, now, now]
    console.log('Insert SQL:', insertSQL)
    console.log('Insert params:', insertParams)

    const insertResult = await executeQuery(insertSQL, insertParams)
    console.log('Insert result:', insertResult)

    // Get the inserted ID
    console.log('Getting inserted ID...')
    const maxIdResult = await executeQuery(`SELECT MAX(id) as maxId FROM ${tableName}`)
    const newId = (maxIdResult?.result?.resultRows?.[0]?.[0] as number) || 0

    if (!newId || newId === 0) {
      throw new Error('Failed to get valid COD ID after insert')
    }

    // Return the created item
    const createdItem = await getCODById(newId)
    if (!createdItem) {
      throw new Error('Failed to retrieve created COD item')
    }

    console.log('COD created successfully:', createdItem)
    return createdItem
  } catch (error) {
    console.error('Error creating COD:', error)
    throw error
  }
}

// Update COD item
export const updateCOD = async (id: number, data: CODFormData): Promise<COD> => {
  const { executeQuery } = useSQLite()

  try {
    const tableName = getCODTableName()
    const now = new Date().toISOString()

    console.log('Updating COD with ID:', id, 'data:', data)
    console.log('Using table name:', tableName)

    // Update COD record
    const updateSQL = `
      UPDATE ${tableName}
      SET code = ?, name = ?, address = ?, code_type = ?, updated_at = ?
      WHERE id = ?
    `
    const updateParams = [data.code, data.name || '', data.address || '', data.codeType, now, id]

    console.log('Update SQL:', updateSQL)
    console.log('Update params:', updateParams)

    const updateResult = await executeQuery(updateSQL, updateParams)
    console.log('Update result:', updateResult)

    // Check if update was successful
    if (!updateResult || updateResult.type !== 'exec') {
      throw new Error('Update operation failed')
    }

    // Try to retrieve the updated item from database to get complete data
    try {
      console.log('Retrieving updated COD item...')
      const updatedItem = await getCODById(id)

      if (updatedItem) {
        console.log('Update successful, returning from DB:', updatedItem)
        return updatedItem
      }
    } catch (retrieveError) {
      console.warn(
        'Failed to retrieve updated item from DB, returning constructed object:',
        retrieveError,
      )
    }

    // Fallback: return a constructed object if DB retrieval fails
    const fallbackItem: COD = {
      id,
      code: data.code,
      name: data.name || '',
      address: data.address || '',
      codeType: data.codeType,
      createdAt: new Date().toISOString(), // Use current time as fallback
      updatedAt: now,
    }

    console.log('Update successful, returning fallback:', fallbackItem)
    return fallbackItem
  } catch (error) {
    console.error('Error updating COD:', error)
    throw error
  }
}

// Delete COD item
export const deleteCOD = async (id: number): Promise<void> => {
  const { executeQuery } = useSQLite()

  try {
    const tableName = getCODTableName()

    console.log('Deleting COD with ID:', id)

    const result = await executeQuery(`DELETE FROM ${tableName} WHERE id = ?`, [id])
    console.log('Delete result:', result)

    console.log('COD deleted successfully')
  } catch (error) {
    console.error('Error deleting COD:', error)
    throw error
  }
}
