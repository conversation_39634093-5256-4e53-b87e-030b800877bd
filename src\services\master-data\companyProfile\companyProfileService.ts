import { useSQLite } from '@IVC/hooks/useSQLite'
import type {
  CompanyProfile,
  CompanyStore,
  DebitCode,
  CompanyInfo,
} from '@IVC/types/CompanyProfile'

// Mock data for initialization
const mockCompanyProfile: CompanyProfile = {
  id: 1,
  companyInfo: {
    name: 'NIPPON EXPRESS (VIETNAM) CO.,LTD.',
    address: 'Le Hong Phong/189/8 D. Tan Phuoc, KP, Di An, Binh Duong',
    phone: '+84 28 1234 5678',
    taxCode: '0123456789',
  },
  stores: [
    {
      id: 1,
      name: 'L.A Logistics Center',
      location: 'Di An District, Binh Duong Province, Vietnam',
      manager: '<PERSON>',
      phone: '+84 28-3812-2922',
      status: 'active',
      isDefault: true,
    },
    {
      id: 2,
      name: 'Amata Warehouse',
      location: 'Bien Hoa City, Dong Nai Province, Vietnam',
      manager: '<PERSON>',
      phone: '+84 28-3812-2922',
      status: 'active',
      isDefault: false,
    },
    {
      id: 3,
      name: 'HP GSDSC Vietnam warehouse',
      location: 'ward 4 , Tan Binh District, HCMC, Vietnam',
      manager: '<PERSON>',
      phone: '+84 28-3812-2922',
      status: 'active',
      isDefault: false,
    },
    {
      id: 4,
      name: 'HPE Hanoi Mini GSD SC warehouse',
      location: 'Hanoi city, Hanoi, Vietnam',
      manager: 'Alice Brown',
      phone: ' +84 24-3755-6100',
      status: 'active',
      isDefault: false,
    },
    {
      id: 5,
      name: 'Quang Minh',
      location: 'Me Linh, Hanoi, Vietnam',
      manager: 'Charlie Davis',
      phone: ' +84 24-3755-6100',
      status: 'inactive',
      isDefault: false,
    },
  ],
  debitCodes: [
    {
      id: 1,
      code: 'W1H',
      name: 'PHI DICH VU',
      description: 'Phi dich vu',
      isActive: true,
      isUsed: false,
    },
    {
      id: 2,
      code: 'TL1',
      name: 'DAN NHAN',
      description: 'Danh nhan',
      isActive: true,
      isUsed: false,
    },
    {
      id: 3,
      code: 'WB1',
      name: 'BOC XEP KHO',
      description: 'Bốc xếp kho',
      isActive: true,
      isUsed: false,
    },
    {
      id: 4,
      code: 'OW1',
      name: 'LUU KHO',
      description: 'Lưu kho',
      isActive: true,
      isUsed: false,
    },
    {
      id: 5,
      code: 'WM1',
      name: 'QUAN LY KHO',
      description: 'Quản lý kho',
      isActive: true,
      isUsed: false,
    },
    {
      id: 6,
      code: 'WS1',
      name: 'PHI HE THONG',
      description: 'Phi he thong',
      isActive: true,
      isUsed: false,
    },
    {
      id: 7,
      code: 'TW1',
      name: 'VAN CHUYEN',
      description: 'Van chuyen',
      isActive: true,
      isUsed: false,
    },
  ],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
}

// Counter for temporary IDs
let tempIdCounter = 999999

// Temporary functions for UI-only operations (not saved to DB until Save Changes)
export const createTempCompanyStore = (store: Omit<CompanyStore, 'id'>): CompanyStore => {
  // Generate temporary high positive ID for new items (to avoid conflicts)
  const tempId = tempIdCounter++

  return {
    id: tempId,
    name: store.name || '',
    location: store.location || '',
    manager: store.manager || '',
    phone: store.phone || '',
    status: store.status,
    isDefault: store.isDefault,
  }
}

export const createTempDebitCode = (code: Omit<DebitCode, 'id'>): DebitCode => {
  // Generate temporary high positive ID for new items (to avoid conflicts)
  const tempId = tempIdCounter++

  return {
    id: tempId,
    code: code.code || '',
    name: code.name || '',
    description: code.description || '',
    isActive: code.isActive,
    isUsed: code.isUsed,
  }
}

// Initialize database with seed data
export const initializeCompanyProfileDB = async () => {
  const { executeQuery, tables } = useSQLite()

  try {
    // Check if data already exists
    const companyInfoExists = await executeQuery(
      `SELECT COUNT(*) as count FROM ${tables.company_info}`,
    )
    const storesExist = await executeQuery(`SELECT COUNT(*) as count FROM ${tables.company_stores}`)
    const debitCodesExist = await executeQuery(
      `SELECT COUNT(*) as count FROM ${tables.debit_codes}`,
    )

    // Seed company info if not exists
    if (companyInfoExists?.result?.resultRows?.[0]?.[0] === 0) {
      await executeQuery(
        `
        INSERT INTO ${tables.company_info} (name, address, phone, tax_code)
        VALUES (?, ?, ?, ?)
      `,
        [
          mockCompanyProfile.companyInfo.name,
          mockCompanyProfile.companyInfo.address,
          mockCompanyProfile.companyInfo.phone,
          mockCompanyProfile.companyInfo.taxCode,
        ],
      )
    }

    // Seed stores if not exist
    if (storesExist?.result?.resultRows?.[0]?.[0] === 0) {
      for (const store of mockCompanyProfile.stores) {
        await executeQuery(
          `
          INSERT INTO ${tables.company_stores} (name, location, manager, phone, status, is_default)
          VALUES (?, ?, ?, ?, ?, ?)
        `,
          [
            store.name,
            store.location,
            store.manager,
            store.phone,
            store.status,
            store.isDefault ? 1 : 0,
          ],
        )
      }
    }

    // Seed debit codes if not exist
    if (debitCodesExist?.result?.resultRows?.[0]?.[0] === 0) {
      for (const code of mockCompanyProfile.debitCodes) {
        await executeQuery(
          `
          INSERT INTO ${tables.debit_codes} (code, name, description, is_active, is_used)
          VALUES (?, ?, ?, ?, ?)
        `,
          [code.code, code.name, code.description, code.isActive ? 1 : 0, code.isUsed ? 1 : 0],
        )
      }
    }

    console.log('Company Profile database initialized successfully')
  } catch (error) {
    console.error('Error initializing company profile database:', error)
  }
}

// Company Info operations
export const getCompanyInfo = async (): Promise<CompanyInfo> => {
  const { executeQuery, tables } = useSQLite()

  try {
    const result = await executeQuery(
      `SELECT * FROM ${tables.company_info} ORDER BY id DESC LIMIT 1`,
    )
    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const row = result.result.resultRows[0]
      return {
        name: row[1] as string,
        address: row[2] as string,
        phone: row[3] as string,
        taxCode: row[4] as string,
      }
    }
    return mockCompanyProfile.companyInfo
  } catch (error) {
    console.error('Error getting company info:', error)
    return mockCompanyProfile.companyInfo
  }
}

export const updateCompanyInfo = async (info: CompanyInfo): Promise<CompanyInfo> => {
  const { executeQuery, tables } = useSQLite()

  try {
    await executeQuery(
      `
      UPDATE ${tables.company_info}
      SET name = ?, address = ?, phone = ?, tax_code = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = (SELECT id FROM ${tables.company_info} ORDER BY id DESC LIMIT 1)
    `,
      [info.name, info.address, info.phone, info.taxCode],
    )

    return info
  } catch (error) {
    console.error('Error updating company info:', error)
    throw error
  }
}

// Company Stores operations
export const getCompanyStores = async (): Promise<CompanyStore[]> => {
  const { executeQuery, tables } = useSQLite()

  try {
    const result = await executeQuery(`SELECT * FROM ${tables.company_stores} ORDER BY id`)
    if (result?.result?.resultRows) {
      return result.result.resultRows.map((row: unknown[]) => ({
        id: row[0] as number,
        name: row[1] as string,
        location: row[2] as string,
        manager: row[3] as string,
        phone: row[4] as string,
        status: row[5] as 'active' | 'inactive',
        isDefault: Boolean(row[6]),
      }))
    }
    return mockCompanyProfile.stores
  } catch (error) {
    console.error('Error getting company stores:', error)
    return mockCompanyProfile.stores
  }
}

export const addCompanyStore = async (store: Omit<CompanyStore, 'id'>): Promise<CompanyStore> => {
  const { executeQuery, tables } = useSQLite()

  try {
    console.log('Adding company store:', store)

    // If setting as default, unset other defaults first
    if (store.isDefault) {
      await executeQuery(`UPDATE ${tables.company_stores} SET is_default = 0`)
    }

    // Use placeholder values for empty fields to ensure data is saved
    const storeData = {
      name: store.name || '',
      location: store.location || '',
      manager: store.manager || '',
      phone: store.phone || '',
      status: store.status,
      isDefault: store.isDefault,
    }

    const result = await executeQuery(
      `
      INSERT INTO ${tables.company_stores} (name, location, manager, phone, status, is_default)
      VALUES (?, ?, ?, ?, ?, ?)
    `,
      [
        storeData.name,
        storeData.location,
        storeData.manager,
        storeData.phone,
        storeData.status,
        storeData.isDefault ? 1 : 0,
      ],
    )

    console.log('Insert result:', result)

    // Get the inserted ID using fallback method
    const maxIdResult = await executeQuery(`SELECT MAX(id) as maxId FROM ${tables.company_stores}`)
    const newId = (maxIdResult?.result?.resultRows?.[0]?.[0] as number) || 0

    const newStore = {
      id: newId,
      ...storeData,
    }

    console.log('New store created:', newStore)
    return newStore
  } catch (error) {
    console.error('Error adding company store:', error)
    throw error
  }
}

export const updateCompanyStore = async (store: CompanyStore): Promise<CompanyStore> => {
  const { executeQuery, tables } = useSQLite()

  try {
    console.log('Updating company store:', store)

    // If setting as default, unset other defaults first
    if (store.isDefault) {
      await executeQuery(`UPDATE ${tables.company_stores} SET is_default = 0 WHERE id != ?`, [
        store.id,
      ])
    }

    const result = await executeQuery(
      `
      UPDATE ${tables.company_stores}
      SET name = ?, location = ?, manager = ?, phone = ?, status = ?, is_default = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `,
      [
        store.name,
        store.location,
        store.manager,
        store.phone,
        store.status,
        store.isDefault ? 1 : 0,
        store.id,
      ],
    )

    console.log('Update result:', result)
    return store
  } catch (error) {
    console.error('Error updating company store:', error)
    throw error
  }
}

export const deleteCompanyStore = async (id: number): Promise<void> => {
  const { executeQuery, tables } = useSQLite()

  try {
    console.log('Deleting company store with id:', id)

    const result = await executeQuery(`DELETE FROM ${tables.company_stores} WHERE id = ?`, [id])
    console.log('Delete result:', result)
  } catch (error) {
    console.error('Error deleting company store:', error)
    throw error
  }
}

// Debit Codes operations
export const getDebitCodes = async (): Promise<DebitCode[]> => {
  const { executeQuery, tables } = useSQLite()

  try {
    const result = await executeQuery(`SELECT * FROM ${tables.debit_codes} ORDER BY id`)
    if (result?.result?.resultRows) {
      return result.result.resultRows.map((row: unknown[]) => ({
        id: row[0] as number,
        code: row[1] as string,
        name: row[2] as string,
        description: row[3] as string,
        isActive: Boolean(row[4]),
        isUsed: Boolean(row[5]),
      }))
    }
    return mockCompanyProfile.debitCodes
  } catch (error) {
    console.error('Error getting debit codes:', error)
    return mockCompanyProfile.debitCodes
  }
}

export const addDebitCode = async (code: Omit<DebitCode, 'id'>): Promise<DebitCode> => {
  const { executeQuery, tables } = useSQLite()

  try {
    console.log('Adding debit code:', code)

    // Use placeholder values for empty fields to ensure data is saved
    const codeData = {
      code: code.code || 'NEW_CODE',
      name: code.name || 'New Code',
      description: code.description || 'Description TBD',
      isActive: code.isActive,
      isUsed: code.isUsed,
    }

    const result = await executeQuery(
      `
      INSERT INTO ${tables.debit_codes} (code, name, description, is_active, is_used)
      VALUES (?, ?, ?, ?, ?)
    `,
      [
        codeData.code,
        codeData.name,
        codeData.description,
        codeData.isActive ? 1 : 0,
        codeData.isUsed ? 1 : 0,
      ],
    )

    console.log('Insert result:', result)

    // Get the inserted ID using fallback method
    const maxIdResult = await executeQuery(`SELECT MAX(id) as maxId FROM ${tables.debit_codes}`)
    const newId = (maxIdResult?.result?.resultRows?.[0]?.[0] as number) || 0

    const newCode = {
      id: newId,
      ...codeData,
    }

    console.log('New debit code created:', newCode)
    return newCode
  } catch (error) {
    console.error('Error adding debit code:', error)
    throw error
  }
}

export const updateDebitCode = async (code: DebitCode): Promise<DebitCode> => {
  const { executeQuery, tables } = useSQLite()

  try {
    console.log('Updating debit code:', code)

    const result = await executeQuery(
      `
      UPDATE ${tables.debit_codes}
      SET code = ?, name = ?, description = ?, is_active = ?, is_used = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `,
      [code.code, code.name, code.description, code.isActive ? 1 : 0, code.isUsed ? 1 : 0, code.id],
    )

    console.log('Update result:', result)
    return code
  } catch (error) {
    console.error('Error updating debit code:', error)
    throw error
  }
}

export const deleteDebitCode = async (id: number): Promise<void> => {
  const { executeQuery, tables } = useSQLite()

  try {
    console.log('Deleting debit code with id:', id)

    const result = await executeQuery(`DELETE FROM ${tables.debit_codes} WHERE id = ?`, [id])
    console.log('Delete result:', result)
  } catch (error) {
    console.error('Error deleting debit code:', error)
    throw error
  }
}

// Combined operations
export const getCompanyProfile = async (): Promise<CompanyProfile> => {
  try {
    const companyInfo = await getCompanyInfo()
    const stores = await getCompanyStores()
    const debitCodes = await getDebitCodes()

    return {
      id: 1,
      companyInfo,
      stores,
      debitCodes,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
  } catch (error) {
    console.error('Error getting company profile:', error)
    return mockCompanyProfile
  }
}

export const updateCompanyProfile = async (profile: CompanyProfile): Promise<CompanyProfile> => {
  try {
    console.log('Updating company profile:', profile)

    // Update company info
    await updateCompanyInfo(profile.companyInfo)

    // Update stores - only update existing stores with valid data
    for (const store of profile.stores) {
      if (store.id && store.id > 0) {
        // Only update if store has required data
        if (store.name || store.location || store.manager || store.phone) {
          await updateCompanyStore(store)
        }
      }
    }

    // Update debit codes - only update existing codes with valid data
    for (const code of profile.debitCodes) {
      if (code.id && code.id > 0) {
        // Only update if code has required data
        if (code.code || code.name || code.description) {
          await updateDebitCode(code)
        }
      }
    }

    console.log('Company profile updated successfully')
    return await getCompanyProfile()
  } catch (error) {
    console.error('Error updating company profile:', error)
    throw error
  }
}
