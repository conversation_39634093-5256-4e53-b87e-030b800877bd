<template>
  <a-dropdown :trigger="['click']" placement="bottomRight">
    <a-button type="text" class="settings-button">
      <IconSetting class="settings-icon" />
    </a-button>
    <template #overlay>
      <a-menu @click="handleMenuClick" class="settings-menu">
        <a-menu-item key="changePassword" class="menu-item">
          <LockOutlined />
          <span class="menu-text">Change Password</span>
        </a-menu-item>
        <a-menu-divider />
        <a-menu-item key="logout" class="menu-item">
          <LogoutOutlined />
          <span class="menu-text">Logout</span>
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
</template>

<script setup lang="ts">
import { LogoutOutlined, LockOutlined } from '@ant-design/icons-vue'
import IconSetting from '@IVC/components/icons/IconSetting.vue'

// Emits
const emit = defineEmits<{
  'change-password': []
  logout: []
}>()

// Handle menu click
const handleMenuClick = ({ key }: { key: string }) => {
  if (key === 'logout') {
    emit('logout')
  } else if (key === 'changePassword') {
    emit('change-password')
  }
}
</script>

<style scoped>
.settings-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.2s;
}

.settings-button:hover {
  background: rgba(0, 0, 0, 0.04);
}

.settings-icon {
  width: 16px;
  height: 16px;
  color: #666;
  transition: color 0.2s;
}

.settings-button:hover .settings-icon {
  color: #1890ff;
}

.settings-menu {
  min-width: 160px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  transition: all 0.2s;
}

.menu-item:hover {
  background: #f5f5f5;
}

.menu-text {
  margin-left: 8px;
  font-size: 14px;
  color: #262626;
}

/* Icon colors */
:deep(.anticon-lock) {
  color: #1890ff;
}

:deep(.anticon-logout) {
  color: #ff4d4f;
}

/* Menu item hover effects */
.menu-item:hover :deep(.anticon-lock) {
  color: #40a9ff;
}

.menu-item:hover :deep(.anticon-logout) {
  color: #ff7875;
}

.menu-item:hover .menu-text {
  color: #1890ff;
}

.menu-item[data-menu-id$='logout']:hover .menu-text {
  color: #ff4d4f;
}
</style>
