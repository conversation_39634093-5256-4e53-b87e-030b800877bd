import { type User } from '@IVC/types/MasterDataTypes/User'
import { message } from 'ant-design-vue'
import { axiosClient } from '@IVC/common/utils/axiosClient'
import axios from 'axios'

export async function getAllUsers(): Promise<User[]> {
  try {
    console.log('execute get users')
    const response = await axiosClient.get<User[]>('/user')
    return response.data.data
  } catch (error) {
    if (axios.isAxiosError(error)) {
      message.error(error.response?.data?.message || 'Failed to get users')
    }
    throw error
  }
}

export async function getUserById(id: number): Promise<User | null> {
  try {
    const response = await axiosClient.get<User>(`/user/${id}`)
    return response.data
  } catch (error) {
    if (axios.isAxiosError(error) && error.response?.status === 404) {
      return null
    }
    throw error
  }
}

export async function getUserByUsername(username: string): Promise<User | null> {
  try {
    const response = await axiosClient.get<User[]>('/user/search', {
      params: { username },
    })
    return response.data[0] || null
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error(
        'Failed to get user by username:',
        error.response?.data?.message || error.message,
      )
    }
    return null
  }
}

export async function getUserByEmail(email: string): Promise<User | null> {
  try {
    const response = await axiosClient.get<User[]>('/user/search', {
      params: { email },
    })
    return response.data[0] || null
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Failed to get user by email:', error.response?.data?.message || error.message)
    }
    return null
  }
}

export async function addUser(userData: Partial<User>): Promise<void> {
  try {
    await axiosClient.post('/user', userData)
    message.success('User created successfully!')
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 409) {
        message.error(error.response.data.message || 'User already exists')
      } else {
        message.error('Failed to create user')
      }
    }
    throw error
  }
}

export async function updateUser(user: Partial<User>): Promise<void> {
  try {
    // Validate if user exists before update
    const currentUser = await getUserById(user.id!)
    if (!currentUser) {
      message.error('User not found')
      throw new Error('User not found')
    }

    await axiosClient.put(`/user/${user.id}`, user)
    message.success('User updated successfully!')
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 409) {
        message.error(error.response.data.message || 'User data conflict')
      } else {
        message.error('Failed to update user')
      }
    }
    throw error
  }
}

export async function deleteUser(id: number): Promise<void> {
  try {
    await axiosClient.delete(`/user/${id}`)
    message.success('User has been deleted successfully!')
  } catch (error) {
    if (axios.isAxiosError(error)) {
      message.error(error.response?.data?.message || 'Failed to delete user')
    }
    throw error
  }
}

export async function updateUserStatus(id: number, status: boolean): Promise<void> {
  try {
    await axiosClient.put(`/user/${id}/status`, { status })
    const statusText = status ? 'activated' : 'deactivated'
    message.success(`User has been ${statusText}`)
  } catch (error) {
    if (axios.isAxiosError(error)) {
      message.error(error.response?.data?.message || 'Failed to update user status')
    }
    throw error
  }
}

export async function searchUsers(query: string): Promise<User[]> {
  try {
    const response = await axiosClient.get<User[]>('/user/search', {
      params: { query },
    })
    return response.data
  } catch (error) {
    if (axios.isAxiosError(error)) {
      message.error(error.response?.data?.message || 'Failed to search users')
    }
    throw error
  }
}

export async function getUsersByRole(role: 'admin' | 'manager' | 'user'): Promise<User[]> {
  try {
    const response = await axiosClient.get<User[]>(`/user/role/${role}`)
    return response.data
  } catch (error) {
    if (axios.isAxiosError(error)) {
      message.error(error.response?.data?.message || 'Failed to get users by role')
    }
    throw error
  }
}

export async function sendInvitation(id: number): Promise<void> {
  try {
    await axiosClient.post(`/user/${id}/invite`)
    message.success('Invitation sent successfully')
  } catch (error) {
    if (axios.isAxiosError(error)) {
      message.error(error.response?.data?.message || 'Failed to send invitation')
    }
    throw error
  }
}

export async function authenticateUser(username: string, password: string): Promise<User | null> {
  try {
    const response = await axiosClient.post('/auth/login', { username, password })
    if (response.data?.user) {
      return response.data.user
    }
    return null
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Failed to authenticate user:', error.response?.data?.message || error.message)
    }
    return null
  }
}

export async function changeUserPassword(
  userId: number,
  currentPassword: string,
  newPassword: string,
): Promise<void> {
  try {
    await axiosClient.put(`/user/${userId}/password`, {
      currentPassword,
      newPassword,
    })
    message.success('Password changed successfully!')
  } catch (error) {
    if (axios.isAxiosError(error)) {
      const errorMessage = error.response?.data?.message || 'Failed to change password'
      message.error(errorMessage)
    }
    throw error
  }
}

export async function seedInitialUsers(): Promise<void> {
  try {
    await axiosClient.post('/user/seed')
    message.success('Initial users seeded successfully')
  } catch (error) {
    if (axios.isAxiosError(error)) {
      message.error(error.response?.data?.message || 'Failed to seed initial users')
    }
    console.error('Failed to seed initial users:', error)
    throw error
  }
}
