import { useSQLite } from '@IVC/hooks/useSQLite'
import { type Unit, type EntityStatus } from '@IVC/types/MasterDataTypes/Unit' // Ensure this path is correct

export async function getAllUnits(): Promise<Unit[]> {
  const { executeQuery, tables } = useSQLite()
  const query = `SELECT id, code, name, status FROM ${tables.units} ORDER BY id DESC`

  try {
    const response = await executeQuery(query)
    if (response?.result?.resultRows && Array.isArray(response.result.resultRows)) {
      return response.result.resultRows.map((row: unknown[]) => {
        return {
          id: row[0] as number,
          code: row[1] as string,
          name: row[2] as string,
          status: row[3] as EntityStatus,
        } as Unit
      })
    }
    return []
  } catch (err) {
    console.error('Failed to get units:', err)
    throw err
  }
}

export async function getUnitById(id: number): Promise<Unit | null> {
  const { executeQuery, tables } = useSQLite()
  const query = `SELECT id, code, name, status FROM ${tables.units} WHERE id = ?`
  try {
    const response = await executeQuery(query, [id])
    if (response?.result?.resultRows && response.result.resultRows.length > 0) {
      const row = response.result.resultRows[0]
      return {
        id: row[0] as number,
        code: row[1] as string,
        name: row[2] as string,
        status: row[3] as EntityStatus,
      } as Unit
    }
    return null
  } catch (err) {
    console.error(`Failed to get unit with ID ${id}:`, err)
    throw err
  }
}

export async function addUnit(unitData: Partial<Unit>): Promise<void> {
  const { executeQuery, tables } = useSQLite()
  // Default to 'active' if status is not provided, aligning with EntityStatus and DB default.
  // UnitForm.vue typically provides 'active' status for new units.
  const statusToInsert: EntityStatus = unitData.status || 'active'

  const query = `
    INSERT INTO ${tables.units} (code, name, status)
    VALUES (?, ?, ?);`
  const params = [unitData.code, unitData.name, statusToInsert]

  try {
    await executeQuery(query, params)
  } catch (err) {
    console.error('Failed to add unit:', err)
    throw err
  }
}

export async function updateUnit(unit: Partial<Unit>): Promise<void> {
  const { executeQuery, tables } = useSQLite()
  const setClauses: string[] = []
  const params: unknown[] = []

  if (unit.code !== undefined) {
    setClauses.push('code = ?')
    params.push(unit.code)
  }
  if (unit.name !== undefined) {
    setClauses.push('name = ?')
    params.push(unit.name)
  }
  if (unit.status !== undefined) {
    setClauses.push('status = ?')
    params.push(unit.status)
  }

  if (setClauses.length === 0) return // No fields to update

  const query = `
    UPDATE ${tables.units}
    SET ${setClauses.join(', ')}
    WHERE id = ?;
  `
  params.push(unit.id)

  try {
    await executeQuery(query, params)
  } catch (err) {
    console.error(`Failed to update unit with ID ${unit.id}:`, err)
    throw err
  }
}

export async function deleteUnit(id: number): Promise<void> {
  const { executeQuery, tables } = useSQLite()
  const unit = await getUnitById(id) // Fetch unit to check status

  if (unit && unit.status === 'active') {
    // Consider if this business rule applies to Units as it does to Customers
    throw new Error(
      `Active unit "${unit.name}" cannot be deleted. Please set it to inactive first.`,
    )
  }

  const query = `DELETE FROM ${tables.units} WHERE id = ?;`
  const params = [id]

  try {
    await executeQuery(query, params)
  } catch (err) {
    console.error(`Failed to delete unit with ID ${id}:`, err)
    throw err
  }
}
