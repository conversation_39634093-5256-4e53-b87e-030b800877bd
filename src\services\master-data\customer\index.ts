import { axiosClient } from '@IVC/common/utils/axiosClient'
import { message } from 'ant-design-vue'
import axios from 'axios'
import {
  type Customer,
  type CustomerDivision,
  type CustomerSettings,
} from '@IVC/types/MasterDataTypes/Customer'

export async function getAllCustomers(): Promise<Customer[]> {
  try {
    const response = await axiosClient.get<{ status: string; data: Customer[] }>('/customers/')
    return response.data.data
  } catch (error) {
    if (axios.isAxiosError(error)) {
      message.error(error.response?.data?.message || 'Failed to get customers')
    }
    throw error
  }
}

export async function getCustomerById(id: number): Promise<Customer | null> {
  try {
    const response = await axiosClient.get<{ status: string; data: Customer }>(`/customers/${id}`)
    return response.data.data
  } catch (error) {
    if (axios.isAxiosError(error) && error.response?.status === 404) {
      return null
    }
    if (axios.isAxiosError(error)) {
      console.error(
        `Failed to get customer with ID ${id}:`,
        error.response?.data?.message || error.message,
      )
    }
    throw error
  }
}

// --- Customer Settings ---

export async function getCustomerSettings(customerId: number): Promise<CustomerSettings | null> {
  try {
    const response = await axiosClient.get<{ status: string; data: CustomerSettings }>(
      `/customers/${customerId}/settings`,
    )
    return response.data.data
  } catch (error) {
    if (axios.isAxiosError(error) && error.response?.status === 404) {
      return null // No settings found for this customer
    }
    if (axios.isAxiosError(error)) {
      console.error(
        `Failed to get settings for customer ID ${customerId}:`,
        error.response?.data?.message || error.message,
      )
    }
    throw error
  }
}

export async function addOrUpdateCustomerSettings(
  settings: Omit<CustomerSettings, 'id' | 'createdAt' | 'updatedAt'> & { customerId: number },
): Promise<void> {
  try {
    const { customerId, masterDataImportMapping } = settings
    await axiosClient.post(`/customers/${customerId}/settings`, {
      masterDataImportMapping,
    })
    message.success('Customer settings saved successfully!')
  } catch (error) {
    if (axios.isAxiosError(error)) {
      message.error(error.response?.data?.message || 'Failed to save customer settings')
    }
    throw error
  }
}

export async function addCustomer(customerData: Partial<Customer>): Promise<void> {
  try {
    await axiosClient.post('/customers/', {
      code: customerData.code,
      name: customerData.name,
      address: customerData.address,
      taxNumber: customerData.taxNumber,
      phone: customerData.phone,
    })
    message.success('Customer created successfully!')
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 400) {
        message.error(error.response.data.message || 'Invalid customer data')
      } else {
        message.error('Failed to create customer')
      }
    }
    throw error
  }
}

export async function updateCustomer(customer: Partial<Customer>): Promise<void> {
  try {
    // Validate if customer exists before update
    const currentCustomer = await getCustomerById(customer.id!)
    if (!currentCustomer) {
      message.error('Customer not found')
      throw new Error('Customer not found')
    }

    await axiosClient.put(`/customers/${customer.id}`, {
      code: customer.code,
      name: customer.name,
      address: customer.address,
      taxNumber: customer.taxNumber,
      phone: customer.phone,
      status: customer.status,
    })
    message.success('Customer updated successfully!')
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 400) {
        message.error(error.response.data.message || 'Invalid customer data')
      } else if (error.response?.status === 404) {
        message.error('Customer not found')
      } else {
        message.error('Failed to update customer')
      }
    }
    throw error
  }
}

export async function getCustomerDivisionsByCustomerId(
  customerId: number,
): Promise<CustomerDivision[]> {
  try {
    const response = await axiosClient.get<{ status: string; data: CustomerDivision[] }>(
      `/customers/${customerId}/divisions`,
    )
    return response.data.data
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error(
        `Failed to get divisions for customer ID ${customerId}:`,
        error.response?.data?.message || error.message,
      )
    }
    throw error
  }
}

export async function getCustomerDivisionById(id: number): Promise<CustomerDivision | null> {
  try {
    const response = await axiosClient.get<{ status: string; data: CustomerDivision }>(
      `/customers/divisions/${id}`,
    )
    return response.data.data
  } catch (error) {
    if (axios.isAxiosError(error) && error.response?.status === 404) {
      return null
    }
    if (axios.isAxiosError(error)) {
      console.error(
        `Failed to get customer division with ID ${id}:`,
        error.response?.data?.message || error.message,
      )
    }
    throw error
  }
}

export async function addCustomerDivision(
  divisionData: Omit<CustomerDivision, 'id'>,
): Promise<void> {
  try {
    await axiosClient.post(`/customers/${divisionData.customerId}/divisions`, {
      code: divisionData.code,
      name: divisionData.name,
      description: divisionData.description,
    })
    message.success('Customer division created successfully!')
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 400) {
        message.error(error.response.data.message || 'Invalid division data')
      } else {
        message.error('Failed to create customer division')
      }
    }
    throw error
  }
}

export async function updateCustomerDivision(division: CustomerDivision): Promise<void> {
  try {
    // Validate if division exists before update
    const currentDivision = await getCustomerDivisionById(division.id!)
    if (!currentDivision) {
      message.error('Customer division not found')
      throw new Error('Customer division not found')
    }

    await axiosClient.put(`/customers/divisions/${division.id}`, {
      code: division.code,
      name: division.name,
      description: division.description,
      status: division.status,
    })
    message.success('Customer division updated successfully!')
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 400) {
        message.error(error.response.data.message || 'Invalid division data')
      } else if (error.response?.status === 404) {
        message.error('Customer division not found')
      } else {
        message.error('Failed to update customer division')
      }
    }
    throw error
  }
}

export async function deleteCustomerDivision(divisionId: number): Promise<void> {
  try {
    await axiosClient.delete(`/customers/divisions/${divisionId}`)
    message.success('Customer division deleted successfully!')
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 400) {
        message.error(error.response.data.message || 'Cannot delete active customer division')
      } else if (error.response?.status === 404) {
        message.error('Customer division not found')
      } else {
        message.error('Failed to delete customer division')
      }
    }
    throw error
  }
}

export async function deleteCustomer(id: number): Promise<void> {
  try {
    await axiosClient.delete(`/customers/${id}`)
    message.success('Customer deleted successfully!')
  } catch (error) {
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 400) {
        message.error(error.response.data.message || 'Cannot delete active customer')
      } else if (error.response?.status === 404) {
        message.error('Customer not found')
      } else {
        message.error('Failed to delete customer')
      }
    }
    throw error
  }
}
