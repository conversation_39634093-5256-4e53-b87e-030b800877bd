import { useSQLite } from '@IVC/hooks/useSQLite'
import type { EntityStatus } from '@IVC/types/common'
import {
  type Customer,
  type CustomerDivision,
  type CustomerSettings, // Import CustomerSettings type
} from '@IVC/types/MasterDataTypes/Customer'

export async function getAllCustomers(): Promise<Customer[]> {
  const { executeQuery, tables } = useSQLite()
  const query = `SELECT id, code, name, address, tax_number, phone, status FROM ${tables.customers} ORDER BY id DESC`

  try {
    const response = await executeQuery(query)
    if (response?.result?.resultRows && Array.isArray(response.result.resultRows)) {
      return response.result.resultRows.map((row: unknown[]) => {
        return {
          id: row[0] as number,
          code: row[1] as string,
          name: row[2] as string,
          address: row[3] as string,
          taxNumber: row[4] as string, // Corresponds to tax_number in the query
          phone: row[5] as string,
          status: row[6] as EntityStatus,
        } as Customer
      })
    }
    return [] // Return an empty array if data is not in the expected format
  } catch (err) {
    console.error('Failed to get customers:', err)
    throw err // Re-throw the error to be handled by the caller
  }
}

export async function getCustomerById(id: number): Promise<Customer | null> {
  const { executeQuery, tables } = useSQLite()
  const query = `SELECT id, code, name, address, tax_number, phone, status FROM ${tables.customers} WHERE id = ?`
  try {
    const response = await executeQuery(query, [id])
    if (response?.result?.resultRows && response.result.resultRows.length > 0) {
      const row = response.result.resultRows[0]
      return {
        id: row[0] as number,
        code: row[1] as string,
        name: row[2] as string,
        address: row[3] as string,
        taxNumber: row[4] as string,
        phone: row[5] as string,
        status: row[6] as EntityStatus,
      } as Customer
    }
    return null
  } catch (err) {
    console.error(`Failed to get customer with ID ${id}:`, err)
    throw err
  }
}

// --- Customer Settings ---

export async function getCustomerSettings(customerId: number): Promise<CustomerSettings | null> {
  const { executeQuery, tables } = useSQLite()
  const query = `
    SELECT id, customer_id, master_data_import_mapping, created_at, updated_at
    FROM ${tables.customer_settings}
    WHERE customer_id = ?;
  `
  try {
    const response = await executeQuery(query, [customerId])
    if (response?.result?.resultRows && response.result.resultRows.length > 0) {
      const row = response.result.resultRows[0]
      return {
        id: row[0] as number,
        customerId: row[1] as number,
        masterDataImportMapping: row[2] ? JSON.parse(row[2] as string) : {}, // Parse JSON string
        createdAt: row[3] as string,
        updatedAt: row[4] as string,
      } as CustomerSettings
    }
    return null // No settings found for this customer
  } catch (err) {
    console.error(`Failed to get settings for customer ID ${customerId}:`, err)
    throw err
  }
}

export async function addOrUpdateCustomerSettings(
  settings: Omit<CustomerSettings, 'id' | 'createdAt' | 'updatedAt'> & { customerId: number },
): Promise<void> {
  const { executeQuery, tables } = useSQLite()
  const { customerId, masterDataImportMapping } = settings

  // Stringify the mapping object for storage
  const mappingJson = JSON.stringify(masterDataImportMapping || {})

  // UPSERT logic: Try to insert, if customer_id conflicts, then update.
  // SQLite's ON CONFLICT clause is perfect for this.
  const query = `
    INSERT INTO ${tables.customer_settings} (customer_id, master_data_import_mapping, updated_at)
    VALUES (?, ?, CURRENT_TIMESTAMP)
    ON CONFLICT(customer_id) DO UPDATE SET
      master_data_import_mapping = excluded.master_data_import_mapping,
      updated_at = CURRENT_TIMESTAMP;
  `
  // `excluded.master_data_import_mapping` refers to the value that would have been inserted.

  try {
    await executeQuery(query, [customerId, mappingJson])
  } catch (err) {
    console.error(`Failed to add or update settings for customer ID ${customerId}:`, err)
    throw err
  }
}

// Note: Deleting customer_settings is typically handled by ON DELETE CASCADE
// when a customer is deleted. If you need a separate delete function for settings
// (e.g., to reset settings without deleting the customer), you can add it here.
// For example:
// export async function deleteCustomerSettings(customerId: number): Promise<void> { ... }

export async function addCustomer(customerData: Partial<Customer>): Promise<void> {
  const { executeQuery, tables } = useSQLite()
  // status will be 'new' by default from DB schema
  const query = `
    INSERT INTO ${tables.customers} (code, name, address, tax_number, phone)
    VALUES (?, ?, ?, ?, ?);`
  const params = [
    customerData.code,
    customerData.name,
    customerData.address,
    customerData.taxNumber, // This value will be inserted into the tax_number column
    customerData.phone,
  ]

  try {
    await executeQuery(query, params)
  } catch (err) {
    console.error('Failed to add customer:', err)
    throw err // Re-throw the error to be handled by the caller
  }
}

export async function updateCustomer(customer: Partial<Customer>): Promise<void> {
  const { executeQuery, tables } = useSQLite()
  // Build SET clauses dynamically to only update provided fields
  const setClauses: string[] = []
  const params: unknown[] = []

  if (customer.code !== undefined) {
    setClauses.push('code = ?')
    params.push(customer.code)
  }
  if (customer.name !== undefined) {
    setClauses.push('name = ?')
    params.push(customer.name)
  }
  if (customer.address !== undefined) {
    setClauses.push('address = ?')
    params.push(customer.address)
  }
  if (customer.taxNumber !== undefined) {
    setClauses.push('tax_number = ?')
    params.push(customer.taxNumber)
  }
  if (customer.phone !== undefined) {
    setClauses.push('phone = ?')
    params.push(customer.phone)
  }
  if (customer.status !== undefined) {
    setClauses.push('status = ?')
    params.push(customer.status)
  }

  if (setClauses.length === 0) return // No fields to update

  const query = `
    UPDATE ${tables.customers}
    SET ${setClauses.join(', ')}
    WHERE id = ?;
  `
  params.push(customer.id)

  try {
    await executeQuery(query, params)
  } catch (err) {
    console.error(`Failed to update customer with ID ${customer.id}:`, err)
    throw err
  }
}

export async function getCustomerDivisionsByCustomerId(
  customerId: number,
): Promise<CustomerDivision[]> {
  const { executeQuery, tables } = useSQLite()
  const query = `
    SELECT id, code, name, description, customer_id, status
    FROM ${tables.customer_divisions}
    WHERE customer_id = ?;
  `
  const params = [customerId]

  try {
    const response = await executeQuery(query, params)
    if (response?.result?.resultRows && Array.isArray(response.result.resultRows)) {
      return response.result.resultRows.map((row: unknown[]) => {
        return {
          id: row[0] as number,
          code: row[1] as string,
          name: row[2] as string,
          description: row[3] as string,
          customerId: row[4] as number, // Should always be present
          status: row[5] as EntityStatus,
        } as CustomerDivision
      })
    }
    return [] // Return an empty array if no divisions found or data is not in expected format
  } catch (err) {
    console.error(`Failed to get divisions for customer ID ${customerId}:`, err)
    throw err // Re-throw the error
  }
}

export async function getCustomerDivisionById(id: number): Promise<CustomerDivision | null> {
  const { executeQuery, tables } = useSQLite()
  const query = `SELECT id, code, name, description, customer_id, status FROM ${tables.customer_divisions} WHERE id = ?`
  try {
    const response = await executeQuery(query, [id])
    if (response?.result?.resultRows && response.result.resultRows.length > 0) {
      const row = response.result.resultRows[0]
      return {
        id: row[0] as number,
        code: row[1] as string,
        name: row[2] as string,
        description: row[3] as string,
        customerId: row[4] as number,
        status: row[5] as EntityStatus,
      } as CustomerDivision
    }
    return null
  } catch (err) {
    console.error(`Failed to get customer division with ID ${id}:`, err)
    throw err
  }
}

export async function addCustomerDivision(
  divisionData: Omit<CustomerDivision, 'id'>,
): Promise<void> {
  const { executeQuery, tables } = useSQLite()
  // status will be 'new' by default from DB schema
  const query = `
    INSERT INTO ${tables.customer_divisions} (code, name, description, customer_id)
    VALUES (?, ?, ?, ?);
  `
  const params = [
    divisionData.code,
    divisionData.name,
    divisionData.description,
    divisionData.customerId,
  ]

  try {
    await executeQuery(query, params)
  } catch (err) {
    console.error('Failed to add customer division:', err)
    throw err // Re-throw the error to be handled by the caller
  }
}

export async function updateCustomerDivision(division: CustomerDivision): Promise<void> {
  const { executeQuery, tables } = useSQLite()
  // Build SET clauses dynamically
  const setClauses: string[] = []
  const params: unknown[] = []

  if (division.code !== undefined) {
    setClauses.push('code = ?')
    params.push(division.code)
  }
  if (division.name !== undefined) {
    setClauses.push('name = ?')
    params.push(division.name)
  }
  if (division.description !== undefined) {
    setClauses.push('description = ?')
    params.push(division.description)
  }
  if (division.customerId !== undefined) {
    setClauses.push('customer_id = ?')
    params.push(division.customerId)
  }
  if (division.status !== undefined) {
    setClauses.push('status = ?')
    params.push(division.status)
  }

  if (setClauses.length === 0) return

  const query = `
    UPDATE ${tables.customer_divisions}
    SET ${setClauses.join(', ')}
    WHERE id = ?;
  `
  params.push(division.id)

  try {
    await executeQuery(query, params)
  } catch (err) {
    console.error(`Failed to update customer division with ID ${division.id}:`, err)
    throw err
  }
}

export async function deleteCustomerDivision(divisionId: number): Promise<void> {
  const { executeQuery, tables } = useSQLite()
  const division = await getCustomerDivisionById(divisionId)

  if (division && division.status === 'active') {
    throw new Error(
      `Active customer division "${division.name}" cannot be deleted. Please set it to inactive first.`,
    )
  }
  const query = `DELETE FROM ${tables.customer_divisions} WHERE id = ?;`
  const params = [divisionId]

  try {
    await executeQuery(query, params)
  } catch (err) {
    console.error(`Failed to delete customer division with ID ${divisionId}:`, err)
    throw err
  }
}

export async function deleteCustomer(id: number): Promise<void> {
  const { executeQuery, tables } = useSQLite()
  const customer = await getCustomerById(id) // Fetch customer to check status

  if (customer && customer.status === 'active') {
    throw new Error(
      `Active customer "${customer.name}" cannot be deleted. Please set it to inactive first.`,
    )
  }
  const query = `DELETE FROM ${tables.customers} WHERE id = ?;`
  const params = [id]

  try {
    await executeQuery(query, params)
  } catch (err) {
    console.error(`Failed to delete customer with ID ${id}:`, err)
    throw err
  }
}
