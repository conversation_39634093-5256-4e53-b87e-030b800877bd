/**
 * @fileoverview Inbound List Service - Database operations for inbound data listing and management
 *
 * This service provides comprehensive functionality for viewing, searching, and managing
 * inbound invoice records. It supports advanced filtering, pagination, and data retrieval
 * operations for the inbound data listing interface.
 *
 * @module InboundListService
 * @version 1.0.0
 * <AUTHOR> Development Team
 * @since 2024
 *
 * Key Features:
 * - Advanced search and filtering capabilities
 * - Pagination support for large datasets
 * - Customer and division data management
 * - Record deletion and management
 * - Comprehensive data mapping and transformation
 * - Database initialization and maintenance
 *
 * Dependencies:
 * - @IVC/hooks/useSQLite - SQLite database operations
 *
 * Database Tables:
 * - customer_inbound - Main inbound transaction records
 * - customers - Customer master data
 * - customer_divisions - Division information per customer
 */

import { useSQLite } from '@IVC/hooks/useSQLite'
import { minusStock } from './ImportOutboundService'

const { executeQuery } = useSQLite()

// ============================================================================
// INTERFACE DEFINITIONS
// ============================================================================

/**
 * Inbound record interface representing complete inbound transaction data
 * @interface InboundRecord
 */
export interface InboundRecord {
  /** Unique record identifier */
  id: number
  /** Customer identifier */
  customer_id: number
  /** Division identifier (optional) */
  division_id: number | null
  /** Customer name for display purposes */
  customer_name?: string
  /** Division name for display purposes */
  division_name?: string
  /** AVR AS number */
  avr_as_num: string
  /** AVR ASLN number */
  avr_asln_num: number
  /** AVR ASSQ number */
  avr_assq_num: number
  /** AVH arrival date */
  avh_arv_ymd: string
  /** AVR product code */
  avr_prod_cod: number
  /** AVD product name */
  avd_prod_nam: string
  /** AVR RTPC quantity */
  avr_rtpc_qty: number
  /** Product PPC number */
  prod_ppc_num: number
  /** Product HRC1 specification */
  prod_hrc1: string
  /** AI value */
  ai: number
  /** Inner master count */
  inner_master: number
  /** Carton quantity */
  carton: number
  /** Pieces quantity */
  pcs: number
  /** Inner carton quantity */
  inner_carton: number
  /** Inner pieces quantity */
  inner_pcs: number
  /** Total carton quantity */
  total_carton: number
  /** Total pieces quantity */
  total_pcs: number
  /** Total AI value */
  total_ai: number
  /** Total cubic meters */
  total_m3: number
  /** Record creation timestamp */
  created_at: string
  /** Record last update timestamp */
  updated_at: string
}

/**
 * Customer entity interface
 * @interface Customer
 */
export interface Customer {
  /** Unique customer identifier */
  id: string
  /** Customer display name */
  name: string
}

/**
 * Division entity interface
 * @interface Division
 */
export interface Division {
  /** Unique division identifier */
  id: string
  /** Division display name */
  name: string
  /** Parent customer identifier */
  customer_id: string
}

/**
 * Search filters interface for data filtering
 * @interface SearchFilters
 */
export interface SearchFilters {
  /** Customer ID filter (optional) */
  customerId: string | null
  /** Division ID filter (optional) */
  divisionId: string | null
  /** Month filter in YYYY-MM format (optional) */
  month: string | null
}

/**
 * Pagination parameters interface
 * @interface PaginationParams
 */
export interface PaginationParams {
  /** Current page number (1-based) */
  current: number
  /** Number of records per page */
  pageSize: number
}

/**
 * Search result interface containing data and metadata
 * @interface SearchResult
 */
export interface SearchResult {
  /** Array of inbound records */
  data: InboundRecord[]
  /** Total number of records (for pagination) */
  total: number
}

// ============================================================================
// CUSTOMER AND DIVISION MANAGEMENT
// ============================================================================

/**
 * Loads all available customers from the database
 *
 * @returns {Promise<Customer[]>} Array of customer objects sorted by name
 * @throws {Error} When database query fails or connection issues occur
 *
 * @example
 * ```typescript
 * try {
 *   const customers = await loadCustomers();
 *   console.log(`Found ${customers.length} customers`);
 *   customers.forEach(customer => {
 *     console.log(`${customer.id}: ${customer.name}`);
 *   });
 * } catch (error) {
 *   console.error('Failed to load customers:', error);
 * }
 * ```
 *
 * @since 1.0.0
 */
export const loadCustomers = async (): Promise<Customer[]> => {
  try {
    const result = await executeQuery('SELECT id, name FROM customers ORDER BY name')
    return (
      result?.result?.resultRows?.map((row: unknown[]) => ({
        id: String(row[0]),
        name: String(row[1]),
      })) || []
    )
  } catch (error) {
    console.error('Error loading customers:', error)
    throw new Error('Failed to load customers')
  }
}

/**
 * Loads divisions, optionally filtered by customer ID
 *
 * @param {string} [customerId] - Optional customer ID to filter divisions
 * @returns {Promise<Division[]>} Array of division objects sorted by name
 * @throws {Error} When database query fails
 *
 * @example
 * ```typescript
 * // Load all divisions
 * const allDivisions = await loadDivisions();
 *
 * // Load divisions for specific customer
 * const customerDivisions = await loadDivisions('CUST001');
 * console.log(`Customer has ${customerDivisions.length} divisions`);
 * ```
 *
 * @since 1.0.0
 */
export const loadDivisions = async (customerId?: string): Promise<Division[]> => {
  try {
    let sql = 'SELECT id, name, customer_id FROM customer_divisions'
    const params: string[] = []

    if (customerId) {
      sql += ' WHERE customer_id = ?'
      params.push(customerId)
    }

    sql += ' ORDER BY name'

    const result = await executeQuery(sql, params)
    return (
      result?.result?.resultRows?.map((row: unknown[]) => ({
        id: String(row[0]),
        name: String(row[1]),
        customer_id: String(row[2]),
      })) || []
    )
  } catch (error) {
    console.error('Error loading divisions:', error)
    throw new Error('Failed to load divisions')
  }
}

// ============================================================================
// INBOUND DATA SEARCH AND RETRIEVAL
// ============================================================================

/**
 * Searches inbound data with filtering and pagination support
 *
 * This function provides comprehensive search capabilities for inbound records
 * with support for customer filtering, division filtering, month filtering,
 * and pagination. Results include joined customer and division names.
 *
 * @param {SearchFilters} filters - Search filter criteria
 * @param {PaginationParams} pagination - Pagination parameters
 * @returns {Promise<SearchResult>} Search results with data and total count
 * @throws {Error} When database query fails or data processing error occurs
 *
 * @example
 * ```typescript
 * const filters = {
 *   customerId: 'CUST001',
 *   divisionId: 'DIV001',
 *   month: '2024-01'
 * };
 * const pagination = { current: 1, pageSize: 20 };
 *
 * const result = await searchInboundData(filters, pagination);
 * console.log(`Found ${result.total} records, showing ${result.data.length}`);
 * ```
 *
 * @since 1.0.0
 */
export const searchInboundData = async (
  filters: SearchFilters,
  pagination: PaginationParams,
): Promise<SearchResult> => {
  try {
    let sql = `
      SELECT 
        ci.*,
        c.name as customer_name,
        cd.name as division_name
      FROM customer_inbound ci
      LEFT JOIN customers c ON ci.customer_id = c.id
      LEFT JOIN customer_divisions cd ON ci.division_id = cd.id
      WHERE 1=1
    `
    const params: (string | number)[] = []

    // Add customer filter
    if (filters.customerId) {
      sql += ' AND ci.customer_id = ?'
      params.push(filters.customerId)
    }

    // Add division filter
    if (filters.divisionId) {
      sql += ' AND ci.division_id = ?'
      params.push(filters.divisionId)
    }

    // Add month filter
    if (filters.month) {
      sql += ` AND strftime('%Y-%m', ci.avh_arv_ymd) = ?`
      params.push(filters.month)
    }

    // Add ordering
    sql += ' ORDER BY ci.created_at DESC'

    // Add pagination
    const offset = (pagination.current - 1) * pagination.pageSize
    sql += ` LIMIT ? OFFSET ?`
    params.push(pagination.pageSize, offset)

    console.log('Search SQL:', sql)
    console.log('Search params:', params)

    const result = await executeQuery(sql, params)
    const rows = result?.result?.resultRows || []

    // Get total count for pagination
    const totalCount = await getInboundTotalCount(filters)

    // Map rows to objects with correct field order based on SELECT statement
    const data: InboundRecord[] = rows.map((row: unknown[]) => ({
      id: Number(row[0]),
      customer_id: Number(row[1]),
      division_id: row[2] ? Number(row[2]) : null,
      avr_as_num: String(row[3] || ''),
      avr_asln_num: Number(row[4] || 0),
      avr_assq_num: Number(row[5] || 0),
      avh_arv_ymd: String(row[6] || ''),
      avr_prod_cod: Number(row[7] || 0),
      avd_prod_nam: String(row[8] || ''),
      avr_rtpc_qty: Number(row[9] || 0),
      prod_ppc_num: Number(row[10] || 0),
      prod_hrc1: String(row[11] || ''),
      ai: Number(row[12] || 0),
      inner_master: Number(row[13] || 0),
      carton: Number(row[14] || 0),
      pcs: Number(row[15] || 0),
      inner_carton: Number(row[16] || 0),
      inner_pcs: Number(row[17] || 0),
      total_carton: Number(row[18] || 0),
      total_pcs: Number(row[19] || 0),
      total_ai: Number(row[20] || 0),
      total_m3: Number(row[21] || 0),
      created_at: String(row[22] || ''),
      updated_at: String(row[23] || ''),
      customer_name: String(row[24] || ''),
      division_name: String(row[25] || ''),
    }))

    return {
      data,
      total: totalCount,
    }
  } catch (error) {
    console.error('Error searching inbound data:', error)
    throw new Error(
      'Failed to search data: ' + (error instanceof Error ? error.message : String(error)),
    )
  }
}

/**
 * Gets the total count of inbound records matching the search filters
 *
 * This function provides the total record count for pagination purposes
 * without returning the actual data records.
 *
 * @param {SearchFilters} filters - Search filter criteria
 * @returns {Promise<number>} Total number of matching records
 * @throws {Error} When database query fails
 *
 * @example
 * ```typescript
 * const filters = { customerId: 'CUST001', divisionId: null, month: '2024-01' };
 * const totalCount = await getInboundTotalCount(filters);
 * console.log(`Total matching records: ${totalCount}`);
 * ```
 *
 * @since 1.0.0
 */
export const getInboundTotalCount = async (filters: SearchFilters): Promise<number> => {
  try {
    let countSql = `
      SELECT COUNT(*) as total
      FROM customer_inbound ci
      LEFT JOIN customers c ON ci.customer_id = c.id
      LEFT JOIN customer_divisions cd ON ci.division_id = cd.id
      WHERE 1=1
    `
    const countParams: (string | number)[] = []

    if (filters.customerId) {
      countSql += ' AND ci.customer_id = ?'
      countParams.push(filters.customerId)
    }

    if (filters.divisionId) {
      countSql += ' AND ci.division_id = ?'
      countParams.push(filters.divisionId)
    }

    if (filters.month) {
      countSql += ` AND strftime('%Y-%m', ci.avh_arv_ymd) = ?`
      countParams.push(filters.month)
    }

    const countResult = await executeQuery(countSql, countParams)
    return Number(countResult?.result?.resultRows?.[0]?.[0] || 0)
  } catch (error) {
    console.error('Error getting inbound total count:', error)
    throw new Error('Failed to get total count')
  }
}

// ============================================================================
// INBOUND RECORD MANAGEMENT
// ============================================================================

/**
 * Deletes an inbound record by its ID
 *
 * @param {number} recordId - The unique ID of the record to delete
 * @returns {Promise<void>} Resolves when record is successfully deleted
 * @throws {Error} When database operation fails or record not found
 *
 * @example
 * ```typescript
 * try {
 *   await deleteInboundRecord(123);
 *   console.log('Record deleted successfully');
 * } catch (error) {
 *   console.error('Failed to delete record:', error);
 * }
 * ```
 *
 * @since 1.0.0
 */
export const deleteInboundRecord = async (recordId: number): Promise<void> => {
  try {
    // First, retrieve the record information before deletion
    const recordQuery = `
      SELECT avr_prod_cod, avr_rtpc_qty, customer_id
      FROM customer_inbound
      WHERE id = ?
    `

    const recordResult = await executeQuery(recordQuery, [recordId])

    if (!recordResult?.result?.resultRows || recordResult.result.resultRows.length === 0) {
      throw new Error(`Inbound record with ID ${recordId} not found`)
    }

    const record = recordResult.result.resultRows[0]
    const productCode = record[0] as string
    const avrRtpcQty = Number(record[1]) || 0
    const customerId = record[2] as string

    // Deduct stock using the retrieved information
    if (productCode && customerId && avrRtpcQty > 0) {
      const successStock = await minusStock(productCode, customerId, avrRtpcQty)
      if (successStock) {
        console.log('Stock updated successfully')
      }
    }

    // Delete the record from customer_inbound table
    await executeQuery('DELETE FROM customer_inbound WHERE id = ?', [recordId])
  } catch (error) {
    console.error('Error deleting inbound record:', error)
    throw new Error('Failed to delete record')
  }
}

// ============================================================================
// DATABASE INITIALIZATION
// ============================================================================

/**
 * Initializes the SQLite database connection
 *
 * @returns {Promise<void>} Resolves when database is initialized
 * @throws {Error} When database initialization fails
 *
 * @example
 * ```typescript
 * try {
 *   await initializeDatabase();
 *   console.log('Database initialized successfully');
 * } catch (error) {
 *   console.error('Database initialization failed:', error);
 * }
 * ```
 *
 * @since 1.0.0
 */
export const initializeDatabase = async (): Promise<void> => {
  try {
    const { initialize } = useSQLite()
    await initialize()
  } catch (error) {
    console.error('Error initializing database:', error)
    throw new Error('Failed to initialize database')
  }
}
