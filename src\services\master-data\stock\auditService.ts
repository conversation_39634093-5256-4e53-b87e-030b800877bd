// filepath: d:\02. Projects\06. NPE\Source\npe\src\services\master-data\stock\auditService.ts

import { useSQLite } from '@IVC/hooks/useSQLite'
import type {
  StockAuditBatch,
  StockAuditEntry,
  StockFieldChange,
  CreateStockAuditBatchData,
  CreateStockAuditHistoryData,
  StockOperationType,
  StockOperationSource,
} from '@IVC/types/MasterDataTypes/StockAudit'

// Re-export types for easy import
export type {
  StockOperationSource,
  StockOperationType,
  StockFieldChange,
  StockAuditEntry,
  StockAuditBatch,
}

import type { Stock } from './index'

const { executeQuery, tables } = useSQLite()

/**
 * Create a new audit batch for stock operations
 */
export async function createStockAuditBatch(
  batchData: CreateStockAuditBatchData,
): Promise<StockAuditBatch> {
  const { operationType, operationSource, performedByUserId, notes, affectedCount = 1 } = batchData

  const query = `
    INSERT INTO ${tables.stock_audit_batches} (operation_type, operation_source, performed_by_user_id, notes, affected_count)
    VALUES (?, ?, ?, ?, ?)
  `

  await executeQuery(query, [
    operationType,
    operationSource,
    performedByUserId ?? null,
    notes ?? null,
    affectedCount,
  ])

  // Get the newly created batch ID
  const idResult = await executeQuery('SELECT last_insert_rowid() as id')
  const newBatchId = idResult.result.resultRows?.[0]?.[0] as number

  if (!newBatchId) {
    throw new Error('Failed to create stock audit batch')
  }

  // Fetch the complete batch data
  const batchQuery = `
    SELECT id, operation_type, operation_source, performed_by_user_id, performed_at, notes, affected_count
    FROM ${tables.stock_audit_batches}
    WHERE id = ?
  `

  const batchResult = await executeQuery(batchQuery, [newBatchId])
  const batchRow = batchResult.result.resultRows?.[0]

  if (!batchRow) {
    throw new Error('Failed to retrieve created stock audit batch')
  }

  return {
    id: batchRow[0] as number,
    operationType: batchRow[1] as StockOperationType,
    operationSource: batchRow[2] as StockOperationSource,
    performedByUserId: batchRow[3] as number | undefined,
    performedAt: batchRow[4] as string,
    notes: batchRow[5] as string | undefined,
    affectedCount: batchRow[6] as number,
  }
}

/**
 * Create audit history entries for stock changes
 */
export async function createStockAuditHistory(
  historyData: CreateStockAuditHistoryData[],
): Promise<void> {
  if (historyData.length === 0) return

  const query = `
    INSERT INTO ${tables.stock_audit_history}
    (audit_batch_id, stock_id, operation_type, field_name, old_value, new_value)
    VALUES (?, ?, ?, ?, ?, ?)
  `

  for (const entry of historyData) {
    await executeQuery(query, [
      entry.auditBatchId,
      entry.stockId ?? null,
      entry.operationType,
      entry.fieldName ?? null,
      entry.oldValue?.toString() ?? null,
      entry.newValue?.toString() ?? null,
    ])
  }
}

/**
 * Log stock creation with audit trail
 */
export async function logStockCreation(
  stock: Stock,
  operationSource: StockOperationSource = 'MANUAL',
  performedByUserId?: number,
  notes?: string,
): Promise<StockAuditBatch> {
  // Only log if we have customer and product info (from virtual fields)
  if (!stock.customerId || !stock.productCode) {
    console.warn('Cannot create stock audit log without customer and product information')
    // Create a minimal batch for tracking purposes
    return await createStockAuditBatch({
      operationType: 'CREATE',
      operationSource,
      performedByUserId,
      notes: (notes ?? '') + ' (No audit history - missing customer/product info)',
      affectedCount: 1,
    })
  }

  // Create audit batch
  const batch = await createStockAuditBatch({
    operationType: 'CREATE',
    operationSource,
    performedByUserId,
    notes,
    affectedCount: 1,
  })

  // Create history entries for all fields of the new stock
  const historyEntries: CreateStockAuditHistoryData[] = [
    {
      auditBatchId: batch.id,
      stockId: stock.id,
      operationType: 'CREATE',
      fieldName: 'product_name',
      oldValue: null,
      newValue: stock.productName,
    },
    {
      auditBatchId: batch.id,
      stockId: stock.id,
      operationType: 'CREATE',
      fieldName: 'avr_rtpc_qty',
      oldValue: null,
      newValue: stock.avrRtpcQty,
    },
    {
      auditBatchId: batch.id,
      stockId: stock.id,
      operationType: 'CREATE',
      fieldName: 'product_ppc_num',
      oldValue: null,
      newValue: stock.productPpcNum,
    },
    {
      auditBatchId: batch.id,
      stockId: stock.id,
      operationType: 'CREATE',
      fieldName: 'product_hrc1',
      oldValue: null,
      newValue: stock.productHrc1,
    },
    {
      auditBatchId: batch.id,
      stockId: stock.id,
      operationType: 'CREATE',
      fieldName: 'product_hrc3',
      oldValue: null,
      newValue: stock.productHrc3,
    },
    {
      auditBatchId: batch.id,
      stockId: stock.id,
      operationType: 'CREATE',
      fieldName: 'product_pcm3',
      oldValue: null,
      newValue: stock.productPcm3,
    },
    {
      auditBatchId: batch.id,
      stockId: stock.id,
      operationType: 'CREATE',
      fieldName: 'location',
      oldValue: null,
      newValue: stock.location,
    },
  ]

  await createStockAuditHistory(historyEntries)
  return batch
}

/**
 * Log stock updates with audit trail
 */
export async function logStockUpdate(
  stockId: number,
  changes: StockFieldChange[],
  operationSource: StockOperationSource = 'MANUAL',
  performedByUserId?: number,
  notes?: string,
): Promise<StockAuditBatch> {
  if (changes.length === 0) {
    throw new Error('No changes to log for stock update')
  }

  // Create audit batch
  const batch = await createStockAuditBatch({
    operationType: 'UPDATE',
    operationSource,
    performedByUserId,
    notes,
    affectedCount: 1,
  })

  // Create history entries for each changed field
  const historyEntries: CreateStockAuditHistoryData[] = changes.map((change) => ({
    auditBatchId: batch.id,
    stockId,
    operationType: 'UPDATE',
    fieldName: change.fieldName,
    oldValue: change.oldValue,
    newValue: change.newValue,
  }))

  await createStockAuditHistory(historyEntries)
  return batch
}

/**
 * Log stock deletion with audit trail
 */
export async function logStockDeletion(
  stock: Stock,
  operationSource: StockOperationSource = 'MANUAL',
  performedByUserId?: number,
  notes?: string,
): Promise<StockAuditBatch> {
  // Only log if we have customer and product info (from virtual fields)
  if (!stock.customerId || !stock.productCode) {
    console.warn('Cannot create stock deletion audit log without customer and product information')
    // Create a minimal batch for tracking purposes
    return await createStockAuditBatch({
      operationType: 'DELETE',
      operationSource,
      performedByUserId,
      notes: (notes ?? '') + ' (No audit history - missing customer/product info)',
      affectedCount: 1,
    })
  }

  // Create audit batch
  const batch = await createStockAuditBatch({
    operationType: 'DELETE',
    operationSource,
    performedByUserId,
    notes,
    affectedCount: 1,
  })

  // Create history entry for deletion
  const historyEntries: CreateStockAuditHistoryData[] = [
    {
      auditBatchId: batch.id,
      stockId: stock.id,
      operationType: 'DELETE',
      fieldName: 'deleted',
      oldValue: 'active',
      newValue: 'deleted',
    },
  ]

  await createStockAuditHistory(historyEntries)
  return batch
}

/**
 * Get audit history for a specific stock item by stock ID
 */
export async function getStockAuditHistory(
  stockId: number,
  limit: number = 50,
): Promise<StockAuditEntry[]> {
  const query = `
    SELECT
      sab.id as batch_id,
      sab.operation_type,
      sab.operation_source,
      sab.performed_by_user_id,
      sab.performed_at,
      sab.notes,
      sab.affected_count,
      sah.id as history_id,
      sah.stock_id,
      sah.field_name,
      sah.old_value,
      sah.new_value,
      sah.created_at
    FROM ${tables.stock_audit_batches} sab
    JOIN ${tables.stock_audit_history} sah ON sab.id = sah.audit_batch_id
    WHERE sah.stock_id = ?
    ORDER BY sab.performed_at DESC, sah.created_at DESC
    LIMIT ?
  `

  const result = await executeQuery(query, [stockId, limit])

  if (!result?.result?.resultRows) return []

  // Group results by batch
  const batchMap = new Map<number, StockAuditEntry>()

  for (const row of result.result.resultRows) {
    const batchId = row[0] as number

    if (!batchMap.has(batchId)) {
      batchMap.set(batchId, {
        batch: {
          id: batchId,
          operationType: row[1] as StockOperationType,
          operationSource: row[2] as StockOperationSource,
          performedByUserId: row[3] as number | undefined,
          performedAt: row[4] as string,
          notes: row[5] as string | undefined,
          affectedCount: row[6] as number,
        },
        changes: [],
      })
    }

    const entry = batchMap.get(batchId)!
    entry.changes.push({
      id: row[7] as number,
      auditBatchId: batchId,
      stockId: row[8] as number | undefined,
      customerId: 0, // No longer available in audit history
      productCode: '', // No longer available in audit history
      operationType: row[1] as StockOperationType,
      fieldName: row[9] as string | undefined,
      oldValue: row[10] as string | undefined,
      newValue: row[11] as string | undefined,
      createdAt: row[12] as string,
    })
  }

  return Array.from(batchMap.values())
}

/**
 * Get all audit batches with pagination
 */
export async function getStockAuditBatches(
  customerId?: number,
  limit: number = 50,
  offset: number = 0,
): Promise<{ batches: StockAuditBatch[]; total: number }> {
  // Build query with optional customer filter
  let query = `
    SELECT DISTINCT sab.id, sab.operation_type, sab.operation_source, sab.performed_by_user_id,
           sab.performed_at, sab.notes, sab.affected_count
    FROM ${tables.stock_audit_batches} sab
  `

  let countQuery = `
    SELECT COUNT(DISTINCT sab.id) as total
    FROM ${tables.stock_audit_batches} sab
  `

  const params: (number | undefined)[] = []

  if (customerId !== undefined) {
    query += `
      JOIN ${tables.stock_audit_history} sah ON sab.id = sah.audit_batch_id
      WHERE sah.customer_id = ?
    `
    countQuery += `
      JOIN ${tables.stock_audit_history} sah ON sab.id = sah.audit_batch_id
      WHERE sah.customer_id = ?
    `
    params.push(customerId)
  }

  query += ` ORDER BY sab.performed_at DESC LIMIT ? OFFSET ?`
  params.push(limit, offset)

  // Get total count
  const countResult = await executeQuery(countQuery, customerId !== undefined ? [customerId] : [])
  const total = (countResult?.result?.resultRows?.[0]?.[0] as number) || 0

  // Get batches
  const result = await executeQuery(query, params)

  if (!result?.result?.resultRows) {
    return { batches: [], total: 0 }
  }

  const batches: StockAuditBatch[] = result.result.resultRows.map((row: unknown[]) => ({
    id: row[0] as number,
    operationType: row[1] as StockOperationType,
    operationSource: row[2] as StockOperationSource,
    performedByUserId: row[3] as number | undefined,
    performedAt: row[4] as string,
    notes: row[5] as string | undefined,
    affectedCount: row[6] as number,
  }))

  return { batches, total }
}

/**
 * Compare two stock objects and return field changes
 */
export function compareStockForChanges(
  oldStock: Stock,
  newStock: Partial<Stock>,
): StockFieldChange[] {
  const changes: StockFieldChange[] = []

  // Define comparable fields
  const comparableFields: (keyof Stock)[] = [
    'productName',
    'avrRtpcQty',
    'productPpcNum',
    'productHrc1',
    'productHrc3',
    'productPcm3',
    'location',
  ]

  for (const field of comparableFields) {
    if (newStock[field] !== undefined && oldStock[field] !== newStock[field]) {
      const oldValue = oldStock[field]
      const newValue = newStock[field]

      // Convert values to audit-compatible types
      const convertValue = (
        value: string | number | boolean | null | undefined,
      ): string | number | null => {
        if (value === null || value === undefined) return null
        if (typeof value === 'boolean') return String(value)
        return value
      }

      changes.push({
        fieldName: field,
        oldValue: convertValue(oldValue),
        newValue: convertValue(newValue),
      })
    }
  }

  return changes
}

/**
 * Get the previous value of a specific field for a stock item from audit history
 */
export async function getPreviousFieldValue(
  stockId: number,
  fieldName: string,
): Promise<string | null> {
  const query = `
    SELECT sah.old_value
    FROM ${tables.stock_audit_batches} sab
    JOIN ${tables.stock_audit_history} sah ON sab.id = sah.audit_batch_id
    WHERE sah.stock_id = ? AND sah.field_name = ? AND sah.old_value IS NOT NULL
    ORDER BY sab.performed_at DESC, sah.created_at DESC
    LIMIT 1
  `

  try {
    const result = await executeQuery(query, [stockId, fieldName])
    if (!result?.result?.resultRows || result.result.resultRows.length === 0) return null

    return result.result.resultRows[0][0] as string | null
  } catch (err) {
    console.error('Failed to get previous field value:', err)
    return null
  }
}

/**
 * Get the previous Pieces Per Carton (PPC) value for a stock item
 */
export async function getPreviousPpcValue(stockId: number): Promise<string | null> {
  return await getPreviousFieldValue(stockId, 'product_ppc_num')
}

/**
 * Get the previous AVR RTPC QTY value for a stock item
 */
export async function getPreviousAvrRtpcQty(stockId: number): Promise<number | null> {
  const previousValue = await getPreviousFieldValue(stockId, 'avr_rtpc_qty')
  if (!previousValue) return null

  const numValue = parseFloat(previousValue)
  return isNaN(numValue) ? null : numValue
}
