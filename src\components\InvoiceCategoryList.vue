<template>
  <div class="invoice-category-list">
    <div v-for="category in categories" :key="category.id" class="category-section">
      <div class="category-header">
        <div class="category-title">
          <span class="category-name">{{ category.name }}</span>
          <a-tooltip v-if="!readonly" title="Delete Category">
            <a-button
              type="text"
              danger
              @click="handleDeleteCategory(category.id)"
              class="delete-category-btn"
            >
              <template #icon><DeleteOutlined /></template>
            </a-button>
          </a-tooltip>
        </div>
        <a-tooltip v-if="!readonly" title="Add New Charge">
          <a-button
            type="primary"
            ghost
            @click="handleAddCharge(category.id)"
            class="add-charge-btn"
          >
            <template #icon><PlusOutlined /></template>
            Add Charge
          </a-button>
        </a-tooltip>
      </div>

      <a-table
        :columns="columns"
        :data-source="category.charges"
        row-key="id"
        size="middle"
        :pagination="false"
        :scroll="{ x: 1200 }"
        class="charge-table"
        :row-class-name="getRowClassName"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'calculationType'">
            <a-tooltip :title="getCalculationType(record.workoutId)">
              <a-tag :color="getCalculationTypeColor(record.workoutId)">
                {{ getCalculationType(record.workoutId) }}
              </a-tag>
            </a-tooltip>
          </template>
          <template v-else-if="column.key === 'workout'">
            <template v-if="readonly">
              <div class="workout-cell">
                <div class="workout-text-container">
                  <a-tooltip :title="getWorkoutName(record.workoutId)">
                    <span class="workout-text">{{ getWorkoutName(record.workoutId) }}</span>
                  </a-tooltip>
                  <a-tooltip
                    v-if="isLinkedWorkout(record.workoutId)"
                    :title="getLinkedWorkoutTooltip(record.workoutId)"
                  >
                    <LinkOutlined class="link-icon" />
                  </a-tooltip>
                </div>
              </div>
            </template>
            <template v-else>
              <a-form-item
                :validate-status="
                  errors?.[record.id]?.includes('Workout is required') ? 'error' : ''
                "
                :help="
                  errors?.[record.id]?.includes('Workout is required') ? 'Workout is required' : ''
                "
                class="table-form-item"
              >
                <a-select
                  v-model:value="record.workoutId"
                  class="fixed-width-select"
                  @change="(value: number) => handleWorkoutChange(category.id, record, value)"
                  placeholder="Select workout"
                  show-search
                  :filter-option="filterOption"
                >
                  <a-select-option
                    v-for="w in categoryWorkouts(category.categoryId)"
                    :key="w.id"
                    :value="w.id"
                  >
                    {{ w.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </template>
          </template>
          <template v-else-if="column.key === 'unit'">
            <a-tooltip :title="getUnitName(record.unitId)">
              <span class="ellipsis-cell">{{ getUnitName(record.unitId) }}</span>
            </a-tooltip>
          </template>
          <template v-else-if="column.key === 'unitPrice'">
            <a-tooltip :title="formatCurrency(record.unitPrice)">
              <span class="numeric-cell">{{ formatCurrency(record.unitPrice) }}</span>
            </a-tooltip>
          </template>
          <template v-else-if="column.key === 'quantity'">
            <template v-if="readonly">
              <a-tooltip :title="formatNumber(record.quantity)">
                <span class="numeric-cell">{{ formatNumber(record.quantity) }}</span>
              </a-tooltip>
            </template>
            <template v-else>
              <div class="input-with-error">
                <a-input-number
                  v-model:value="record.quantity"
                  :precision="2"
                  :status="
                    errors?.[record.id]?.includes('Quantity must be greater than 0') ? 'error' : ''
                  "
                  :disabled="isAutoCalculated(record.workoutId)"
                  @change="(value: number) => handleQuantityChange(category.id, record, value)"
                  placeholder="Enter quantity"
                  class="quantity-input"
                />
                <div
                  v-if="errors?.[record.id]?.includes('Quantity must be greater than 0')"
                  class="field-error-message"
                >
                  Quantity must be greater than 0
                </div>
              </div>
            </template>
          </template>
          <template v-else-if="column.key === 'total'">
            <a-tooltip :title="formatCurrency(record.totalAmount)">
              <span class="numeric-cell total-amount">{{
                formatCurrency(record.totalAmount)
              }}</span>
            </a-tooltip>
          </template>
          <template v-else-if="column.key === 'vatRate'">
            <a-tooltip :title="getVatRate(record.workoutId) + '%'">
              <span class="numeric-cell">{{ getVatRate(record.workoutId) }}%</span>
            </a-tooltip>
          </template>
          <template v-else-if="column.key === 'vatAmount'">
            <a-tooltip :title="formatCurrency(calculateVatAmount(record))">
              <span class="numeric-cell">{{ formatCurrency(calculateVatAmount(record)) }}</span>
            </a-tooltip>
          </template>
          <template v-else-if="column.key === 'debitCode'">
            <a-tooltip :title="getDebitCodeName(record.debitCodeId)">
              <span class="ellipsis-cell">{{ getDebitCodeName(record.debitCodeId) }}</span>
            </a-tooltip>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-tooltip v-if="!readonly" title="Delete Charge">
              <a-button
                type="text"
                danger
                size="small"
                @click="$emit('delete-charge', category.id, record.id)"
              >
                <template #icon><DeleteOutlined /></template>
              </a-button>
            </a-tooltip>
          </template>
        </template>
        <template #footer="{ record }">
          <div v-if="record && getErrorMessages(record).length > 0" class="error-messages">
            <div v-for="error in getErrorMessages(record)" :key="error" class="error-message">
              {{ error }}
            </div>
          </div>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { PlusOutlined, DeleteOutlined, LinkOutlined } from '@ant-design/icons-vue'
import type { InvoiceCategory, InvoiceCharge } from '../types/invoice'
import type { Category } from '../types/MasterDataTypes/Category'
import type { Unit } from '../types/MasterDataTypes/Unit'
import type { Workout } from '../types/MasterDataTypes/Category'
import type { DebitCode } from '../types/CompanyProfile'
import { useInvoiceService } from '../services/invoiceLocalService'
import { getDebitCodes } from '../services/master-data/companyProfile/companyProfileService'

const props = defineProps<{
  categories: InvoiceCategory[]
  allCategories: Category[]
  workouts: Workout[]
  units: Unit[]
  customerId?: number
  customerDivisionId?: number
  readonly?: boolean
  errors?: Record<string, string[]>
}>()

const emit = defineEmits<{
  (e: 'add-charge', categoryId: string): void
  (e: 'update-charge', categoryId: string, charge: InvoiceCharge): void
  (e: 'delete-charge', categoryId: string, chargeId: string): void
  (e: 'delete-category', categoryId: string): void
}>()

const invoiceService = useInvoiceService()
const debitCodes = ref<DebitCode[]>([])

onMounted(async () => {
  try {
    debitCodes.value = await getDebitCodes()
  } catch (error) {
    console.error('Error loading debit codes:', error)
  }
})

// Add calculation type functions
const getCalculationType = (workoutId: number | undefined) => {
  if (!workoutId) return ''
  const workout = props.workouts.find((w) => w.id === workoutId)
  return workout?.calculationType || ''
}

const getCalculationTypeColor = (workoutId: number | undefined) => {
  if (!workoutId) return ''
  const workout = props.workouts.find((w) => w.id === workoutId)
  switch (workout?.calculationType) {
    case 'Manual':
      return 'blue'
    case 'Link':
      return 'green'
    case 'Auto':
      return 'orange'
    default:
      return ''
  }
}

// Add columns definition
const columns = computed(() => {
  const baseColumns = [
    {
      title: 'Type',
      key: 'calculationType',
      dataIndex: 'workoutId',
      width: 60,
      fixed: 'left',
      ellipsis: true,
    },
    {
      title: 'Workout',
      key: 'workout',
      dataIndex: 'workoutId',
      width: 250,
      fixed: 'left',
      ellipsis: {
        showTitle: false,
      },
    },
    {
      title: 'Unit',
      key: 'unit',
      dataIndex: 'unitId',
      width: 100,
      ellipsis: true,
    },
    {
      title: 'Unit Price',
      key: 'unitPrice',
      dataIndex: 'unitPrice',
      width: 120,
      align: 'right',
      ellipsis: true,
    },
    {
      title: 'Quantity',
      key: 'quantity',
      dataIndex: 'quantity',
      width: 100,
      align: 'right',
      ellipsis: true,
    },
    {
      title: 'VAT',
      key: 'vatRate',
      dataIndex: 'vatRate',
      width: 60,
      align: 'right',
      ellipsis: true,
    },
    {
      title: 'VAT Amount',
      key: 'vatAmount',
      dataIndex: 'vatAmount',
      width: 120,
      align: 'right',
      ellipsis: true,
    },
    {
      title: 'Total',
      key: 'total',
      dataIndex: 'totalAmount',
      width: 120,
      align: 'right',
      fixed: 'right',
      ellipsis: true,
    },
    {
      title: 'Debit Code',
      key: 'debitCode',
      dataIndex: 'debitCodeId',
      width: 180,
      fixed: 'right',
      ellipsis: {
        showTitle: false,
      },
    },
  ]

  if (!props.readonly) {
    baseColumns.push({
      title: '',
      key: 'action',
      dataIndex: 'action',
      width: 60,
      align: 'center',
      fixed: 'right',
      ellipsis: true,
    })
  }

  return baseColumns
})

const categoryWorkouts = (categoryId: number) => {
  return props.workouts.filter((w) => w.categoryId === categoryId)
}

const getUnitName = (unitId: number | undefined) => {
  if (!unitId) return ''
  return props.units.find((u) => u.id === unitId)?.name || ''
}

const formatCurrency = (amount: number | undefined) => {
  if (amount === undefined) return '$0.00'
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount)
}

const handleAddCharge = (categoryId: string) => {
  emit('add-charge', categoryId)
}

// Add update tracking to prevent infinite loops
const updatingCharges = ref(new Set<string>())

const getChargeKey = (categoryId: string, chargeId: string) => `${categoryId}-${chargeId}`

const calculateLinkedQuantity = (
  sourceWorkoutId: number,
  sourceQuantity: number,
  targetWorkoutId: number,
) => {
  const sourceWorkout = props.workouts.find((w) => w.id === sourceWorkoutId)
  const targetWorkout = props.workouts.find((w) => w.id === targetWorkoutId)

  if (!sourceWorkout || !targetWorkout) return sourceQuantity

  // For now, we use direct quantity. Can be extended with more complex calculations
  return sourceQuantity
}

const findLinkedCharge = (workoutId: number): InvoiceCharge | undefined => {
  for (const category of props.categories) {
    const charge = category.charges.find((c) => c.workoutId === workoutId)
    if (charge) return charge
  }
  return undefined
}

const findLinkedCharges = (workoutId: number): { categoryId: string; charge: InvoiceCharge }[] => {
  const result: { categoryId: string; charge: InvoiceCharge }[] = []
  props.categories.forEach((category) => {
    category.charges.forEach((charge) => {
      const workout = props.workouts.find((w) => w.id === charge.workoutId)
      if (workout?.calculationType === 'Link' && workout.linkedWorkoutId === workoutId) {
        result.push({ categoryId: category.id, charge })
      }
    })
  })
  return result
}

const updateLinkedWorkouts = (sourceWorkoutId: number, sourceQuantity: number) => {
  const linkedCharges = findLinkedCharges(sourceWorkoutId)
  linkedCharges.forEach(({ categoryId, charge }) => {
    const chargeKey = getChargeKey(categoryId, charge.id)
    if (!updatingCharges.value.has(chargeKey)) {
      updatingCharges.value.add(chargeKey)
      charge.quantity = calculateLinkedQuantity(
        sourceWorkoutId,
        sourceQuantity,
        charge.workoutId || 0,
      )
      calculateTotal(charge)
      emit('update-charge', categoryId, charge)
      updatingCharges.value.delete(chargeKey)
    }
  })
}

const handleWorkoutChange = async (
  categoryId: string,
  charge: InvoiceCharge,
  workoutId: number,
) => {
  const workout = props.workouts.find((w) => w.id === workoutId)
  if (workout) {
    charge.workoutId = workoutId
    charge.unitId = workout.unitId || undefined
    charge.unitPrice = workout.unitPrice || 0
    charge.debitCodeId = workout.debitCodeId || undefined
    charge.vatRate = workout.vat || 0

    // Handle linked workout calculation
    if (workout.calculationType === 'Link' && workout.linkedWorkoutId) {
      const linkedCharge = findLinkedCharge(workout.linkedWorkoutId)
      if (linkedCharge) {
        const chargeKey = getChargeKey(categoryId, charge.id)
        if (!updatingCharges.value.has(chargeKey)) {
          updatingCharges.value.add(chargeKey)
          charge.quantity = calculateLinkedQuantity(
            workout.linkedWorkoutId,
            linkedCharge.quantity || 0,
            workoutId,
          )
          updatingCharges.value.delete(chargeKey)
        }
      }
    }
    // Handle auto calculation if needed
    else if (workout.calculationType === 'Auto' && workout.autoFocusCode && props.customerId) {
      try {
        const currentDate = new Date()
        const yearMonth = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`

        const autoValue = await invoiceService.getAutoFunctionValue({
          autoFocusCode: workout.autoFocusCode,
          customerId: props.customerId,
          divisionId: props.customerDivisionId,
          yearMonth,
        })

        if (autoValue) {
          charge.quantity = autoValue.value
        }
      } catch (error) {
        console.error('Error fetching auto function value:', error)
      }
    }

    calculateTotal(charge)
    emit('update-charge', categoryId, charge)
  }
}

const handleQuantityChange = (categoryId: string, charge: InvoiceCharge, quantity: number) => {
  const chargeKey = getChargeKey(categoryId, charge.id)
  if (!updatingCharges.value.has(chargeKey)) {
    updatingCharges.value.add(chargeKey)
    charge.quantity = quantity
    calculateTotal(charge)
    // Update any linked workouts that depend on this one
    if (charge.workoutId) {
      updateLinkedWorkouts(charge.workoutId, quantity)
    }
    emit('update-charge', categoryId, charge)
    updatingCharges.value.delete(chargeKey)
  }
}

const calculateTotal = (charge: InvoiceCharge) => {
  const baseAmount = (charge.unitPrice || 0) * (charge.quantity || 0)
  charge.totalAmount = baseAmount
  charge.vatAmount = calculateVatAmount(charge)
}

const handleDeleteCategory = (categoryId: string) => {
  emit('delete-category', categoryId)
}

const getWorkoutName = (workoutId: number | undefined) => {
  if (!workoutId) return ''
  return props.workouts.find((w) => w.id === workoutId)?.name || ''
}

const isAutoCalculated = (workoutId: number | undefined) => {
  if (!workoutId) return false
  const workout = props.workouts.find((w) => w.id === workoutId)
  return workout?.calculationType === 'Auto' || workout?.calculationType === 'Link'
}

const isLinkedWorkout = (workoutId: number | undefined) => {
  if (!workoutId) return false
  const workout = props.workouts.find((w) => w.id === workoutId)
  return workout?.calculationType === 'Link'
}

const getVatRate = (workoutId: number | undefined) => {
  if (!workoutId) return 0
  const workout = props.workouts.find((w) => w.id === workoutId)
  return ((workout?.vat || 0) * 100).toFixed(1)
}

const calculateVatAmount = (charge: InvoiceCharge) => {
  const workout = props.workouts.find((w) => w.id === charge.workoutId)
  if (!workout) return 0
  return (charge.totalAmount || 0) * (workout.vat || 0)
}

const getDebitCodeName = (debitCodeId: number | undefined) => {
  if (!debitCodeId) return ''
  const debitCode = debitCodes.value.find((d) => d.id === debitCodeId)
  return debitCode ? `${debitCode.code} - ${debitCode.name}` : `DC-${debitCodeId}`
}

const getLinkedWorkoutTooltip = (workoutId: number | undefined) => {
  if (!workoutId) return ''
  const workout = props.workouts.find((w) => w.id === workoutId)
  if (!workout?.linkedWorkoutId) return ''

  const sourceWorkout = props.workouts.find((w) => w.id === workout.linkedWorkoutId)
  return sourceWorkout ? `Linked to: ${sourceWorkout.name}` : ''
}

// Add helper function to get error class
const getErrorClass = (record: InvoiceCharge) => {
  return props.errors?.[record.id]?.length ? 'row-error' : ''
}

// Add helper function to get error messages
const getErrorMessages = (record: InvoiceCharge | undefined) => {
  if (!record) return []
  return props.errors?.[record.id] || []
}

// Add filter function for select components
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// Add row class name function
const getRowClassName = (record: any) => {
  return props.errors?.[record.id]?.length ? 'row-error' : ''
}

// Add number formatting function
const formatNumber = (value: number | undefined) => {
  if (value === undefined || value === null) return ''
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value)
}
</script>

<style scoped>
.invoice-category-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.ant-table-wrapper :deep(.ant-table) {
  overflow-x: auto;
}

.ant-table-wrapper :deep(.ant-table-tbody > tr.ant-table-placeholder) {
  height: auto !important;
}

.ant-table-wrapper :deep(.ant-table-tbody > tr.ant-table-placeholder .ant-table-cell) {
  padding: 8px !important;
}

.ant-table-wrapper
  :deep(.ant-table-tbody > tr.ant-table-placeholder .ant-table-expanded-row-fixed) {
  min-height: unset !important;
  height: auto !important;
}

.ant-table-wrapper :deep(.ant-table-tbody > tr.ant-table-placeholder .ant-empty.ant-empty-normal) {
  margin: 4px 0 !important;
}

.ant-table-wrapper :deep(.ant-table-tbody > tr.ant-table-placeholder .ant-empty-image) {
  height: 32px !important;
  margin-bottom: 4px !important;
}

.ant-table-wrapper :deep(.ant-table-tbody > tr.ant-table-placeholder .ant-empty-image svg) {
  width: 48px !important;
  height: 32px !important;
}

.ant-table-wrapper :deep(.ant-table-tbody > tr.ant-table-placeholder .ant-empty-description) {
  font-size: 13px !important;
  color: rgba(0, 0, 0, 0.45) !important;
  margin-bottom: 0 !important;
  line-height: 1.2 !important;
}

.ant-table-wrapper :deep(.ant-table-tbody > tr.ant-table-row.row-error) {
  height: auto !important;
}

.ant-table-wrapper :deep(.ant-table-tbody > tr.ant-table-row.row-error > td) {
  padding-bottom: 32px !important;
}

.ant-table-wrapper :deep(.ant-table-cell-fix-left),
.ant-table-wrapper :deep(.ant-table-cell-fix-right) {
  background: #fff !important;
}

.category-section {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  min-height: 200px;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fafafa;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.category-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-name {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.delete-category-btn {
  padding: 4px;
  margin-left: 8px;
}

.add-charge-btn {
  display: flex;
  align-items: center;
  gap: 4px;
}

.charge-table :deep(.ant-table) {
  border-radius: 0;
}

.charge-table :deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
  white-space: nowrap;
  padding: 12px 16px;
  transition: background 0.3s;
}

.charge-table :deep(.ant-table-thead > tr > th:hover) {
  background: #f5f5f5;
}

.charge-table :deep(.ant-table-tbody > tr > td) {
  vertical-align: middle;
  padding: 8px 16px;
  max-width: 300px;
}

.charge-table :deep(.ant-table-cell) {
  vertical-align: middle;
}

.charge-table :deep(.ellipsis-cell) {
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 100%;
}

.charge-table :deep(.numeric-cell) {
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: right;
  padding-right: 8px;
}

.charge-table :deep(.ant-form-item) {
  margin-bottom: 0;
  display: flex;
  align-items: center;
}

.charge-table :deep(.ant-select) {
  width: 100%;
}

.charge-table :deep(.input-with-error) {
  display: flex;
  align-items: center;
}

.total-amount {
  font-weight: 600;
  color: #262626;
}

.input-with-error {
  position: relative;
  min-height: 32px;
}

.field-error-message {
  position: absolute;
  left: 0;
  bottom: -24px;
  color: #ff4d4f;
  font-size: 12px;
  line-height: 1.2;
  white-space: nowrap;
  z-index: 21;
}

.ant-table-cell-fix-left :deep(.field-error-message),
.ant-table-cell-fix-right :deep(.field-error-message),
.ant-table-cell-fix-left :deep(.ant-form-item-explain),
.ant-table-cell-fix-right :deep(.ant-form-item-explain) {
  z-index: 21;
}

.ant-form-item {
  margin-bottom: 0;
}

.ant-table-cell {
  overflow: visible !important;
}

.ant-table-cell-fix-left-last,
.ant-table-cell-fix-right-first {
  overflow: visible !important;
}

.quantity-input {
  width: 100% !important;
}

.quantity-input :deep(.ant-input-number-input) {
  text-align: right;
}

.table-form-item {
  margin-bottom: 0;
}

.table-form-item :deep(.ant-form-item-explain) {
  position: absolute;
  font-size: 12px;
  padding-top: 2px;
}

.ant-tag {
  margin: 0;
  text-transform: capitalize;
}

.ant-select {
  width: 100%;
}

.ant-input-number-focused,
.ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input)
  .ant-select-selector {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.error-messages {
  padding: 8px;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  margin-top: 4px;
}

.error-message {
  color: #ff4d4f;
  font-size: 12px;
  line-height: 1.5;
}

.ant-tooltip {
  max-width: 500px;
}

.ant-tooltip-inner {
  min-height: 32px;
  padding: 6px 12px;
  color: #fff;
  text-align: left;
  text-decoration: none;
  word-wrap: break-word;
  background-color: rgba(0, 0, 0, 0.85);
  border-radius: 4px;
  box-shadow:
    0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

/* Remove unwanted borders and lines */
.ant-table-wrapper :deep(.ant-table-cell) {
  border: none !important;
}

.ant-table-wrapper :deep(.ant-table-thead > tr > th) {
  border-bottom: 1px solid #f0f0f0 !important;
}

.ant-table-wrapper :deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid #f0f0f0 !important;
}

.ant-table-wrapper :deep(.ant-table-cell-fix-left),
.ant-table-wrapper :deep(.ant-table-cell-fix-right) {
  border-right: 1px solid #f0f0f0 !important;
}

.fixed-width-select {
  width: 300px !important;
}

.fixed-width-select :deep(.ant-select-selector) {
  max-width: 300px;
}

.fixed-width-select :deep(.ant-select-selection-item) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.workout-cell {
  width: 100%;
  position: relative;
}

.workout-text-container {
  display: flex;
  align-items: center;
  gap: 4px;
  max-width: calc(100% - 4px);
}

.workout-text {
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
}

.link-icon {
  flex-shrink: 0;
  color: #1890ff;
  font-size: 14px;
}
</style>
