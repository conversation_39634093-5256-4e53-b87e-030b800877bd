<template>
  <a-modal
    :visible="visible"
    title="Select Categories"
    @ok="handleOk"
    @cancel="handleCancel"
    :confirmLoading="confirmLoading"
  >
    <div class="category-list">
      <a-checkbox-group v-model:value="selectedCategoryIds" class="checkbox-group">
        <div v-for="category in availableCategories" :key="category.id" class="category-item">
          <a-checkbox :value="category.id">{{ category.name }}</a-checkbox>
        </div>
      </a-checkbox-group>
    </div>
    <template #footer>
      <div class="modal-footer">
        <span class="selected-count" v-if="selectedCategoryIds.length > 0">
          {{ selectedCategoryIds.length }} categories selected
        </span>
        <div class="footer-buttons">
          <a-button @click="handleCancel">Cancel</a-button>
          <a-button
            type="primary"
            :disabled="selectedCategoryIds.length === 0"
            :loading="confirmLoading"
            @click="handleOk"
          >
            Add Categories
          </a-button>
        </div>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { InvoiceCategory } from '../../types/invoice'
import type { Category } from '../../types/MasterDataTypes/Category'

const props = defineProps<{
  visible: boolean
  allCategories: Category[]
  currentCategories: InvoiceCategory[]
}>()

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
  (e: 'select', categoryIds: number[]): void
}>()

const confirmLoading = ref(false)
const selectedCategoryIds = ref<number[]>([])

// Compute available categories (categories that haven't been added yet)
const availableCategories = computed(() => {
  return props.allCategories.filter(
    (cat) => !props.currentCategories.some((c) => c.categoryId === cat.id),
  )
})

const handleOk = async () => {
  if (selectedCategoryIds.value.length === 0) return

  confirmLoading.value = true
  try {
    emit('select', selectedCategoryIds.value)
    emit('update:visible', false)
  } finally {
    confirmLoading.value = false
    selectedCategoryIds.value = []
  }
}

const handleCancel = () => {
  emit('update:visible', false)
  selectedCategoryIds.value = []
}
</script>

<style scoped>
.category-list {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.category-item {
  display: block;
  padding: 8px 12px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  transition: all 0.3s;
}

.category-item:hover {
  background-color: #f5f5f5;
}

.modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selected-count {
  color: #8c8c8c;
  font-size: 14px;
}

.footer-buttons {
  display: flex;
  gap: 8px;
}
</style>
