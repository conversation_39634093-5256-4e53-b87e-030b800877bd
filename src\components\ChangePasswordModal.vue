<template>
  <a-modal
    v-model:open="visible"
    title="Change Password"
    @ok="handleChangePassword"
    @cancel="handleCancel"
    :ok-text="'Change Password'"
    :cancel-text="'Cancel'"
    :confirm-loading="loading"
  >
    <a-form layout="vertical" ref="formRef" :model="formData" :rules="formRules">
      <a-form-item label="Current Password" name="currentPassword">
        <a-input-password
          v-model:value="formData.currentPassword"
          placeholder="Enter current password"
        />
      </a-form-item>

      <a-form-item label="New Password" name="newPassword">
        <a-input-password v-model:value="formData.newPassword" placeholder="Enter new password" />
      </a-form-item>

      <a-form-item label="Confirm New Password" name="confirmPassword">
        <a-input-password
          v-model:value="formData.confirmPassword"
          placeholder="Confirm new password"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { message } from 'ant-design-vue'
import { useAuthStore } from '../stores/auth'
import { changeUserPassword } from '../services/master-data/user/index'

// Props
interface Props {
  open: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:open': [value: boolean]
  success: []
}>()

// Auth store
const authStore = useAuthStore()

// Reactive data
const loading = ref(false)
const formRef = ref()
const visible = ref(props.open)

// Form data
const formData = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
})

// Form validation rules
const formRules = {
  currentPassword: [
    { required: true, message: 'Please enter your current password', trigger: 'blur' },
  ],
  newPassword: [
    { required: true, message: 'Please enter a new password', trigger: 'blur' },
    { min: 6, message: 'Password must be at least 6 characters', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: 'Please confirm your new password', trigger: 'blur' },
    {
      validator: (_rule: any, value: string) => {
        if (value && value !== formData.newPassword) {
          return Promise.reject('Passwords do not match')
        }
        return Promise.resolve()
      },
      trigger: 'blur',
    },
  ],
}

// Watch props.open to sync with local visible
watch(
  () => props.open,
  (newVal) => {
    visible.value = newVal
  },
)

// Watch local visible to emit update
watch(visible, (newVal) => {
  emit('update:open', newVal)
})

// Reset form
const resetForm = () => {
  Object.assign(formData, {
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  })
  formRef.value?.resetFields()
}

// Handle change password
const handleChangePassword = async () => {
  try {
    await formRef.value.validate()
    loading.value = true

    // Get current user ID from auth store
    const currentUser = authStore.user
    if (!currentUser) {
      message.error('User not authenticated')
      return
    }

    // Change password using SQLite service
    await changeUserPassword(currentUser.id, formData.currentPassword, formData.newPassword)

    // Close modal and reset form
    visible.value = false
    resetForm()

    // Emit success event
    emit('success')
  } catch (error) {
    console.error('Password change failed:', error)
    // Error message is already shown by the service
  } finally {
    loading.value = false
  }
}

// Handle cancel
const handleCancel = () => {
  visible.value = false
  resetForm()
}
</script>

<style scoped>
/* Add any specific styles for the modal here */
</style>
