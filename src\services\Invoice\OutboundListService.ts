/**
 * @fileoverview Outbound List Service - Database operations for outbound data listing and management
 *
 * This service provides comprehensive functionality for viewing, searching, and managing
 * outbound invoice records. It supports advanced filtering, pagination, and data retrieval
 * operations for the outbound data listing interface.
 *
 * @module OutboundListService
 * @version 1.0.0
 * <AUTHOR> Development Team
 * @since 2024
 *
 * Key Features:
 * - Advanced search and filtering capabilities
 * - Pagination support for large datasets
 * - Customer and division data management
 * - Record deletion and management
 * - Comprehensive data mapping and transformation
 * - Database initialization and maintenance
 *
 * Dependencies:
 * - @IVC/hooks/useSQLite - SQLite database operations
 *
 * Database Tables:
 * - customer_outbound - Main outbound transaction records
 * - customers - Customer master data
 * - customer_divisions - Division information per customer
 */

import { useSQLite } from '@IVC/hooks/useSQLite'
import { addStock } from './ImportInboundService'

// ============================================================================
// INTERFACE DEFINITIONS
// ============================================================================

/**
 * Outbound record interface representing complete outbound transaction data
 * @interface OutboundRecord
 */
export interface OutboundRecord {
  /** Unique record identifier */
  id: number
  /** Customer identifier */
  customer_id: number
  /** Division identifier (optional) */
  division_id?: number
  /** Customer name for display purposes */
  customer_name?: string
  /** Division name for display purposes */
  division_name?: string
  /** SPR AS number */
  spr_as_num: string
  /** SPR ASLN number */
  spr_asln_num: number
  /** SPR ASSQ number */
  spr_assq_num: number
  /** SPH ship date */
  sph_ship_ymd: string
  /** SPH delivery code */
  sph_dlv_cod: number
  /** SPH delivery name 1 */
  sph_dlv_nam1: string
  /** SPR product code */
  spr_prod_code: number
  /** SPD product name */
  spd_prod_nam: string
  /** SPR RTPC quantity */
  spr_rtpc_qty: number
  /** Product PPC number */
  prod_ppc_num: number
  /** Product HRC1 specification */
  prod_hrc1: string
  /** AI value */
  ai: number
  /** Inner master count */
  inner_master: number
  /** Carton quantity */
  carton: number
  /** Pieces quantity */
  pcs: number
  /** Inner carton quantity */
  inner_carton: number
  /** Inner pieces quantity */
  inner_pcs: number
  /** Total carton quantity */
  total_carton: number
  /** Total pieces quantity */
  total_pcs: number
  /** Total AI value */
  total_ai: number
  /** Total cubic meters */
  total_m3: number
  /** Record creation timestamp */
  created_at: string
  /** Record last update timestamp */
  updated_at: string
}

/**
 * Customer entity interface
 * @interface Customer
 */
export interface Customer {
  /** Unique customer identifier */
  id: string
  /** Customer display name */
  name: string
}

/**
 * Division entity interface
 * @interface Division
 */
export interface Division {
  /** Unique division identifier */
  id: string
  /** Division display name */
  name: string
  /** Parent customer identifier */
  customer_id: string
}

/**
 * Search filters interface for data filtering
 * @interface SearchFilters
 */
export interface SearchFilters {
  /** Customer ID filter (optional) */
  customerId?: string | null
  /** Division ID filter (optional) */
  divisionId?: string | null
  /** Month filter in YYYY-MM format (optional) */
  month?: string | null
}

/**
 * Pagination parameters interface
 * @interface PaginationParams
 */
export interface PaginationParams {
  /** Current page number (1-based) */
  current: number
  /** Number of records per page */
  pageSize: number
}

/**
 * Search result interface containing data and metadata
 * @interface SearchResult
 */
export interface SearchResult {
  /** Array of outbound records */
  data: OutboundRecord[]
  /** Total number of records (for pagination) */
  total: number
}

// SQLite instance
const { executeQuery, initialize } = useSQLite()

// ============================================================================
// DATABASE INITIALIZATION
// ============================================================================

/**
 * Initializes the SQLite database connection
 *
 * @returns {Promise<void>} Resolves when database is initialized
 * @throws {Error} When database initialization fails
 *
 * @example
 * ```typescript
 * try {
 *   await initializeDatabase();
 *   console.log('Database initialized successfully');
 * } catch (error) {
 *   console.error('Database initialization failed:', error);
 * }
 * ```
 *
 * @since 1.0.0
 */
export const initializeDatabase = async (): Promise<void> => {
  try {
    await initialize()
  } catch (error) {
    console.error('Error initializing database:', error)
    throw new Error('Failed to initialize database')
  }
}

// ============================================================================
// CUSTOMER AND DIVISION MANAGEMENT
// ============================================================================

/**
 * Loads all available customers from the database
 *
 * @returns {Promise<Customer[]>} Array of customer objects with id and name
 * @throws {Error} When database query fails or connection issues occur
 *
 * @example
 * ```typescript
 * try {
 *   const customers = await loadCustomers();
 *   console.log(`Found ${customers.length} customers`);
 *   customers.forEach(customer => {
 *     console.log(`${customer.id}: ${customer.name}`);
 *   });
 * } catch (error) {
 *   console.error('Failed to load customers:', error);
 * }
 * ```
 *
 * @since 1.0.0
 */
export const loadCustomers = async (): Promise<Customer[]> => {
  try {
    const result = await executeQuery('SELECT id, name FROM customers')
    return (
      result?.result?.resultRows?.map((row: unknown[]) => ({
        id: String(row[0]),
        name: String(row[1]),
      })) || []
    )
  } catch (error) {
    console.error('Error loading customers:', error)
    throw new Error('Failed to load customers')
  }
}

/**
 * Loads divisions, optionally filtered by customer ID
 *
 * @param {string} [customerId] - Optional customer ID to filter divisions
 * @returns {Promise<Division[]>} Array of division objects
 * @throws {Error} When database query fails
 *
 * @example
 * ```typescript
 * // Load all divisions
 * const allDivisions = await loadDivisions();
 *
 * // Load divisions for specific customer
 * const customerDivisions = await loadDivisions('CUST001');
 * console.log(`Customer has ${customerDivisions.length} divisions`);
 * ```
 *
 * @since 1.0.0
 */
export const loadDivisions = async (customerId?: string): Promise<Division[]> => {
  try {
    let sql = 'SELECT id, name, customer_id FROM customer_divisions'
    const params: string[] = []

    if (customerId) {
      sql += ' WHERE customer_id = ?'
      params.push(customerId)
    }

    const result = await executeQuery(sql, params)
    return (
      result?.result?.resultRows?.map((row: unknown[]) => ({
        id: String(row[0]),
        name: String(row[1]),
        customer_id: String(row[2]),
      })) || []
    )
  } catch (error) {
    console.error('Error loading divisions:', error)
    throw new Error('Failed to load divisions')
  }
}

// ============================================================================
// OUTBOUND DATA SEARCH AND RETRIEVAL
// ============================================================================

/**
 * Searches outbound data with filtering and pagination support
 *
 * This function provides comprehensive search capabilities for outbound records
 * with support for customer filtering, division filtering, month filtering,
 * and pagination. Results include joined customer and division names.
 *
 * @param {SearchFilters} filters - Search filter criteria
 * @param {PaginationParams} pagination - Pagination parameters
 * @returns {Promise<SearchResult>} Search results with data and total count
 * @throws {Error} When database query fails or data processing error occurs
 *
 * @example
 * ```typescript
 * const filters = {
 *   customerId: 'CUST001',
 *   divisionId: 'DIV001',
 *   month: '2024-01'
 * };
 * const pagination = { current: 1, pageSize: 20 };
 *
 * const result = await searchOutboundData(filters, pagination);
 * console.log(`Found ${result.total} records, showing ${result.data.length}`);
 * ```
 *
 * @since 1.0.0
 */
export const searchOutboundData = async (
  filters: SearchFilters,
  pagination: PaginationParams,
): Promise<SearchResult> => {
  try {
    let sql = `
      SELECT 
        co.id,
        co.customer_id,
        co.division_id,
        co.spr_as_num,
        co.spr_asln_num,
        co.spr_assq_num,
        co.sph_ship_ymd,
        co.sph_dlv_cod,
        co.sph_dlv_nam1,
        co.spr_prod_code,
        co.spd_prod_nam,
        co.spr_rtpc_qty,
        co.prod_ppc_num,
        co.prod_hrc1,
        co.ai,
        co.inner_master,
        co.carton,
        co.pcs,
        co.inner_carton,
        co.inner_pcs,
        co.total_carton,
        co.total_pcs,
        co.total_ai,
        co.total_m3,
        co.created_at,
        co.updated_at,
        c.name as customer_name,
        cd.name as division_name
      FROM customer_outbound co
      LEFT JOIN customers c ON co.customer_id = c.id
      LEFT JOIN customer_divisions cd ON co.division_id = cd.id
      WHERE 1=1
    `
    const params: (string | number)[] = []

    // Add customer filter
    if (filters.customerId) {
      sql += ' AND co.customer_id = ?'
      params.push(filters.customerId)
    }

    // Add division filter
    if (filters.divisionId) {
      sql += ' AND co.division_id = ?'
      params.push(filters.divisionId)
    }

    // Add month filter
    if (filters.month) {
      sql += ` AND strftime('%Y-%m', co.sph_ship_ymd) = ?`
      params.push(filters.month)
    }

    // Add ordering
    sql += ' ORDER BY co.created_at DESC'

    // Add pagination
    const offset = (pagination.current - 1) * pagination.pageSize
    sql += ` LIMIT ? OFFSET ?`
    params.push(pagination.pageSize, offset)

    console.log('Search SQL:', sql)
    console.log('Search params:', params)

    const result = await executeQuery(sql, params)
    const rows = result?.result?.resultRows || []

    // Get total count
    const total = await getOutboundTotalCount(filters)

    // Map rows to objects
    const data: OutboundRecord[] = rows.map((row: unknown[]) => ({
      id: Number(row[0]),
      customer_id: Number(row[1]),
      division_id: row[2] ? Number(row[2]) : undefined,
      spr_as_num: String(row[3] || ''),
      spr_asln_num: Number(row[4] || 0),
      spr_assq_num: Number(row[5] || 0),
      sph_ship_ymd: String(row[6] || ''),
      sph_dlv_cod: Number(row[7] || 0),
      sph_dlv_nam1: String(row[8] || ''),
      spr_prod_code: Number(row[9] || 0),
      spd_prod_nam: String(row[10] || ''),
      spr_rtpc_qty: Number(row[11] || 0),
      prod_ppc_num: Number(row[12] || 0),
      prod_hrc1: String(row[13] || ''),
      ai: Number(row[14] || 0),
      inner_master: Number(row[15] || 0),
      carton: Number(row[16] || 0),
      pcs: Number(row[17] || 0),
      inner_carton: Number(row[18] || 0),
      inner_pcs: Number(row[19] || 0),
      total_carton: Number(row[20] || 0),
      total_pcs: Number(row[21] || 0),
      total_ai: Number(row[22] || 0),
      total_m3: Number(row[23] || 0),
      created_at: String(row[24] || ''),
      updated_at: String(row[25] || ''),
      customer_name: String(row[26] || ''),
      division_name: String(row[27] || ''),
    }))

    return { data, total }
  } catch (error) {
    console.error('Error searching outbound data:', error)
    throw new Error(
      'Failed to search outbound data: ' + (error instanceof Error ? error.message : String(error)),
    )
  }
}

/**
 * Gets the total count of outbound records matching the search filters
 *
 * This function provides the total record count for pagination purposes
 * without returning the actual data records.
 *
 * @param {SearchFilters} filters - Search filter criteria
 * @returns {Promise<number>} Total number of matching records
 * @throws {Error} When database query fails
 *
 * @example
 * ```typescript
 * const filters = { customerId: 'CUST001', divisionId: null, month: '2024-01' };
 * const totalCount = await getOutboundTotalCount(filters);
 * console.log(`Total matching records: ${totalCount}`);
 * ```
 *
 * @since 1.0.0
 */
export const getOutboundTotalCount = async (filters: SearchFilters): Promise<number> => {
  try {
    let countSql = `
      SELECT COUNT(*) as total
      FROM customer_outbound co
      LEFT JOIN customers c ON co.customer_id = c.id
      LEFT JOIN customer_divisions cd ON co.division_id = cd.id
      WHERE 1=1
    `
    const countParams: (string | number)[] = []

    if (filters.customerId) {
      countSql += ' AND co.customer_id = ?'
      countParams.push(filters.customerId)
    }

    if (filters.divisionId) {
      countSql += ' AND co.division_id = ?'
      countParams.push(filters.divisionId)
    }

    if (filters.month) {
      countSql += ` AND strftime('%Y-%m', co.sph_ship_ymd) = ?`
      countParams.push(filters.month)
    }

    const countResult = await executeQuery(countSql, countParams)
    return Number(countResult?.result?.resultRows?.[0]?.[0] || 0)
  } catch (error) {
    console.error('Error getting outbound total count:', error)
    throw new Error('Failed to get total count')
  }
}

// ============================================================================
// OUTBOUND RECORD MANAGEMENT
// ============================================================================

/**
 * Deletes an outbound record by its ID
 *
 * @param {number} recordId - The unique ID of the record to delete
 * @returns {Promise<void>} Resolves when record is successfully deleted
 * @throws {Error} When database operation fails or record not found
 *
 * @example
 * ```typescript
 * try {
 *   await deleteOutboundRecord(123);
 *   console.log('Record deleted successfully');
 * } catch (error) {
 *   console.error('Failed to delete record:', error);
 * }
 * ```
 *
 * @since 1.0.0
 */
export const deleteOutboundRecord = async (recordId: number): Promise<void> => {
  try {
    // Retrieve record information before deletion
    const recordQuery = `SELECT spr_prod_code, spr_rtpc_qty, customer_id FROM customer_outbound WHERE id = ?`
    const recordResult = await executeQuery(recordQuery, [recordId])

    if (!recordResult?.result?.resultRows || recordResult.result.resultRows.length === 0) {
      console.log(`No outbound record found with id ${recordId}`)
      throw new Error(`No outbound record found with id ${recordId}`)
    }

    const record = recordResult.result.resultRows[0]
    const productCode = String(record[0])
    const avrRtpcQty = Number(record[1]) || 0
    const customerId = String(record[2])

    if (!productCode || !customerId || avrRtpcQty <= 0) {
      console.log('Invalid record data. Cannot adjust stock.')
      throw new Error('Invalid record data. Cannot adjust stock.')
    }

    const successStock = await addStock(productCode, customerId, avrRtpcQty)
    if (successStock) {
      console.log('Stock updated successfully')
    }

    await executeQuery('DELETE FROM customer_outbound WHERE id = ?', [recordId])
  } catch (error) {
    console.error('Error deleting outbound record:', error)
    throw new Error('Failed to delete outbound record')
  }
}
