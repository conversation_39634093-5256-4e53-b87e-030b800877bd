import type { EntityStatus } from '../common'

// Define Category type (assuming it's not imported from a central types file for this example)
export interface Category {
  id: number
  code: string
  name: string
  workouts?: Workout[] // To store workouts for the expanded view
  status?: EntityStatus // Assuming categories might also have a status
}

export type WorkoutCalculationType = 'Manual' | 'Link' | 'Auto'

// --- Workout Types and Functions ---
export interface Workout {
  id: number
  code: string
  name: string
  unitId: number | null
  unitPrice: number | null
  unitPriceSign: '+' | '-'
  unitPriceUnit: 'VND' | 'PERCENT'
  unitPriceIsPercentage: boolean
  status: EntityStatus
  vat: number
  categoryId: number
  calculationType: WorkoutCalculationType
  linkedWorkoutId?: number | null // For 'Link' type
  autoFocusCode?: string | null // For 'Auto' type
  debitCodeId?: number | null // For Debit Code selection
  // Joined properties (read-only)
  unitCode?: string | null
  unitName?: string | null
  categoryName?: string | null
  linkedWorkoutName?: string | null
}

export type CategoryType = 'standard' | 'percentage_based' | 'automatic'
