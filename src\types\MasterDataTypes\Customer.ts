import type { EntityStatus } from '../common'

export type Customer = {
  id: number
  code: string
  name: string
  address: string
  taxNumber: string
  phone: string
  status: EntityStatus
  divisions?: CustomerDivision[]
}

export type CustomerDivision = {
  id: number
  code: string
  name: string
  description?: string
  customerId: number // Made non-optional as it's a foreign key
  status: EntityStatus
}

// In d:\02. Projects\06. NPE\Source\npe\src\types\MasterDataTypes\Customer.ts
export interface CustomerSettings {
  id: number
  customerId: number
  masterDataImportMapping: Record<string, string | undefined> // Or a more specific type for your mapping
  createdAt?: string // Or Date
  updatedAt?: string // Or Date
}
