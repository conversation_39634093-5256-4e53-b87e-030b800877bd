export interface Invoice {
  id: string
  customerId?: number
  customerDivisionId?: number
  totalAmount: number
  status: 'DRAFT' | 'PENDING' | 'APPROVED' | 'REJECTED' | 'PAID' | 'OVERDUE'
  notes: string
  createdAt: string
  updatedAt: string
  categories: InvoiceCategory[]
}

export interface InvoiceCharge {
  id: string
  invoiceId?: string
  categoryId?: number
  workoutId?: number
  unitId?: number
  unitPrice: number
  quantity: number
  totalAmount: number
  debitCodeId?: number
  vatRate: number
  vatAmount: number
  status?: string
  createdAt?: string
  updatedAt?: string
}

export interface InvoiceCategory {
  id: string
  invoiceId: string
  categoryId: number
  name: string
  order: number
  charges: InvoiceCharge[]
}
