/**
 * Get the number of days in the month
 * @param year
 * @param month
 * @returns
 */
export function getDaysInMonth(year, month) {
  return new Date(year, month, 0).getDate()
}

/**
 * Generate term milestones for a customer
 * @param paymentTerm
 * @param year
 * @param month
 * @returns
 */
export function getTermEnds(paymentTerm, year, month) {
  const daysInMonth = getDaysInMonth(year, month)
  const terms = []
  let day = paymentTerm
  while (day < daysInMonth) {
    terms.push(day)
    day += paymentTerm
  }
  terms.push(daysInMonth)
  return terms
}

/**
 * Get term label from Date
 * @param dateISO
 * @param payTerm
 * @returns
 */
export function getTermLabelFromDate(dateISO: string, payTerm: number): string {
  const [y, m, d] = dateISO.split('-').map(Number) // yyyy-mm-dd
  const lastDay = new Date(y, m, 0).getDate() // last day of the month
  const idx = Math.floor((d - 1) / payTerm) // 0-based
  const start = idx * payTerm + 1
  const end = Math.min(start + payTerm - 1, lastDay)
  return `${start}->${end}`
}

/**
 * Make payment term label
 * @param payTerm
 * @param y
 * @param m
 * @returns
 */
export function makeTermLabels(payTerm: number, y: number, m: number): string[] {
  const last = new Date(y, m, 0).getDate()
  const labels: string[] = []
  for (let s = 1; s <= last; s += payTerm) {
    const e = Math.min(s + payTerm - 1, last)
    labels.push(`${s}->${e}`)
  }
  return labels
}
