/**
 * Service: Import Outbound Service
 *
 * This service handles all database operations related to importing outbound invoice data,
 * including customer management, auto function calculations, column mapping, and data processing.
 *
 * Author: Generated for NPE Project
 * Created: 2024
 */

import { useSQLite } from '@IVC/hooks/useSQLite'

const { executeQuery, initialize } = useSQLite()

/**
 * ==============================================
 * Interface Definitions
 * ==============================================
 */
export interface Customer {
  id: string
  name: string
}

export interface Division {
  id: string
  name: string
  code: string
}

export interface AutoFunction {
  id: number
  code: string
  name: string
}

export interface ExcelRowData {
  [key: string]: string | number | boolean | null
  key: number
}

/**
 * ==============================================
 * Customer and Division Management
 * ==============================================
 */

/**
 * Loads all customers from the database
 *
 * @returns Promise<Customer[]> Array of customer objects with id and name
 * @throws Error if database query fails
 */
export const loadCustomers = async (): Promise<Customer[]> => {
  try {
    const result = await executeQuery('SELECT id, name FROM customers')
    return (
      result?.result?.resultRows?.map((row: unknown[]) => ({
        id: String(row[0]),
        name: String(row[1]),
      })) || []
    )
  } catch (error) {
    console.error('Error loading customers:', error)
    throw new Error('Failed to load customers')
  }
}

/**
 * Loads divisions for a specific customer
 *
 * @param customerId - The unique identifier of the customer
 * @returns Promise<Division[]> Array of division objects with id, code, and name
 * @throws Error if database query fails
 */
export const loadDivisions = async (customerId: string): Promise<Division[]> => {
  try {
    const result = await executeQuery(
      'SELECT id, code, name FROM customer_divisions WHERE customer_id = ? AND status = ?',
      [customerId, 'active'],
    )
    return (
      result?.result?.resultRows?.map((row: unknown[]) => ({
        id: String(row[0]),
        code: String(row[1]),
        name: String(row[2]),
      })) || []
    )
  } catch (error) {
    console.error('Error loading divisions:', error)
    throw new Error('Failed to load divisions')
  }
}

/**
 * ==============================================
 * COD Management and Auto Functions
 * ==============================================
 */

/**
 * Retrieves COD (Cash on Delivery) codes by specified type
 *
 * @param codeType - The type of COD code to retrieve (e.g., 'Store', 'Customer')
 * @returns Promise<number[]> Array of COD codes as numbers
 * @throws Error if database query fails
 *
 * @example
 * ```typescript
 * const storeCodes = await getCODCodesByType('Store');
 * console.log(storeCodes); // [101, 102, 103]
 * ```
 */
export const getCODCodesByType = async (codeType: string): Promise<number[]> => {
  try {
    const result = await executeQuery('SELECT code FROM cod WHERE code_type = ?', [codeType])
    return result?.result?.resultRows?.map((row: unknown[]) => Number(row[0])) || []
  } catch (error) {
    console.error(`Error fetching COD codes for type ${codeType}:`, error)
    throw error
  }
}

export const getCODCodes = async (): Promise<number[]> => {
  try {
    const result = await executeQuery('SELECT code FROM cod ')
    return result?.result?.resultRows?.map((row: unknown[]) => Number(row[0])) || []
  } catch (error) {
    console.error(`Error fetching COD codes:`, error)
    throw error
  }
}

/**
 * Retrieves auto function codes and names for a specific customer
 *
 * @param customerId - The unique identifier of the customer
 * @returns Promise<AutoFunction[]> Array of auto function objects containing id, code, and name
 * @throws Error if database query fails
 *
 * @example
 * ```typescript
 * const autoFunctions = await getAutoFunctions('customer123');
 * console.log(autoFunctions); // [{ id: 1, code: 'N_A_OUT_SUM_CARTON', name: 'Sum Total Carton' }]
 * ```
 */
export const getAutoFunctions = async (customerId: string): Promise<AutoFunction[]> => {
  try {
    const autoFunctionsQuery = `
      SELECT af.id, af.code, af.name
      FROM auto_function af
      INNER JOIN auto_function_customers afc ON af.id = afc.auto_function_id
      WHERE afc.customer_id = ?
    `

    const autoFunctionsResult = await executeQuery(autoFunctionsQuery, [customerId])

    return (
      autoFunctionsResult?.result?.resultRows?.map((row: unknown[]) => ({
        id: Number(row[0]),
        code: String(row[1]),
        name: String(row[2]),
      })) || []
    )
  } catch (error) {
    console.error('Error fetching auto functions:', error)
    throw error
  }
}

/**
 * Calculates the sum of total cartons for a specific customer and time period
 *
 * @param customerId - The unique identifier of the customer
 * @param divisionId - The division identifier (optional, can be null)
 * @param yearMonth - The year-month string in format 'YYYY-MM'
 * @returns Promise<number> The total sum of cartons
 * @throws Error if database query fails
 *
 * @example
 * ```typescript
 * const totalCartons = await calculateSumTotalCarton('customer123', 'div456', '2024-01');
 * console.log(totalCartons); // 1500
 * ```
 */
export const calculateSumTotalCarton = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    let query = `
      SELECT COALESCE(SUM(total_carton), 0) as sum_total_carton
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
    `

    const params = [customerId, yearMonth]

    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_carton query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalCarton = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_carton result: ${sumTotalCarton}`)
      return sumTotalCarton
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_carton:', error)
    throw error
  }
}

/**
 * Calculates the sum of total pieces for a specific customer and time period
 *
 * @param customerId - The unique identifier of the customer
 * @param divisionId - The division identifier (optional, can be null)
 * @param yearMonth - The year-month string in format 'YYYY-MM'
 * @returns Promise<number> The total sum of pieces
 * @throws Error if database query fails
 *
 * @example
 * ```typescript
 * const totalPcs = await calculateSumTotalPcs('customer123', 'div456', '2024-01');
 * console.log(totalPcs); // 25000
 * ```
 */
export const calculateSumTotalPcs = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    let query = `
      SELECT COALESCE(SUM(total_pcs), 0) as sum_total_pcs
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
    `

    const params = [customerId, yearMonth]

    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_pcs query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalPcs = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_pcs result: ${sumTotalPcs}`)
      return sumTotalPcs
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_pcs:', error)
    throw error
  }
}

/**
 * Calculates the sum of total cubic meters for a specific customer and time period
 *
 * @param customerId - The unique identifier of the customer
 * @param divisionId - The division identifier (optional, can be null)
 * @param yearMonth - The year-month string in format 'YYYY-MM'
 * @returns Promise<number> The total sum of cubic meters
 * @throws Error if database query fails
 *
 * @example
 * ```typescript
 * const totalM3 = await calculateSumTotalM3('customer123', 'div456', '2024-01');
 * console.log(totalM3); // 125.75
 * ```
 */
export const calculateSumTotalM3 = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    let query = `
      SELECT COALESCE(SUM(total_m3), 0) as sum_total_m3
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
    `

    const params = [customerId, yearMonth]

    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_m3 query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalM3 = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_m3 result: ${sumTotalM3}`)
      return sumTotalM3
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_m3:', error)
    throw error
  }
}

/**
 * Calculates the sum of total cartons for store delivery type
 *
 * @param customerId - The unique identifier of the customer
 * @param divisionId - The division identifier (optional, can be null)
 * @param yearMonth - The year-month string in format 'YYYY-MM'
 * @returns Promise<number> The total sum of cartons for store delivery
 * @throws Error if database query fails or no store COD codes found
 *
 * @example
 * ```typescript
 * const storeCartons = await calculateSumTotalCartonStore('customer123', 'div456', '2024-01');
 * console.log(storeCartons); // 800
 * ```
 */
export const calculateSumTotalCartonStore = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    const storeCodes = await getCODCodesByType('Store')

    if (storeCodes.length === 0) {
      console.log('No COD Store codes found, returning 0')
      return 0
    }

    const placeholders = storeCodes.map(() => '?').join(', ')
    let query = `
      SELECT COALESCE(SUM(total_carton), 0) as sum_total_carton_store
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
      AND sph_dlv_cod IN (${placeholders})
    `

    const params = [customerId, yearMonth, ...storeCodes]

    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_carton store query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalCartonStore = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_carton store result: ${sumTotalCartonStore}`)
      return sumTotalCartonStore
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_carton store:', error)
    throw error
  }
}

/**
 * Calculates the sum of total pieces for store delivery type
 *
 * @param customerId - The unique identifier of the customer
 * @param divisionId - The division identifier (optional, can be null)
 * @param yearMonth - The year-month string in format 'YYYY-MM'
 * @returns Promise<number> The total sum of pieces for store delivery
 * @throws Error if database query fails or no store COD codes found
 *
 * @example
 * ```typescript
 * const storePcs = await calculateSumTotalPcsStore('customer123', 'div456', '2024-01');
 * console.log(storePcs); // 12000
 * ```
 */
export const calculateSumTotalPcsStore = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    const storeCodes = await getCODCodesByType('Store')

    if (storeCodes.length === 0) {
      console.log('No COD Store codes found, returning 0')
      return 0
    }

    const placeholders = storeCodes.map(() => '?').join(', ')
    let query = `
      SELECT COALESCE(SUM(total_pcs), 0) as sum_total_pcs_store
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
      AND sph_dlv_cod IN (${placeholders})
    `

    const params = [customerId, yearMonth, ...storeCodes]

    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_pcs store query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalPcsStore = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_pcs store result: ${sumTotalPcsStore}`)
      return sumTotalPcsStore
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_pcs store:', error)
    throw error
  }
}

/**
 * Calculates the sum of total cubic meters for store delivery type
 *
 * @param customerId - The unique identifier of the customer
 * @param divisionId - The division identifier (optional, can be null)
 * @param yearMonth - The year-month string in format 'YYYY-MM'
 * @returns Promise<number> The total sum of cubic meters for store delivery
 * @throws Error if database query fails or no store COD codes found
 *
 * @example
 * ```typescript
 * const storeM3 = await calculateSumTotalM3Store('customer123', 'div456', '2024-01');
 * console.log(storeM3); // 85.5
 * ```
 */
export const calculateSumTotalM3Store = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    const storeCodes = await getCODCodesByType('Store')

    if (storeCodes.length === 0) {
      console.log('No COD Store codes found, returning 0')
      return 0
    }

    const placeholders = storeCodes.map(() => '?').join(', ')
    let query = `
      SELECT COALESCE(SUM(total_m3), 0) as sum_total_m3_store
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
      AND sph_dlv_cod IN (${placeholders})
    `

    const params = [customerId, yearMonth, ...storeCodes]

    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_m3 store query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalM3Store = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_m3 store result: ${sumTotalM3Store}`)
      return sumTotalM3Store
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_m3 store:', error)
    throw error
  }
}

/**
 * Calculates the sum of total cartons for customer delivery type
 *
 * @param customerId - The unique identifier of the customer
 * @param divisionId - The division identifier (optional, can be null)
 * @param yearMonth - The year-month string in format 'YYYY-MM'
 * @returns Promise<number> The total sum of cartons for customer delivery
 * @throws Error if database query fails or no customer COD codes found
 *
 * @example
 * ```typescript
 * const customerCartons = await calculateSumTotalCartonCustomer('customer123', 'div456', '2024-01');
 * console.log(customerCartons); // 700
 * ```
 */
export const calculateSumTotalCartonCustomer = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    const customerCodes = await getCODCodesByType('Customer')

    if (customerCodes.length === 0) {
      console.log('No COD Customer codes found, returning 0')
      return 0
    }

    const placeholders = customerCodes.map(() => '?').join(', ')
    let query = `
      SELECT COALESCE(SUM(total_carton), 0) as sum_total_carton_customer
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
      AND sph_dlv_cod IN (${placeholders})
    `

    const params = [customerId, yearMonth, ...customerCodes]

    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_carton customer query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalCartonCustomer = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_carton customer result: ${sumTotalCartonCustomer}`)
      return sumTotalCartonCustomer
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_carton customer:', error)
    throw error
  }
}

/**
 * Calculates the sum of total pieces for customer delivery type
 *
 * @param customerId - The unique identifier of the customer
 * @param divisionId - The division identifier (optional, can be null)
 * @param yearMonth - The year-month string in format 'YYYY-MM'
 * @returns Promise<number> The total sum of pieces for customer delivery
 * @throws Error if database query fails or no customer COD codes found
 *
 * @example
 * ```typescript
 * const customerPcs = await calculateSumTotalPcsCustomer('customer123', 'div456', '2024-01');
 * console.log(customerPcs); // 13000
 * ```
 */
export const calculateSumTotalPcsCustomer = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    const customerCodes = await getCODCodesByType('Customer')

    if (customerCodes.length === 0) {
      console.log('No COD Customer codes found, returning 0')
      return 0
    }

    const placeholders = customerCodes.map(() => '?').join(', ')
    let query = `
      SELECT COALESCE(SUM(total_pcs), 0) as sum_total_pcs_customer
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
      AND sph_dlv_cod IN (${placeholders})
    `

    const params = [customerId, yearMonth, ...customerCodes]

    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_pcs customer query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalPcsCustomer = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_pcs customer result: ${sumTotalPcsCustomer}`)
      return sumTotalPcsCustomer
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_pcs customer:', error)
    throw error
  }
}

/**
 * Calculates the sum of total cubic meters for customer delivery type
 *
 * @param customerId - The unique identifier of the customer
 * @param divisionId - The division identifier (optional, can be null)
 * @param yearMonth - The year-month string in format 'YYYY-MM'
 * @returns Promise<number> The total sum of cubic meters for customer delivery
 * @throws Error if database query fails or no customer COD codes found
 *
 * @example
 * ```typescript
 * const customerM3 = await calculateSumTotalM3Customer('customer123', 'div456', '2024-01');
 * console.log(customerM3); // 40.25
 * ```
 */
export const calculateSumTotalM3Customer = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    const customerCodes = await getCODCodesByType('Customer')

    if (customerCodes.length === 0) {
      console.log('No COD Customer codes found, returning 0')
      return 0
    }

    const placeholders = customerCodes.map(() => '?').join(', ')
    let query = `
      SELECT COALESCE(SUM(total_m3), 0) as sum_total_m3_customer
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
      AND sph_dlv_cod IN (${placeholders})
    `

    const params = [customerId, yearMonth, ...customerCodes]

    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_m3 customer query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalM3Customer = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_m3 customer result: ${sumTotalM3Customer}`)
      return sumTotalM3Customer
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_m3 customer:', error)
    throw error
  }
}

/**
 * Calculates the sum of SPR AS numbers for customer delivery type
 *
 * @param customerId - The unique identifier of the customer
 * @param divisionId - The division identifier (optional, can be null)
 * @param yearMonth - The year-month string in format 'YYYY-MM'
 * @returns Promise<number> The total sum of SPR AS numbers for customer delivery
 * @throws Error if database query fails or no customer COD codes found
 *
 * @example
 * ```typescript
 * const labelCount = await calculateSumTotalLabelCustomer('customer123', 'div456', '2024-01');
 * console.log(labelCount); // 450
 * ```
 */
export const calculateSumTotalLabelCustomer = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    const customerCodes = await getCODCodesByType('Customer')

    if (customerCodes.length === 0) {
      console.log('No COD Customer codes found, returning 0')
      return 0
    }

    const placeholders = customerCodes.map(() => '?').join(', ')
    let query = `
      SELECT COALESCE(SUM(spr_as_num), 0) as sum_spr_as_num_customer
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
      AND sph_dlv_cod IN (${placeholders})
    `

    const params = [customerId, yearMonth, ...customerCodes]

    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum spr_as_num customer query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumSprAsNumCustomer = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum spr_as_num customer result: ${sumSprAsNumCustomer}`)
      return sumSprAsNumCustomer
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum spr_as_num customer:', error)
    throw error
  }
}

/**
 * ==============================================
 * Auto Function Result Management
 * ==============================================
 */

/**
 * Saves or updates auto function calculation results in the database
 *
 * @param autoFunctionId - The unique identifier of the auto function
 * @param customerId - The unique identifier of the customer
 * @param divisionId - The division identifier (optional, can be null)
 * @param yearMonth - The year-month string in format 'YYYY-MM'
 * @param value - The calculated value to save
 * @returns Promise<void> Resolves when the operation completes successfully
 * @throws Error if database operation fails
 *
 * @example
 * ```typescript
 * await saveAutoFunctionResult(1, 'customer123', 'div456', '2024-01', 1500);
 * console.log('Auto function result saved successfully');
 * ```
 */
export const saveAutoFunctionResult = async (
  autoFunctionId: number,
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
  value: number,
): Promise<void> => {
  try {
    // Check if record already exists
    let checkQuery = `
      SELECT id FROM auto_function_customer_detail
      WHERE auto_function_id = ? AND customer_id = ? AND year_month = ?
    `

    const checkParams = [autoFunctionId, customerId, yearMonth]

    // Add division filter to check query
    if (divisionId) {
      checkQuery += ' AND division_id = ?'
      checkParams.push(divisionId)
    } else {
      checkQuery += ' AND division_id IS NULL'
    }

    const existingRecord = await executeQuery(checkQuery, checkParams)
    const existingRecords = existingRecord?.result?.resultRows || []

    if (existingRecords.length > 0) {
      // Update existing record
      let updateQuery = `
        UPDATE auto_function_customer_detail
        SET value = ?, updated_at = CURRENT_TIMESTAMP
        WHERE auto_function_id = ? AND customer_id = ? AND year_month = ?
      `

      const updateParams = [value, autoFunctionId, customerId, yearMonth]

      if (divisionId) {
        updateQuery += ' AND division_id = ?'
        updateParams.push(divisionId)
      } else {
        updateQuery += ' AND division_id IS NULL'
      }

      await executeQuery(updateQuery, updateParams)
      console.log('Updated existing auto function detail record')
    } else {
      // Insert new record
      const insertQuery = `
        INSERT INTO auto_function_customer_detail (
          auto_function_id, customer_id, division_id, year_month, value, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `

      const insertParams = [autoFunctionId, customerId, divisionId, yearMonth, value]

      await executeQuery(insertQuery, insertParams)
      console.log('Inserted new auto function detail record')
    }
  } catch (error) {
    console.error('Error saving auto function result:', error)
    throw error
  }
}

/**
 * Calculates the sum of total cartons for other delivery types (not Store or Customer)
 *
 * @param customerId - The unique identifier of the customer
 * @param divisionId - The division identifier (optional, can be null)
 * @param yearMonth - The year-month string in format 'YYYY-MM'
 * @returns Promise<number> The total sum of cartons for other delivery types
 * @throws Error if database query fails or no COD codes found
 *
 * @example
 * ```typescript
 * const otherCartons = await calculateSumTotalCartonOther('customer123', 'div456', '2024-01');
 * console.log(otherCartons); // 150
 * ```
 */
export const calculateSumTotalCartonOther = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    const CODCodes = await getCODCodes()

    if (CODCodes.length === 0) {
      console.log('No COD Customer codes found, returning 0')
      return 0
    }

    const placeholders = CODCodes.map(() => '?').join(', ')
    let query = `
      SELECT COALESCE(SUM(total_carton), 0) as sum_total_carton_other
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
      AND (sph_dlv_cod Not IN (${placeholders}) or sph_dlv_cod is null )
    `

    const params = [customerId, yearMonth, ...CODCodes]

    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_carton other query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalCartonOther = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_carton other result: ${sumTotalCartonOther}`)
      return sumTotalCartonOther
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_carton other:', error)
    throw error
  }
}

/**
 * Calculates the sum of total pieces for other delivery types (not Store or Customer)
 *
 * @param customerId - The unique identifier of the customer
 * @param divisionId - The division identifier (optional, can be null)
 * @param yearMonth - The year-month string in format 'YYYY-MM'
 * @returns Promise<number> The total sum of pieces for other delivery types
 * @throws Error if database query fails or no COD codes found
 *
 * @example
 * ```typescript
 * const otherPcs = await calculateSumTotalPcsOther('customer123', 'div456', '2024-01');
 * console.log(otherPcs); // 3500
 * ```
 */
export const calculateSumTotalPcsOther = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    const CODCodes = await getCODCodes()

    if (CODCodes.length === 0) {
      console.log('No COD Customer codes found, returning 0')
      return 0
    }

    const placeholders = CODCodes.map(() => '?').join(', ')
    let query = `
      SELECT COALESCE(SUM(total_pcs), 0) as sum_total_pcs_other
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
      AND (sph_dlv_cod Not IN (${placeholders}) or sph_dlv_cod is null )
    `

    const params = [customerId, yearMonth, ...CODCodes]

    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_pcs other query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalPcsOther = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_pcs other result: ${sumTotalPcsOther}`)
      return sumTotalPcsOther
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_pcs other:', error)
    throw error
  }
}

/**
 * Calculates the sum of total cubic meters for other delivery types (not Store or Customer)
 *
 * @param customerId - The unique identifier of the customer
 * @param divisionId - The division identifier (optional, can be null)
 * @param yearMonth - The year-month string in format 'YYYY-MM'
 * @returns Promise<number> The total sum of cubic meters for other delivery types
 * @throws Error if database query fails or no COD codes found
 *
 * @example
 * ```typescript
 * const otherM3 = await calculateSumTotalM3Other('customer123', 'div456', '2024-01');
 * console.log(otherM3); // 18.75
 * ```
 */
export const calculateSumTotalM3Other = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<number> => {
  try {
    const CODCodes = await getCODCodes()

    if (CODCodes.length === 0) {
      console.log('No COD Customer codes found, returning 0')
      return 0
    }

    const placeholders = CODCodes.map(() => '?').join(', ')
    let query = `
      SELECT COALESCE(SUM(total_m3), 0) as sum_total_m3_other
      FROM customer_outbound
      WHERE customer_id = ?
      AND strftime('%Y-%m', sph_ship_ymd) = ?
      AND (sph_dlv_cod Not IN (${placeholders}) or sph_dlv_cod is null )
    `

    const params = [customerId, yearMonth, ...CODCodes]

    if (divisionId) {
      query += ' AND division_id = ?'
      params.push(divisionId)
    }

    console.log('Executing sum total_m3 other query:', query, 'with params:', params)

    const result = await executeQuery(query, params)

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      const sumTotalM3Other = Number(result.result.resultRows[0][0]) || 0
      console.log(`Sum total_m3 other result: ${sumTotalM3Other}`)
      return sumTotalM3Other
    }

    return 0
  } catch (error) {
    console.error('Error calculating sum total_m3 other:', error)
    throw error
  }
}

/**
 * Processes and calculates all auto functions for a specific customer and time period
 *
 * This function retrieves all auto functions configured for a customer and calculates
 * their values based on the function code, then saves the results to the database.
 *
 * @param customerId - The unique identifier of the customer
 * @param divisionId - The division identifier (optional, can be null)
 * @param yearMonth - The year-month string in format 'YYYY-MM'
 * @returns Promise<boolean> True if all calculations completed successfully, false if validation fails
 * @throws Error if database operations fail during calculation or saving
 *
 * @example
 * ```typescript
 * const success = await calculateAutoFunction('customer123', 'div456', '2024-01');
 * if (success) {
 *   console.log('All auto functions calculated successfully');
 * }
 * ```
 */
export const calculateAutoFunction = async (
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<boolean> => {
  try {
    console.log('Starting auto function calculation...')

    // Check if we have selected customer
    if (!customerId) {
      console.warn('No customer selected for auto function calculation')
      return false
    }

    // Check if we have selected month
    if (!yearMonth) {
      console.warn('No month selected for auto function calculation')
      return false
    }

    console.log(
      `Calculating auto functions for customer: ${customerId}, division: ${divisionId}, month: ${yearMonth}`,
    )

    // Get auto functions for this customer
    const autoFunctions = await getAutoFunctions(customerId)

    if (autoFunctions.length === 0) {
      console.log('No auto functions found for this customer')
      return true // Not an error, just no auto functions to calculate
    }

    console.log(`Found ${autoFunctions.length} auto functions to calculate`)

    // Process each auto function
    for (const autoFunction of autoFunctions) {
      const { id: autoFunctionId, code: functionCode, name: functionName } = autoFunction

      console.log(`Processing auto function: ${functionCode} (${functionName})`)

      let calculatedValue = 0

      // Process based on auto function code
      switch (functionCode) {
        case 'N_A_OUT_SUM_CARTON':
          calculatedValue = await calculateSumTotalCarton(customerId, divisionId, yearMonth)
          break

        case 'N_A_OUT_SUM_PCS':
          calculatedValue = await calculateSumTotalPcs(customerId, divisionId, yearMonth)
          break

        case 'N_A_OUT_SUM_M3':
          calculatedValue = await calculateSumTotalM3(customerId, divisionId, yearMonth)
          break

        case 'N_A_OUT_STORE_SUM_CARTON':
          calculatedValue = await calculateSumTotalCartonStore(customerId, divisionId, yearMonth)
          break

        case 'N_A_OUT_STORE_SUM_PCS':
          calculatedValue = await calculateSumTotalPcsStore(customerId, divisionId, yearMonth)
          break

        case 'N_A_OUT_STORE_SUM_M3':
          calculatedValue = await calculateSumTotalM3Store(customerId, divisionId, yearMonth)
          break

        case 'N_A_OUT_CUSTOMER_SUM_CARTON':
          calculatedValue = await calculateSumTotalCartonCustomer(customerId, divisionId, yearMonth)
          break

        case 'N_A_OUT_CUSTOMER_SUM_PCS':
          calculatedValue = await calculateSumTotalPcsCustomer(customerId, divisionId, yearMonth)
          break

        case 'N_A_OUT_CUSTOMER_SUM_M3':
          calculatedValue = await calculateSumTotalM3Customer(customerId, divisionId, yearMonth)
          break

        case 'N_A_OUT_CUSTOMER_SUM_LABEL':
          calculatedValue = await calculateSumTotalLabelCustomer(customerId, divisionId, yearMonth)
          break

        case 'N_A_OUT_NONE_SUM_CARTON':
          calculatedValue = await calculateSumTotalCartonOther(customerId, divisionId, yearMonth)
          break

        case 'N_A_OUT_NONE_SUM_PCS':
          calculatedValue = await calculateSumTotalPcsOther(customerId, divisionId, yearMonth)
          break

        case 'N_A_OUT_NONE_SUM_M3':
          calculatedValue = await calculateSumTotalM3Other(customerId, divisionId, yearMonth)
          break

        default:
          console.warn(`Unknown auto function code: ${functionCode}`)
          continue
      }

      console.log(`Auto function ${functionCode} calculated value: ${calculatedValue}`)

      // Save the calculated value
      await saveAutoFunctionResult(
        autoFunctionId,
        customerId,
        divisionId,
        yearMonth,
        calculatedValue,
      )

      console.log(`Auto function ${functionCode} saved successfully`)
    }

    console.log('Auto function calculation completed successfully')
    return true
  } catch (error) {
    console.error('Error in auto function calculation:', error)
    throw error
  }
}

/**
 * ==============================================
 * Stock Management
 * ==============================================
 */

/**
 * Deducts stock quantity for outbound operations
 *
 * This function validates and deducts stock quantities from the stock table for outbound operations.
 * It performs stock availability checks and updates the stock records accordingly.
 *
 * @param productCode - The product code to look up in stock
 * @param customerId - The unique identifier of the customer
 * @param avrRtpcQty - The quantity to deduct from stock
 * @returns Promise<boolean> True if stock deduction is successful
 * @throws Error if insufficient stock, no stock record found, or database operation fails
 *
 * @example
 * ```typescript
 * try {
 *   const success = await minusStock('PROD001', 'customer123', 50);
 *   console.log('Stock deducted successfully');
 * } catch (error) {
 *   console.error('Stock deduction failed:', error.message);
 * }
 * ```
 */
export const minusStock = async (
  productCode: string,
  customerId: string,
  avrRtpcQty: number,
): Promise<boolean> => {
  try {
    console.log(
      `Starting stock deduction for product: ${productCode}, customer: ${customerId}, quantity: ${avrRtpcQty}`,
    )

    // Step 2: Get stock information with conditions
    const stockQuery = `
      SELECT id, avr_rtpc_qty_temp
      FROM stock
      WHERE product_code = ?
      AND customer_id = ?
      AND is_active = 1
    `

    const stockResult = await executeQuery(stockQuery, [productCode, customerId])

    // Step 4: Check if stock record exists
    if (!stockResult?.result?.resultRows || stockResult.result.resultRows.length === 0) {
      const errorMessage = `No active stock record found for product: ${productCode}, customer: ${customerId}`
      console.error(errorMessage)
      throw new Error(errorMessage)
    }

    const stockRecord = stockResult.result.resultRows[0]
    const stockId = stockRecord[0] as number
    const currentStockQty = Number(stockRecord[1]) || 0

    console.log(`Found stock record - ID: ${stockId}, Current quantity: ${currentStockQty}`)

    // Step 3.1: Check if sufficient stock is available
    if (currentStockQty < avrRtpcQty) {
      const errorMessage = `Insufficient stock. Available: ${currentStockQty}, Required: ${avrRtpcQty} for product: ${productCode}`
      console.error(errorMessage)
      throw new Error(errorMessage)
    }

    // Step 3.2: Update stock quantity and operation source
    const newStockQty = currentStockQty - avrRtpcQty
    const updateQuery = `
      UPDATE stock
      SET avr_rtpc_qty_temp = ?,
          operation_source = 'Outbound',
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `

    await executeQuery(updateQuery, [newStockQty, stockId])

    console.log(
      `Stock deducted successfully. New quantity: ${newStockQty} for product: ${productCode}`,
    )
    return true
  } catch (error) {
    console.error('Error in stock deduction:', error)
    throw error
  }
}

/**
 * ==============================================
 * Column Mapping and Database Operations
 * ==============================================
 */

/**
 * Saves column mapping configuration for Excel import
 *
 * This function stores the mapping between Excel column names and database column names
 * for a specific customer, allowing customized import configurations.
 *
 * @param customerId - The unique identifier of the customer
 * @param columnMapping - Object mapping Excel column names to database column names
 * @returns Promise<void> Resolves when mapping is saved successfully
 * @throws Error if database operation fails
 *
 * @example
 * ```typescript
 * const mapping = {
 *   'Ship Date': 'sph_ship_ymd',
 *   'Quantity': 'spr_rtpc_qty'
 * };
 * await saveColumnMapping('customer123', mapping);
 * ```
 */
export const saveColumnMapping = async (
  customerId: string,
  columnMapping: { [key: string]: string },
): Promise<void> => {
  try {
    await initialize()

    // Delete existing mappings for this customer and import type
    await executeQuery(
      `DELETE FROM customer_import_mapping WHERE customer_id = ? AND import_type = 'outbound'`,
      [customerId],
    )

    // Insert new mappings
    const insertPromises = Object.entries(columnMapping).map(([excelColumn, dbColumn]) => {
      return executeQuery(
        `INSERT INTO customer_import_mapping (customer_id, import_type, excel_column, db_column) VALUES (?, 'outbound', ?, ?)`,
        [customerId, excelColumn, dbColumn],
      )
    })

    await Promise.all(insertPromises)
    console.log('Column mapping saved successfully for outbound')
  } catch (error) {
    console.error('Error saving column mapping:', error)
    throw error
  }
}

/**
 * Loads previously saved column mapping configuration for a customer
 *
 * @param customerId - The unique identifier of the customer
 * @returns Promise<{ [key: string]: string }> Object containing Excel to database column mappings
 * @throws Error if database operation fails
 *
 * @example
 * ```typescript
 * const savedMappings = await loadSavedMappings('customer123');
 * console.log(savedMappings); // { 'Ship Date': 'sph_ship_ymd', 'Quantity': 'spr_rtpc_qty' }
 * ```
 */
export const loadSavedMappings = async (customerId: string): Promise<{ [key: string]: string }> => {
  try {
    await initialize()

    const result = await executeQuery(
      `SELECT excel_column, db_column FROM customer_import_mapping WHERE customer_id = ? AND import_type = 'outbound'`,
      [customerId],
    )

    const savedMappings: { [key: string]: string } = {}

    if (result?.result?.resultRows && result.result.resultRows.length > 0) {
      result.result.resultRows.forEach((row: unknown[]) => {
        const excelColumn = row[0] as string
        const dbColumn = row[1] as string
        savedMappings[excelColumn] = dbColumn
      })
    }

    return savedMappings
  } catch (error) {
    console.error('Error loading saved mappings:', error)
    throw error
  }
}

/**
 * ==============================================
 * Data Import Processing
 * ==============================================
 */

/**
 * Initializes the database connection and ensures it's ready for operations
 *
 * @returns Promise<void> Resolves when database is successfully initialized
 * @throws Error if database initialization fails
 *
 * @example
 * ```typescript
 * await initializeDatabase();
 * console.log('Database ready for operations');
 * ```
 */
export const initializeDatabase = async (): Promise<void> => {
  try {
    await initialize()
    console.log('Database initialized successfully for outbound import')
  } catch (error) {
    console.error('Error initializing database:', error)
    throw new Error('Failed to initialize database')
  }
}

/**
 * Processes complete data import from Excel to database
 *
 * This function handles the entire import process including data mapping, validation,
 * and batch insertion of Excel data into the customer_outbound table.
 *
 * @param excelData - Array of Excel row data objects
 * @param columnMapping - Mapping between Excel columns and database columns
 * @param customerId - The unique identifier of the customer
 * @param divisionId - The division identifier (optional, can be null)
 * @param yearMonth - The year-month string in format 'YYYY-MM'
 * @returns Promise<{successCount: number, errorCount: number, errors: string[]}> Import results summary
 * @throws Error if the import process fails
 *
 * @example
 * ```typescript
 * const result = await processDataImport(
 *   excelData,
 *   { 'Ship Date': 'sph_ship_ymd', 'Quantity': 'spr_rtpc_qty' },
 *   'customer123',
 *   'div456',
 *   '2024-01'
 * );
 * console.log(`Import completed: ${result.successCount} success, ${result.errorCount} errors`);
 * ```
 */
export const processDataImport = async (
  excelData: ExcelRowData[],
  columnMapping: { [key: string]: string },
  customerId: string,
  divisionId: string | null,
  yearMonth: string,
): Promise<{ successCount: number; errorCount: number; errors: string[] }> => {
  try {
    console.log('Starting outbound data import process...')

    // Create mapping from Excel columns to DB columns
    const excelToDbMapping = columnMapping
    const mappedDbColumns = Object.values(excelToDbMapping)

    // Build the INSERT SQL dynamically based on mapped columns
    const baseColumns = ['customer_id', 'division_id']
    const baseColumn2s = [
      'prod_ppc_num',
      'prod_hrc1',
      'ai',
      'inner_master',
      'carton',
      'pcs',
      'inner_carton',
      'inner_pcs',
      'total_carton',
      'total_pcs',
      'total_ai',
      'total_m3',
    ]
    const allColumns = [...baseColumns, ...mappedDbColumns, ...baseColumn2s]
    const placeholders = allColumns.map(() => '?').join(', ')

    const insertSQL = `
      INSERT INTO customer_outbound (${allColumns.join(', ')})
      VALUES (${placeholders})
    `

    console.log('Insert SQL:', insertSQL)

    let successCount = 0
    let errorCount = 0
    const errors: string[] = []

    for (let i = 0; i < excelData.length; i++) {
      try {
        const row = excelData[i]
        const values: (string | number | boolean | null)[] = []

        // Add customer_id first
        values.push(customerId)

        // Add division_id (optional - can be null)
        values.push(divisionId)

        // Add mapped column values
        for (const dbColumn of mappedDbColumns) {
          // Find the excel column that maps to this db column
          const excelColumn = Object.keys(excelToDbMapping).find(
            (excelCol) => excelToDbMapping[excelCol] === dbColumn,
          )

          if (excelColumn && row[excelColumn] !== undefined) {
            let value = row[excelColumn]

            // Handle date formatting for date columns
            if (dbColumn === 'sph_ship_ymd' && typeof value === 'string') {
              // Try to parse and format the date
              value = formatDate(value)
            }

            // Handle numeric conversions
            if (typeof value === 'string' && value.trim() === '') {
              value = null // Convert empty strings to null
            } else if (typeof value === 'number' || !isNaN(Number(value))) {
              // Keep numeric values as numbers if they're numeric columns
              const numericColumns = ['spr_rtpc_qty']
              if (numericColumns.includes(dbColumn)) {
                value = Number(value) || 0
              }
            } else if (typeof value === 'boolean') {
              // Convert boolean to string or number as needed
              value = value ? 1 : 0
            }

            values.push(value)
          } else {
            // Handle unmapped or missing columns
            values.push(null)
          }
        }

        // Get extended product data if product code exists
        let prodPpcNum: string | number | null = null
        let prodHrc1: string | number | null = null
        let prodPcm3: string | number | null = null
        let aiValue: string | number = '-'
        let innerMaster = 0

        // Get the product code column mapping
        const productCodeColumn = Object.keys(columnMapping).find(
          (key) => columnMapping[key] === 'spr_prod_cod',
        )

        if (productCodeColumn && row[productCodeColumn]) {
          const productCode = row[productCodeColumn]
          const productData = await getProductDataExtended(String(productCode))
          prodPpcNum = productData.prodPpcNum
          prodHrc1 = productData.prodHrc1
          prodPcm3 = productData.prodPcm3
          aiValue = productData.aiValue
          innerMaster = productData.innerMaster
        }

        // Calculate additional columns with error handling
        let carton: number = 0
        let pcs: number = 0
        let innerCarton: number = 0
        let innerPcs: number = 0
        let totalCarton: number = 0
        let totalPcs: number = 0
        let totalAi: number = 0
        let totalM3: number = 0

        try {
          // Get avr_rtpc_qty value for calculations
          const avrRtpcQtyColumn = Object.keys(columnMapping).find(
            (key) => columnMapping[key] === 'avr_rtpc_qty',
          )
          const avrRtpcQty =
            avrRtpcQtyColumn && row[avrRtpcQtyColumn] ? Number(row[avrRtpcQtyColumn]) : 0

          // Basic calculations: carton = avr_rtpc_qty/prod_ppc_num, pcs = avr_rtpc_qty - (carton * prod_ppc_num)
          if (prodPpcNum && Number(prodPpcNum) > 0 && avrRtpcQty > 0) {
            carton = Math.floor(avrRtpcQty / Number(prodPpcNum))
            pcs = avrRtpcQty - carton * Number(prodPpcNum)
          }

          // Conditional calculations based on prod_hrc1 containing "INNER"
          if (prodHrc1 && String(prodHrc1).toUpperCase().includes('INNER')) {
            if (innerMaster > 0 && carton > 0) {
              innerCarton = Math.floor(carton / innerMaster)
              innerPcs = carton - innerCarton * innerMaster
            }
            totalCarton = innerCarton
            totalPcs =
              innerCarton * innerMaster * Number(prodPpcNum || 0) +
              innerPcs * Number(prodPpcNum || 0) +
              pcs
          } else {
            totalCarton = carton
            totalPcs = avrRtpcQty
          }

          // Special calculation for total_ai based on ai column value being "AI"
          if (aiValue && String(aiValue).toUpperCase() === 'AI') {
            totalAi = totalPcs
          }

          // Calculate total_m3 = avr_rtpc_qty * product_pcm3
          if (avrRtpcQty > 0 && prodPcm3 && Number(prodPcm3) > 0) {
            totalM3 = avrRtpcQty * Number(prodPcm3)
          } else {
            totalM3 = 0
          }
        } catch (calcError) {
          console.warn(`Warning: Error in calculations for row ${i + 1}:`, calcError)
          // Keep default values (0) if calculation fails
        }
        // Add product data columns
        values.push(prodPpcNum)
        values.push(prodHrc1)
        values.push(aiValue)
        values.push(innerMaster)

        // Add calculated columns
        values.push(carton)
        values.push(pcs)
        values.push(innerCarton)
        values.push(innerPcs)
        values.push(totalCarton)
        values.push(totalPcs)
        values.push(totalAi)
        values.push(totalM3)

        console.log(`Row ${i + 1} values:`, values)

        // Execute the INSERT
        await executeQuery(insertSQL, values)

        // Extract product code and quantity for stock deduction
        const productCodeCol = Object.keys(excelToDbMapping).find(
          (excelCol) => excelToDbMapping[excelCol] === 'spr_prod_code',
        )
        const quantityCol = Object.keys(excelToDbMapping).find(
          (excelCol) => excelToDbMapping[excelCol] === 'spr_rtpc_qty',
        )

        if (productCodeCol && quantityCol) {
          const productCode = row[productCodeCol] as string
          const avrRtpcQty = Number(row[quantityCol]) || 0

          if (productCode && avrRtpcQty > 0) {
            try {
              await minusStock(productCode, customerId, avrRtpcQty)
              console.log(`Stock deducted successfully for product: ${productCode}`)
            } catch (stockError) {
              console.warn(`Stock deduction failed for product ${productCode}:`, stockError)
              // Note: We don't throw here to continue processing other rows
            }
          }
        }

        successCount++
      } catch (error) {
        console.error(`Error inserting row ${i + 1}:`, error)
        errorCount++
        errors.push(`Row ${i + 1}: ${error instanceof Error ? error.message : String(error)}`)
      }
    }

    console.log(`Import completed: ${successCount} successful, ${errorCount} failed`)

    return { successCount, errorCount, errors }
  } catch (error) {
    console.error('Error in data import process:', error)
    throw error
  }
}

/**
 * ==============================================
 * Helper Functions
 * ==============================================
 */

/**
 * Retrieves extended product data with calculated values for Excel processing
 *
 * This function extends basic product data lookup by calculating additional
 * values needed for inbound processing, including AI values and inner master
 * calculations based on HRC1 specifications.
 *
 * @param {string} productCode - The product code to search for
 * @returns {Promise<Object>} Extended product data with calculated values
 * @returns {string | number | null} returns.prodPpcNum - Product PPC number
 * @returns {string | number | null} returns.prodHrc1 - Product HRC1 specification
 * @returns {string | number | null} returns.prodPcm3 - Product PCM3 volume specification
 * @returns {string | number} returns.aiValue - AI value derived from HRC1 or default '-'
 * @returns {number} returns.innerMaster - Inner master count extracted from HRC1 parentheses
 * @throws {Error} When database query fails or data processing error occurs
 *
 * @example
 * ```typescript
 * const extendedData = await getProductDataExtended('PROD001');
 * console.log('AI Value:', extendedData.aiValue);
 * console.log('Inner Master:', extendedData.innerMaster);
 * // If HRC1 is "Product(12)", innerMaster will be 12
 * ```
 *
 * @since 1.0.0
 */
export const getProductDataExtended = async (
  productCode: string,
): Promise<{
  prodPpcNum: string | number | null
  prodHrc1: string | number | null
  prodPcm3: string | number | null
  aiValue: string | number
  innerMaster: number
}> => {
  try {
    const productQuery = `SELECT product_ppc_num, product_hrc1, product_pcm3 FROM imported_products WHERE product_code = ?`
    const productResults = await executeQuery(productQuery, [productCode])

    let prodPpcNum: string | number | null = null
    let prodHrc1: string | number | null = null
    let prodPcm3: string | number | null = null
    let aiValue: string | number = '-'
    let innerMaster = 0

    if (productResults?.result?.resultRows && productResults.result.resultRows.length > 0) {
      const productData = productResults.result.resultRows[0]
      prodPpcNum = (productData[0] as string | number) || null
      prodHrc1 = (productData[1] as string | number) || null
      prodPcm3 = (productData[2] as string | number) || null

      // Set ai value to prod_hrc1 if found, otherwise keep default '-'
      aiValue = prodHrc1 || '-'

      // Calculate inner_master based on prod_hrc1 value
      if (prodHrc1) {
        const hrc1String = String(prodHrc1)
        // Extract any number within parentheses regardless of prefix text
        const numberMatch = hrc1String.match(/\((\d+)\)/)
        if (numberMatch) {
          innerMaster = parseInt(numberMatch[1], 10)
        }
        // For any value without parentheses or numbers, inner_master remains 0 (default)
      }
    }

    return { prodPpcNum, prodHrc1, prodPcm3, aiValue, innerMaster }
  } catch (error) {
    console.error('Error getting extended product data:', error)
    throw error
  }
}

/**
 * Formats date strings to a consistent YYYY-MM-DD format
 *
 * This helper function parses various date formats and converts them to the
 * standard ISO date format (YYYY-MM-DD) required by the database.
 *
 * @param dateStr - The date string to format
 * @returns string The formatted date string in YYYY-MM-DD format, or original string if parsing fails
 *
 * @example
 * ```typescript
 * const formatted1 = formatDate('01/15/2024'); // '2024-01-15'
 * const formatted2 = formatDate('2024-01-15'); // '2024-01-15'
 * const formatted3 = formatDate('invalid'); // 'invalid'
 * ```
 */
export const formatDate = (dateStr: string): string => {
  try {
    // Try to parse the date string and format it consistently
    const date = new Date(dateStr)
    if (!isNaN(date.getTime())) {
      // Format as YYYY-MM-DD
      return date.toISOString().split('T')[0]
    }
    return dateStr
  } catch {
    return dateStr
  }
}
