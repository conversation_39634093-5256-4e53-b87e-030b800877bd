<template>
  <div class="invoice-form-view">
    <!-- Compact Header -->
    <div class="sticky-header">
      <div class="header-content">
        <div class="header-left">
          <div class="title-section">
            <h2>{{ getPageTitle }}</h2>
            <a-tag :color="getStatusColor(invoiceForm.status)" class="status-tag">
              {{ getStatusText(invoiceForm.status) }}
            </a-tag>
          </div>
        </div>
        <div class="header-right">
          <div class="total-section">
            <span class="amount">{{ formatCurrency(grandTotal) }}</span>
          </div>
          <div class="action-buttons">
            <a-button @click="handleCancel">Cancel</a-button>
            <a-button v-if="canSave" type="primary" @click="handleSave" :disabled="isSaveDisabled">
              <template #icon><SaveOutlined /></template>
              Save
            </a-button>
            <a-button v-if="canApprove" type="primary" @click="handleApprove">
              <template #icon><CheckOutlined /></template>
              Approve
            </a-button>
            <a-button v-if="canDisapprove" danger @click="handleDisapprove">
              <template #icon><CloseOutlined /></template>
              Disapprove
            </a-button>
          </div>
        </div>
      </div>

      <!-- Compact Form Section -->
      <div class="quick-form-section">
        <a-row :gutter="16" class="form-row">
          <a-col :span="7">
            <a-form-item
              label="Customer"
              :validate-status="formErrors.customer ? 'error' : ''"
              :help="formErrors.customer"
              required
            >
              <a-select
                v-model:value="invoiceForm.customerId"
                :disabled="isCustomerDisabled"
                :options="customers.map((c) => ({ value: c.id, label: c.name }))"
                placeholder="Select a customer"
                @change="validateCustomer"
                show-search
                :filter-option="filterOption"
              />
            </a-form-item>
          </a-col>
          <a-col :span="7">
            <a-form-item label="Division">
              <a-select
                v-model:value="invoiceForm.customerDivisionId"
                :disabled="isCustomerDisabled || !invoiceForm.customerId"
                :options="divisions.map((d) => ({ value: d.id, label: d.name }))"
                placeholder="Select a division"
                show-search
                :filter-option="filterOption"
              />
            </a-form-item>
          </a-col>
          <a-col :span="10">
            <a-form-item label="Notes">
              <a-input
                v-model:value="invoiceForm.notes"
                :disabled="isReadOnly"
                placeholder="Add notes here..."
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- Alert Section if needed -->
      <div v-if="showAlert" class="alert-container">
        <a-alert type="error" show-icon>
          <template #description>
            <div class="error-list">
              <template v-if="formErrors.customer">
                <div class="error-item">
                  <ExclamationCircleOutlined />
                  <span>Customer: {{ formErrors.customer }}</span>
                </div>
              </template>
              <template v-if="formErrors.categories.length > 0">
                <div
                  v-for="(error, index) in formErrors.categories"
                  :key="index"
                  class="error-item"
                >
                  <ExclamationCircleOutlined />
                  <span>{{ error }}</span>
                </div>
              </template>
            </div>
          </template>
        </a-alert>
      </div>
    </div>

    <!-- Main Content - Categories Section -->
    <div class="main-content">
      <div class="categories-section">
        <div class="section-header">
          <h3>Categories</h3>
          <div class="action-buttons">
            <a-button
              v-if="canAutoGenerate"
              type="default"
              @click="handleAutoGenerate"
              :loading="isAutoGenerating"
            >
              <template #icon><ThunderboltOutlined /></template>
              Auto Generate
            </a-button>
            <a-button
              v-if="!isReadOnly"
              type="primary"
              @click="showCategoryDialog"
              :disabled="!canAddCategory"
            >
              <template #icon><PlusOutlined /></template>
              Add Category
            </a-button>
          </div>
        </div>

        <div class="categories-list-wrapper">
          <invoice-category-list
            :categories="invoiceForm.categories"
            :all-categories="allCategories"
            :workouts="workouts"
            :units="units"
            :customer-id="invoiceForm.customerId"
            :customer-division-id="invoiceForm.customerDivisionId"
            :readonly="isReadOnly"
            :errors="formErrors.charges"
            @add-charge="handleAddCharge"
            @update-charge="handleUpdateCharge"
            @delete-charge="handleDeleteCharge"
            @delete-category="handleDeleteCategory"
          />

          <div v-if="invoiceForm.categories.length === 0" class="empty-state">
            <a-empty
              :description="
                isReadOnly ? 'No categories added' : 'Click &quot;Add Category&quot; to start'
              "
            >
              <template #image>
                <InboxOutlined style="font-size: 48px; color: #bfbfbf" />
              </template>
            </a-empty>
          </div>
        </div>
      </div>
    </div>

    <category-select-dialog
      :visible="categoryDialogVisible"
      :all-categories="allCategories"
      :current-categories="invoiceForm.categories"
      @update:visible="categoryDialogVisible = $event"
      @select="handleCategorySelect"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  PlusOutlined,
  SaveOutlined,
  CheckOutlined,
  CloseOutlined,
  InboxOutlined,
  ExclamationCircleOutlined,
  ThunderboltOutlined,
} from '@ant-design/icons-vue'
import { useInvoiceService } from '../services/invoiceLocalService'
import InvoiceCategoryList from '../components/InvoiceCategoryList.vue'
import CategorySelectDialog from '../components/dialogs/CategorySelectDialog.vue'
import type { Invoice, InvoiceCategory, InvoiceCharge } from '../types/invoice'
import type { Category } from '../types/MasterDataTypes/Category'
import type { Customer } from '../types/MasterDataTypes/Customer'
import type { Unit } from '../types/MasterDataTypes/Unit'
import type { Workout } from '../types/MasterDataTypes/Category'
import type { CustomerDivision } from '../types/MasterDataTypes/Customer'
import type { WorkoutCustomerCharge } from '../types/MasterDataTypes/WorkoutCustomerCharge'
import { message } from 'ant-design-vue'

const route = useRoute()
const router = useRouter()
const invoiceService = useInvoiceService()

const mode = computed(() => {
  if (route.query.mode === 'view') return 'view'
  return isEditMode.value ? 'edit' : 'new'
})

const isEditMode = computed(() => !!route.query.id)
const invoiceId = computed(() => route.query.id as string | undefined)

const customers = ref<Customer[]>([])
const divisions = ref<CustomerDivision[]>([])
const allCategories = ref<Category[]>([])
const workouts = ref<Workout[]>([])
const units = ref<Unit[]>([])
const categoryDialogVisible = ref(false)

const invoiceForm = reactive<Invoice>({
  id: '',
  customerId: undefined,
  customerDivisionId: undefined,
  totalAmount: 0,
  status: 'DRAFT',
  notes: '',
  createdAt: '',
  updatedAt: '',
  categories: [],
})

// Add validation state
const formErrors = reactive({
  customer: '',
  categories: [] as string[],
  charges: {} as Record<string, string[]>,
})

const hasErrors = ref(false)
const showAlert = ref(false)

// Add watcher for error states
watch(
  [() => formErrors.customer, () => formErrors.categories, () => formErrors.charges],
  ([customer, categories, charges]) => {
    hasErrors.value = !!customer || categories.length > 0 || Object.keys(charges).length > 0
    showAlert.value = hasErrors.value
  },
  { immediate: true },
)

// Add function to handle alert visibility
const startAlertTimer = () => {
  if (hasErrors.value) {
    showAlert.value = true
    setTimeout(() => {
      showAlert.value = false
    }, 5000)
  }
}

// Validation rules
const validateCustomer = () => {
  if (!invoiceForm.customerId) {
    formErrors.customer = 'Please select a customer'
    return false
  }
  formErrors.customer = ''
  return true
}

const removeCategoryError = (categoryError: string) => {
  const index = formErrors.categories.indexOf(categoryError)
  if (index > -1) {
    formErrors.categories.splice(index, 1)
  }
}

const validateCharges = () => {
  let isValid = true
  formErrors.charges = {}

  invoiceForm.categories.forEach((category) => {
    if (category.charges.length === 0) {
      formErrors.categories.push(`Category "${category.name}" must have at least one charge`)
      isValid = false
      return
    }
    const categoryError = `Please fix the errors in the category "${category.name}"`
    removeCategoryError(categoryError)

    category.charges.forEach((charge) => {
      const chargeErrors: string[] = []

      if (!charge.workoutId) {
        chargeErrors.push('Workout is required')
      }
      if (!charge.quantity || charge.quantity <= 0) {
        chargeErrors.push('Quantity must be greater than 0')
      }
      // if (!charge.unitPrice || charge.unitPrice <= 0) {
      //   chargeErrors.push('Unit price must be greater than 0')
      // }
      // if (charge.totalAmount <= 0) {
      //   chargeErrors.push('Total amount must be greater than 0')
      // }

      if (chargeErrors.length > 0) {
        if (!formErrors.categories.includes(categoryError)) {
          formErrors.categories.push(categoryError)
        }
        formErrors.charges[charge.id] = chargeErrors
        isValid = false
      }
    })
  })

  return isValid
}

const validateForm = () => {
  // Reset all errors
  formErrors.customer = ''
  formErrors.categories = []
  formErrors.charges = {}

  let isValid = true

  // Validate customer
  if (!invoiceForm.customerId) {
    formErrors.customer = 'Please select a customer'
    isValid = false
  }

  // Validate categories exist
  if (invoiceForm.categories.length === 0) {
    formErrors.categories.push('Please add at least one category')
    isValid = false
  }

  // Validate charges
  const chargesValid = validateCharges()
  isValid = isValid && chargesValid

  setTimeout(() => {
    startAlertTimer()
  }, 100)
  return isValid
}

// Add watch for customer changes
watch(
  () => invoiceForm.customerId,
  async (newCustomerId) => {
    invoiceForm.customerDivisionId = undefined
    divisions.value = []

    if (newCustomerId) {
      try {
        divisions.value = await invoiceService.getCustomerDivisions(newCustomerId)
      } catch (error) {
        console.error('Error fetching divisions:', error)
        message.error('Failed to load divisions')
      }
    }
  },
)

const hasAvailableCategories = computed(() => {
  return allCategories.value.some(
    (cat: Category) => !invoiceForm.categories.some((c) => c.categoryId === cat.id),
  )
})

const getPageTitle = computed(() => {
  switch (mode.value) {
    case 'view':
      return 'View Invoice'
    case 'edit':
      return 'Edit Invoice'
    default:
      return 'New Invoice'
  }
})

const isReadOnly = computed(() => {
  if (mode.value === 'view') return true
  if (!isEditMode.value) return false

  // In edit mode, check status
  return ['APPROVED', 'PAID', 'OVERDUE'].includes(invoiceForm.status)
})

const canSave = computed(() => {
  if (mode.value === 'view') return false
  if (!isEditMode.value) return true

  // In edit mode, check status
  return ['DRAFT', 'PENDING', 'REJECTED'].includes(invoiceForm.status)
})

// Add disabled state for save button
const isSaveDisabled = computed(() => {
  return !invoiceForm.customerId
})

const canApprove = computed(() => {
  // Only show in view mode and when status is DRAFT
  return mode.value === 'view' && invoiceForm.status === 'DRAFT'
})

const canDisapprove = computed(() => {
  if (!isEditMode.value) return false
  // Can only disapprove if status is APPROVED
  return invoiceForm.status === 'APPROVED'
})

const canAddCategory = computed(() => {
  return !isReadOnly.value && invoiceForm.customerId && hasAvailableCategories.value
})

const formatCurrency = (amount: number | undefined) => {
  if (amount === undefined) return '$0.00'
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount)
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'DRAFT':
      return 'processing'
    case 'PENDING':
      return 'warning'
    case 'APPROVED':
      return 'success'
    case 'REJECTED':
      return 'error'
    case 'PAID':
      return 'success'
    case 'OVERDUE':
      return 'error'
    default:
      return undefined
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'DRAFT':
      return 'Draft'
    case 'PENDING':
      return 'Pending'
    case 'APPROVED':
      return 'Approved'
    case 'REJECTED':
      return 'Rejected'
    case 'PAID':
      return 'Paid'
    case 'OVERDUE':
      return 'Overdue'
    default:
      return status
  }
}

const grandTotal = computed(() => {
  return invoiceForm.categories.reduce(
    (sum: number, cat: InvoiceCategory) =>
      sum + cat.charges.reduce((s: number, c: InvoiceCharge) => s + (c.totalAmount || 0), 0),
    0,
  )
})

const fetchMasterData = async () => {
  customers.value = await invoiceService.getAllCustomers()
  allCategories.value = await invoiceService.getAllCategories()
  workouts.value = await invoiceService.getAllWorkouts()
  units.value = await invoiceService.getAllUnits()

  // If we have a customer ID, fetch its divisions
  if (invoiceForm.customerId) {
    try {
      divisions.value = await invoiceService.getCustomerDivisions(invoiceForm.customerId)
    } catch (error) {
      console.error('Error fetching divisions:', error)
      message.error('Failed to load divisions')
    }
  }
}

const fetchInvoice = async () => {
  if (isEditMode.value && invoiceId.value) {
    const invoice = (await invoiceService.getAllInvoices()).find((i) => i.id === invoiceId.value)
    if (invoice) {
      Object.assign(invoiceForm, invoice)
    }
  }
}

const showCategoryDialog = () => {
  categoryDialogVisible.value = true
}

const handleCategorySelect = (categoryIds: number[]) => {
  categoryIds.forEach((categoryId) => {
    const category = allCategories.value.find((c: Category) => c.id === categoryId)
    if (category) {
      invoiceForm.categories.push({
        id: `CAT-${Date.now()}-${categoryId}`,
        invoiceId: invoiceForm.id,
        categoryId: category.id,
        name: category.name,
        order: invoiceForm.categories.length + 1,
        charges: [],
      })
    }
  })
}

const handleDeleteCategory = (categoryId: string) => {
  invoiceForm.categories = invoiceForm.categories.filter((c) => c.id !== categoryId)
}

const handleAddCharge = (categoryId: string) => {
  const cat = invoiceForm.categories.find((c: InvoiceCategory) => c.id === categoryId)
  if (cat) {
    cat.charges.push({
      id: `CHG-${Date.now()}`,
      invoiceId: invoiceForm.id,
      categoryId: cat.categoryId,
      workoutId: undefined,
      unitId: undefined,
      unitPrice: 0,
      quantity: 0,
      totalAmount: 0,
      vatRate: 0,
      vatAmount: 0,
      status: 'DRAFT',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    })
  }
}

const handleUpdateCharge = (categoryId: string, charge: InvoiceCharge) => {
  const cat = invoiceForm.categories.find((c: InvoiceCategory) => c.id === categoryId)
  if (cat) {
    const idx = cat.charges.findIndex((ch: InvoiceCharge) => ch.id === charge.id)
    if (idx !== -1) cat.charges[idx] = { ...charge }
  }
}

const handleDeleteCharge = (categoryId: string, chargeId: string) => {
  const cat = invoiceForm.categories.find((c: InvoiceCategory) => c.id === categoryId)
  if (cat) {
    cat.charges = cat.charges.filter((ch: InvoiceCharge) => ch.id !== chargeId)
  }
}

const handleSave = async () => {
  try {
    if (isReadOnly.value) return

    if (!validateForm()) {
      message.error('Please fix all errors before saving')
      return
    }

    invoiceForm.totalAmount = grandTotal.value
    invoiceForm.updatedAt = new Date().toISOString()

    if (isEditMode.value) {
      await invoiceService.updateInvoice(invoiceForm.id, { ...invoiceForm })
    } else {
      invoiceForm.id = `INV-${Date.now()}`
      invoiceForm.createdAt = new Date().toISOString()
      invoiceForm.updatedAt = invoiceForm.createdAt
      await invoiceService.createInvoice({ ...invoiceForm })
    }

    message.success('Invoice saved successfully')
    router.push({ name: 'Invoice List' })
  } catch (error) {
    message.error('Failed to save invoice')
    console.error('Error saving invoice:', error)
  }
}

const handleCancel = () => {
  router.push({ name: 'Invoice List' })
}

const handleApprove = async () => {
  try {
    if (!canApprove.value) return

    invoiceForm.status = 'APPROVED'
    await invoiceService.updateInvoice(invoiceForm.id, { status: 'APPROVED' })
    message.success('Invoice approved successfully')
    router.push({ name: 'Invoice List' })
  } catch (error) {
    message.error('Failed to approve invoice')
    console.error('Error approving invoice:', error)
  }
}

const handleDisapprove = async () => {
  try {
    if (!canDisapprove.value) return

    invoiceForm.status = 'DRAFT'
    await invoiceService.updateInvoice(invoiceForm.id, { status: 'DRAFT' })
    message.success('Invoice disapproved successfully')
    router.push({ name: 'Invoice List' })
  } catch (error) {
    message.error('Failed to disapprove invoice')
    console.error('Error disapproving invoice:', error)
  }
}

// Add filter function for select components
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// Add number formatting function
const formatNumber = (value: number | undefined) => {
  if (value === undefined || value === null) return ''
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value)
}

// Thêm computed property để kiểm tra điều kiện disable customer
const isCustomerDisabled = computed(() => {
  return (
    isReadOnly.value ||
    invoiceForm.categories.length > 0 ||
    invoiceForm.categories.some((cat) => cat.charges.length > 0)
  )
})

// Add new state for auto generation
const isAutoGenerating = ref(false)

// Add computed property for auto generate button visibility
const canAutoGenerate = computed(() => {
  return !isReadOnly.value && invoiceForm.customerId && invoiceForm.categories.length === 0
})

// Add function to handle auto generation
const handleAutoGenerate = async () => {
  try {
    if (!invoiceForm.customerId) return

    isAutoGenerating.value = true
    const currentDate = new Date()
    const currentYear = currentDate.getFullYear()
    const currentMonth = currentDate.getMonth() + 1

    const workoutCharges = await invoiceService.getWorkoutCustomerCharges({
      customerId: invoiceForm.customerId,
      divisionId: invoiceForm.customerDivisionId,
      year: currentYear,
      month: currentMonth,
    })

    // Group charges by category
    const chargesByCategory = new Map<number, WorkoutCustomerCharge[]>()
    workoutCharges.forEach((charge: WorkoutCustomerCharge) => {
      if (!chargesByCategory.has(charge.categoryId)) {
        chargesByCategory.set(charge.categoryId, [])
      }
      chargesByCategory.get(charge.categoryId)?.push(charge)
    })

    // Create categories and charges
    for (const [categoryId, charges] of chargesByCategory) {
      const category = allCategories.value.find((c) => c.id === categoryId)
      if (!category) continue

      const newCategory: InvoiceCategory = {
        id: `CAT-${Date.now()}-${categoryId}`,
        invoiceId: invoiceForm.id,
        categoryId: category.id,
        name: category.name,
        order: invoiceForm.categories.length + 1,
        charges: [],
      }

      // Add charges to category
      for (const charge of charges) {
        const workout = workouts.value.find((w: Workout) => w.id === charge.workoutId)
        if (!workout) continue

        const monthKey = `month_${currentMonth}` as keyof WorkoutCustomerCharge
        const quantity = charge.finalQuantity ?? charge[monthKey]
        // Skip if no value for this month
        if (typeof quantity !== 'number') continue

        const newCharge: InvoiceCharge = {
          id: `CHG-${Date.now()}-${charge.id}`,
          invoiceId: invoiceForm.id,
          categoryId: category.id,
          workoutId: workout.id,
          unitId: workout.unitId ?? undefined,
          unitPrice: workout.unitPrice || 0,
          quantity: quantity,
          totalAmount: 0, // Will be calculated below
          vatRate: workout.vat || 0,
          vatAmount: 0, // Will be calculated below
          debitCodeId: workout.debitCodeId ?? undefined,
          status: 'DRAFT',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }

        // Calculate totalAmount and vatAmount
        newCharge.totalAmount = (workout.unitPrice || 0) * newCharge.quantity
        newCharge.vatAmount = newCharge.totalAmount * (workout.vat || 0)

        newCategory.charges.push(newCharge)
      }

      if (newCategory.charges.length > 0) {
        invoiceForm.categories.push(newCategory)
      }
    }

    if (invoiceForm.categories.length === 0) {
      message.info('No charges found for the selected customer and period')
    }
  } catch (error) {
    console.error('Error auto generating charges:', error)
    message.error('Failed to auto generate charges')
  } finally {
    isAutoGenerating.value = false
  }
}

onMounted(async () => {
  await fetchMasterData()
  await fetchInvoice()

  // For new invoice, check if customer and division are provided in query params
  if (!isEditMode.value) {
    const customerId = route.query.customerId ? Number(route.query.customerId) : undefined
    if (customerId) {
      invoiceForm.customerId = customerId
      // Divisions will be fetched by the watcher

      // If division is provided and customer exists, set it after divisions are loaded
      const divisionId = route.query.divisionId ? Number(route.query.divisionId) : undefined
      if (divisionId) {
        // Wait for divisions to be loaded
        await new Promise((resolve) => setTimeout(resolve, 100))
        if (divisions.value.find((d) => d.id === divisionId)) {
          invoiceForm.customerDivisionId = divisionId
        }
      }
    }
  }

  // Set CSS variable for sticky header height
  const stickyHeader = document.querySelector('.sticky-header')
  if (stickyHeader) {
    document.documentElement.style.setProperty(
      '--sticky-header-height',
      `${(stickyHeader as HTMLElement).offsetHeight}px`,
    )
  }
})
</script>

<style scoped>
.invoice-form-view {
  min-height: 100%;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
}

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-content {
  padding: 12px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
  margin-top: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-section h2 {
  margin: 0;
  color: #262626;
  font-size: 20px;
  line-height: 28px;
}

.status-tag {
  font-size: 14px;
  padding: 0 12px;
  height: 24px;
  line-height: 22px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 24px;
}

.total-section .amount {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.quick-form-section {
  padding: 12px 24px;
  background: #fff;
}

.form-row {
  margin: 0 !important;
}

.form-row :deep(.ant-form-item) {
  margin-bottom: 0;
}

.form-row :deep(.ant-form-item-label) {
  padding-bottom: 4px;
}

.alert-container {
  padding: 8px 24px;
  width: 100%;
  background: #fff2f0;
  border-bottom: 1px solid #ffccc7;
  transition: opacity 0.3s ease-in-out;
}

.alert-container.v-enter-active,
.alert-container.v-leave-active {
  transition: opacity 0.3s ease;
}

.alert-container.v-enter-from,
.alert-container.v-leave-to {
  opacity: 0;
}

.error-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.error-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ff4d4f;
  font-size: 13px;
  line-height: 1.5;
  padding: 4px 0;
}

.error-item .anticon {
  font-size: 14px;
}

:deep(.ant-alert) {
  margin-bottom: 0;
  border: none;
  background: transparent;
  padding: 0;
}

:deep(.ant-alert-description) {
  margin: 0;
}

:deep(.ant-alert-icon) {
  display: none;
}

/* Remove font-family settings */
:deep(.numeric-cell) {
  text-align: right;
  padding-right: 8px !important;
}

:deep(.ant-input-number-input) {
  text-align: right !important;
}

/* Improve table number formatting */
:deep(.numeric-cell) {
  /* font-family: 'SF Mono', 'Courier New', Courier, monospace; */
  text-align: right;
  padding-right: 8px !important;
}

:deep(.ant-input-number) {
  width: 120px !important;
}

:deep(.ant-input-number-input) {
  text-align: right !important;
  /* font-family: 'SF Mono', 'Courier New', Courier, monospace; */
}

:deep(.ant-table-cell) {
  vertical-align: top !important;
}

/* Add subtle hover effect for table rows */
:deep(.ant-table-tbody > tr:hover > td) {
  background-color: rgba(0, 0, 0, 0.02) !important;
}

/* Improve input states */
:deep(.ant-input-number-focused) {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

:deep(
  .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input)
    .ant-select-selector
) {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  height: calc(100vh - var(--sticky-header-height));
}

.categories-section {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  padding: 8px 24px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.section-header h3 {
  margin: 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.categories-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.categories-list-wrapper {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.empty-state {
  padding: 48px;
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  text-align: center;
}

:deep(.ant-form-item-explain) {
  position: absolute;
}

:deep(.ant-alert) {
  margin-bottom: 0;
}
</style>
