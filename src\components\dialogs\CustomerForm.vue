<template>
  <a-modal
    :title="formState.id ? 'Edit Customer' : 'Add Customer'"
    :open="visible"
    :confirm-loading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formState" layout="vertical" name="customer_form">
      <a-form-item
        name="code"
        label="Code"
        :rules="[{ required: true, message: 'Please input the code!' }]"
      >
        <a-input v-model:value="formState.code" />
      </a-form-item>
      <a-form-item
        name="name"
        label="Name"
        :rules="[{ required: true, message: 'Please input the name!' }]"
      >
        <a-input v-model:value="formState.name" />
      </a-form-item>
      <a-form-item name="address" label="Address">
        <a-input v-model:value="formState.address" />
      </a-form-item>
      <a-form-item name="tax_number" label="Tax Number">
        <a-input v-model:value="formState.taxNumber" />
      </a-form-item>
      <a-form-item name="phone" label="Phone">
        <a-input v-model:value="formState.phone" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, toRaw } from 'vue'
import type { FormInstance } from 'ant-design-vue'
import type { Customer } from '@IVC/types/MasterDataTypes/Customer'

interface Props {
  visible: boolean
  customerData?: Customer | null
}

const props = defineProps<Props>()
const emit = defineEmits(['close', 'save'])

const formRef = ref<FormInstance>()
const confirmLoading = ref(false)

const initialFormState: Omit<Customer, 'id'> & { id?: number } = {
  id: undefined,
  code: '',
  name: '',
  address: '',
  taxNumber: '',
  phone: '',
  status: 'new',
}
const formState = reactive({ ...initialFormState })

watch(
  () => props.customerData,
  (newData) => {
    if (newData) {
      Object.assign(formState, newData)
    } else {
      Object.assign(formState, initialFormState)
      formState.id = undefined // Ensure id is reset for new customer
    }
  },
  { immediate: true, deep: true },
)

const handleOk = async () => {
  try {
    await formRef.value?.validate()
    confirmLoading.value = true
    const dataToSave: Partial<Customer> = { ...toRaw(formState) }
    if (!formState.id) {
      delete dataToSave.id
    }
    emit('save', dataToSave)
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}

const handleCancel = () => {
  emit('close')
}

const resetFormAndLoading = () => {
  formRef.value?.resetFields()
  Object.assign(formState, initialFormState)
  formState.id = undefined
  confirmLoading.value = false
}

defineExpose({ resetFormAndLoading })
</script>
