// Test data seeding script for Previous data functionality testing
import { useSQLite } from '@IVC/hooks/useSQLite'
import { upsertImportedProduct } from '@IVC/services/master-data/product'
import { createOrUpdateStock } from '@IVC/services/master-data/stock'

const { executeQuery, tables } = useSQLite()

// Test data seeding function
export async function seedTestData(): Promise<void> {
  try {
    console.log('=== SEEDING TEST DATA ===')

    // First, ensure we have a customer
    const customerQuery = `
      INSERT OR IGNORE INTO ${tables.customers} (id, code, name, address, tax_number, phone, status)
      VALUES (1, 'CUST001', 'Test Customer', '123 Test St', 'TAX001', '************', 'Active')
    `
    await executeQuery(customerQuery, [])
    console.log('Customer created/ensured')

    // Create first version of products
    const product1V1 = await upsertImportedProduct({
      productCode: 'PROD001',
      productName: 'Test Product 1',
      productPpcNum: '12', // 12 pieces per carton
      productHrc1: 'Category A',
      productHrc3: 'Sub Category A',
      productPcm3: '0.5',
      customerId: 1,
    })
    console.log('Created product 1 version 1:', product1V1)

    const product2V1 = await upsertImportedProduct({
      productCode: 'PROD002',
      productName: 'Test Product 2',
      productPpcNum: '24', // 24 pieces per carton
      productHrc1: 'Category B',
      productHrc3: 'Sub Category B',
      productPcm3: '1.0',
      customerId: 1,
    })
    console.log('Created product 2 version 1:', product2V1)

    // Create stock records for version 1
    const stock1V1 = await createOrUpdateStock({
      productCode: 'PROD001',
      customerId: 1,
      avrRtpcQty: 100, // 100 pieces total
      avrRtpcQtyTemp: 100,
      location: 'Warehouse A',
    })
    console.log('Created stock 1 version 1:', stock1V1)

    const stock2V1 = await createOrUpdateStock({
      productCode: 'PROD002',
      customerId: 1,
      avrRtpcQty: 240, // 240 pieces total
      avrRtpcQtyTemp: 240,
      location: 'Warehouse A',
    })
    console.log('Created stock 2 version 1:', stock2V1)

    // Update products to create version 2 - simulate product updates with different PPC values
    const product1V2 = await upsertImportedProduct({
      productCode: 'PROD001',
      productName: 'Test Product 1',
      productPpcNum: '10', // Changed from 12 to 10 pieces per carton
      productHrc1: 'Category A',
      productHrc3: 'Sub Category A',
      productPcm3: '0.5',
      customerId: 1,
    })
    console.log('Updated product 1 to version 2:', product1V2)

    const product2V2 = await upsertImportedProduct({
      productCode: 'PROD002',
      productName: 'Test Product 2',
      productPpcNum: '20', // Changed from 24 to 20 pieces per carton
      productHrc1: 'Category B',
      productHrc3: 'Sub Category B',
      productPcm3: '1.0',
      customerId: 1,
    })
    console.log('Updated product 2 to version 2:', product2V2)

    // Update stock quantities (creates new versions)
    const stock1V2 = await createOrUpdateStock({
      productCode: 'PROD001',
      customerId: 1,
      avrRtpcQty: 150, // Changed from 100 to 150 pieces total
      avrRtpcQtyTemp: 150,
      location: 'Warehouse A',
    })
    console.log('Created stock 1 version 2:', stock1V2)

    const stock2V2 = await createOrUpdateStock({
      productCode: 'PROD002',
      customerId: 1,
      avrRtpcQty: 200, // Changed from 240 to 200 pieces total
      avrRtpcQtyTemp: 200,
      location: 'Warehouse A',
    })
    console.log('Created stock 2 version 2:', stock2V2)

    console.log('=== TEST DATA SEEDING COMPLETE ===')
    console.log('Expected Previous data:')
    console.log('- PROD001: Previous PPC = 12, Previous Stock = 100')
    console.log('- PROD002: Previous PPC = 24, Previous Stock = 240')
    console.log('Current data:')
    console.log('- PROD001: Current PPC = 10, Current Stock = 150')
    console.log('- PROD002: Current PPC = 20, Current Stock = 200')
  } catch (error) {
    console.error('Error seeding test data:', error)
    throw error
  }
}
