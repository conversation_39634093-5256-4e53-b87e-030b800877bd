export interface CompanyStore {
  id: number
  name: string
  location: string
  manager: string
  phone: string
  status: 'active' | 'inactive'
  isDefault: boolean
}

export interface DebitCode {
  id: number
  code: string
  name: string
  description: string
  isActive: boolean
  isUsed: boolean // Track if code is being used by any store
}

export interface CompanyInfo {
  name: string
  address: string
  phone: string
  taxCode: string
}

export interface CompanyProfile {
  id: number
  companyInfo: CompanyInfo
  stores: CompanyStore[]
  debitCodes: DebitCode[]
  createdAt: string
  updatedAt: string
}
