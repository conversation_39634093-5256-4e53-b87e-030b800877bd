<template>
  <a-modal
    :open="visible"
    :title="modalTitle"
    width="1400px"
    :confirm-loading="saving"
    @cancel="handleCancel"
    :mask-closable="false"
  >
    <div class="workout-charge-modal">
      <!-- Customer & Division Info - Fixed Header -->
      <div class="customer-info">
        <a-row :gutter="16">
          <a-col :span="12">
            <div class="info-item">
              <label>Customer:</label>
              <span class="info-value">{{ customerName }}</span>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="info-item">
              <label>Division:</label>
              <span class="info-value">{{ divisionName }}</span>
            </div>
          </a-col>
        </a-row>
      </div>

      <a-divider />

      <!-- Fixed Action Buttons -->
      <div class="action-buttons-fixed">
        <a-button
          type="primary"
          @click="addCategory"
          size="small"
          :disabled="!hasAvailableCategories"
          :title="!hasAvailableCategories ? 'No more categories available to add' : ''"
        >
          <template #icon>
            <PlusOutlined />
          </template>
          Add Category
        </a-button>
        <span v-if="!hasAvailableCategories" class="no-options-text">
          All categories have been added
        </span>
      </div>

      <!-- Scrollable Table Container -->
      <div class="table-container">
        <!-- Table -->
        <a-table
          :columns="columns"
          :data-source="tableData"
          :pagination="false"
          :show-header="true"
          row-key="key"
          size="small"
          :scroll="{ x: 1200, y: 400 }"
          :expandable="{
            expandedRowKeys: expandedKeys,
            onExpand: onExpand,
          }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div :style="{ paddingLeft: `${record.level * 20}px` }" class="name-cell">
                <!-- Category Level -->
                <a-select
                  v-if="record.type === 'category'"
                  v-model:value="record.categoryId"
                  placeholder="Select category"
                  size="small"
                  style="width: 100%"
                  @change="updateCategoryData(record)"
                  :show-search="true"
                  :filter-option="
                    (input, option) =>
                      option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  "
                >
                  <!-- Show current selection even if it's already used -->
                  <a-select-option
                    v-if="record.categoryId"
                    :key="`current-${record.categoryId}`"
                    :value="String(record.categoryId)"
                  >
                    {{
                      categories.find((c) => String(c.id) === String(record.categoryId))?.name ||
                      'Unknown Category'
                    }}
                  </a-select-option>

                  <!-- Show available categories (not yet selected) -->
                  <a-select-option
                    v-for="category in getAvailableCategories"
                    :key="category.id"
                    :value="String(category.id)"
                  >
                    {{ category.name }}
                  </a-select-option>
                </a-select>

                <!-- Workout Level -->
                <a-select
                  v-else-if="record.type === 'group'"
                  v-model:value="record.groupId"
                  placeholder="Select workout"
                  size="small"
                  style="width: 100%"
                  @change="updateGroupData(record)"
                  :disabled="!record.categoryId"
                  :show-search="true"
                  :filter-option="
                    (input, option) =>
                      option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  "
                >
                  <!-- Show current selection even if it's already used -->
                  <a-select-option
                    v-if="record.groupId"
                    :key="`current-${record.groupId}`"
                    :value="String(record.groupId)"
                  >
                    {{
                      getGroupsForCategory(record.categoryId).find(
                        (g) => String(g.id) === String(record.groupId),
                      )?.name || 'Unknown Workout'
                    }}
                  </a-select-option>

                  <!-- Show available workouts (not yet selected in this category) -->
                  <a-select-option
                    v-for="group in getAvailableWorkoutsForCategory(
                      record.categoryId,
                      record.categoryKey,
                    )"
                    :key="group.id"
                    :value="String(group.id)"
                  >
                    {{ group.name }}
                  </a-select-option>
                </a-select>

                <!-- Year Level - Grid Display -->
                <div v-else-if="record.type === 'year'" class="year-grid-container">
                  <div
                    style="
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                      margin-bottom: 4px;
                    "
                  >
                    <span class="year-label">
                      {{ record.name }}
                      <span
                        v-if="getWorkoutCalculationType(record.categoryKey, record.groupKey)"
                        style="font-size: 12px; color: #888; font-weight: 400; margin-left: 8px"
                      >
                        ({{ getWorkoutCalculationType(record.categoryKey, record.groupKey) }})
                      </span>
                    </span>
                    <div style="display: flex; align-items: center; gap: 8px; margin-left: auto">
                      <span style="font-size: 13px; color: #888">Quick fill:</span>
                      <a-input-number
                        v-model:value="
                          quickFillValues[
                            record.categoryKey + '-' + record.groupKey + '-' + record.year
                          ]
                        "
                        :min="0"
                        :precision="2"
                        size="small"
                        placeholder="Value"
                        style="width: 70px"
                        :disabled="
                          getRowCalculationType(record) === 'Auto' ||
                          getRowCalculationType(record) === 'Link'
                        "
                      />
                      <a-button
                        type="default"
                        size="small"
                        :disabled="
                          getRowCalculationType(record) === 'Auto' ||
                          getRowCalculationType(record) === 'Link' ||
                          quickFillValues[
                            record.categoryKey + '-' + record.groupKey + '-' + record.year
                          ] === undefined ||
                          quickFillValues[
                            record.categoryKey + '-' + record.groupKey + '-' + record.year
                          ] === null
                        "
                        @click="fillAllMonths(record.categoryKey, record.groupKey, record.year)"
                      >
                        Fill All
                      </a-button>
                      <a-button
                        type="default"
                        size="small"
                        :disabled="
                          getRowCalculationType(record) === 'Auto' ||
                          getRowCalculationType(record) === 'Link' ||
                          quickFillValues[
                            record.categoryKey + '-' + record.groupKey + '-' + record.year
                          ] === undefined ||
                          quickFillValues[
                            record.categoryKey + '-' + record.groupKey + '-' + record.year
                          ] === null
                        "
                        @click="
                          quickFillValues[
                            record.categoryKey + '-' + record.groupKey + '-' + record.year
                          ] = undefined
                        "
                      >
                        Clear
                      </a-button>
                    </div>
                  </div>

                  <!-- Year/Month Grid Table -->
                  <div class="year-month-table">
                    <table class="grid-table">
                      <thead>
                        <tr>
                          <th>Year</th>
                          <th>January</th>
                          <th>February</th>
                          <th>March</th>
                          <th>April</th>
                          <th>May</th>
                          <th>June</th>
                          <th>July</th>
                          <th>August</th>
                          <th>September</th>
                          <th>October</th>
                          <th>November</th>
                          <th>December</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td class="year-cell">{{ record.year }}</td>
                          <td v-for="month in 12" :key="month" class="month-cell">
                            <template
                              v-if="
                                getRowCalculationType(record) === 'Auto' ||
                                getRowCalculationType(record) === 'Link'
                              "
                            >
                              <span style="color: #aaa; font-style: italic">N/A</span>
                            </template>
                            <template v-else>
                              <a-input-number
                                :value="
                                  getMonthQuantity(
                                    record.categoryKey,
                                    record.groupKey,
                                    record.year,
                                    month,
                                  )
                                "
                                :min="0"
                                size="small"
                                style="width: 100%"
                                @change="
                                  updateMonthQuantity(
                                    record.categoryKey,
                                    record.groupKey,
                                    record.year,
                                    month,
                                    $event,
                                  )
                                "
                              />
                            </template>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                <!-- Month Level - Hidden (handled in year grid) -->
                <span v-else-if="record.type === 'month'" style="display: none"></span>
              </div>
            </template>

            <template v-else-if="column.key === 'actions'">
              <!-- Category Actions -->
              <div v-if="record.type === 'category'" class="action-group">
                <a-button
                  type="text"
                  size="small"
                  @click="addGroup(record.categoryKey)"
                  :disabled="
                    !canAddWorkout(record.categoryKey) || !hasAvailableWorkouts(record.categoryKey)
                  "
                  :title="
                    !canAddWorkout(record.categoryKey)
                      ? 'Please select a category first'
                      : !hasAvailableWorkouts(record.categoryKey)
                        ? 'No more workouts available for this category'
                        : ''
                  "
                >
                  <template #icon><PlusOutlined /></template>
                  Add Workout
                </a-button>
                <span
                  v-if="
                    canAddWorkout(record.categoryKey) && !hasAvailableWorkouts(record.categoryKey)
                  "
                  class="no-options-text"
                >
                  All workouts added
                </span>
                <a-button
                  type="text"
                  danger
                  size="small"
                  @click="removeCategory(record.categoryKey)"
                >
                  <template #icon><DeleteOutlined /></template>
                  Remove
                </a-button>
              </div>

              <!-- Workout Actions -->
              <div v-else-if="record.type === 'group'" class="action-group">
                <a-button
                  type="text"
                  size="small"
                  @click="addYear(record.categoryKey, record.groupKey)"
                  :disabled="!canAddYear(record.categoryKey, record.groupKey)"
                  :title="
                    !canAddYear(record.categoryKey, record.groupKey)
                      ? 'Please select a workout first'
                      : ''
                  "
                >
                  <template #icon><PlusOutlined /></template>
                  Add Year
                </a-button>
                <a-button
                  type="text"
                  danger
                  size="small"
                  @click="removeGroup(record.categoryKey, record.groupKey)"
                >
                  <template #icon><DeleteOutlined /></template>
                  Remove
                </a-button>
              </div>

              <!-- Year Actions -->
              <div v-else-if="record.type === 'year'" class="action-group">
                <a-button
                  type="text"
                  danger
                  size="small"
                  @click="removeYear(record.categoryKey, record.groupKey, record.year)"
                >
                  <template #icon><DeleteOutlined /></template>
                  Remove Year
                </a-button>
              </div>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- Custom Footer -->
    <template #footer>
      <div class="modal-footer">
        <div class="footer-left">
          <!-- Empty space for left alignment -->
        </div>

        <div class="footer-right">
          <a-button @click="handleCancel">Cancel</a-button>
          <a-button type="primary" :loading="saving" @click="handleSave">
            {{ isEditing ? 'Update Workout Charge' : 'Create Workout Charge' }}
          </a-button>
          <!-- Delete button only shown in edit mode, positioned after save button -->
          <a-popconfirm
            v-if="isEditing"
            title="Are you sure you want to delete this workout charge?"
            ok-text="Yes"
            cancel-text="No"
            @confirm="handleDelete"
          >
            <a-button type="primary" danger :loading="saving" class="delete-button">
              <template #icon>
                <DeleteOutlined />
              </template>
              Delete
            </a-button>
          </a-popconfirm>
        </div>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import type { TableColumnsType } from 'ant-design-vue'
import type {
  WorkoutCustomerCharge,
  WorkoutCustomerChargeFormData,
  CategoryOption,
  CategoryWorkoutOption,
} from '../../types/WorkoutCustomerCharge'
import {
  updateWorkoutCustomerCharge,
  deleteWorkoutCustomerCharge,
  getCategoriesForDropdown,
  getCategoryWorkoutsByCategoryId,
  getWorkoutCustomerChargeByCustomerDivision,
  createWorkoutCustomerCharge,
} from '../../services/master-data/workoutCustomerCharge/workoutCustomerChargeService'

// Table row interface
interface HierarchicalTableRow {
  key: string
  type: 'category' | 'group' | 'year' | 'month'
  name: string
  level: number
  categoryKey?: string
  categoryId?: string
  groupKey?: string
  groupId?: string
  unitPrice?: number
  year?: string
  month?: number
  monthKey?: string
  quantity?: number
}

// Props
interface Props {
  visible: boolean
  customerId: number
  divisionId: number
  customerName: string
  divisionName: string
  existingData?: WorkoutCustomerCharge | null
}

const props = defineProps<Props>()
const emit = defineEmits(['close', 'save'])

// Reactive data
const saving = ref(false)
const categories = ref<CategoryOption[]>([])
const categoryWorkouts = ref<CategoryWorkoutOption[]>([])
const existingWorkoutCharge = ref<WorkoutCustomerCharge | null>(null)
const expandedKeys = ref<string[]>([])
const quickFillValues = reactive<Record<string, number | undefined>>({})

const formData = reactive<WorkoutCustomerChargeFormData>({
  customer_id: props.customerId,
  division_id: props.divisionId,
  workout_customer_charge_content: {},
})

// Table columns
const columns: TableColumnsType = [
  {
    title: 'Category / Workout / Year',
    dataIndex: 'name',
    key: 'name',
    width: 600,
  },
  {
    title: 'Actions',
    key: 'actions',
    width: 250,
    align: 'center',
  },
]

// Computed
const isEditing = computed(() => !!existingWorkoutCharge.value)

const modalTitle = computed(() =>
  isEditing.value
    ? `Edit Workout Charge - ${props.customerName}`
    : `Create Workout Charge - ${props.customerName}`,
)

// Check if category has selected category_id
const canAddWorkout = (categoryKey: string): boolean => {
  const categoryData = formData.workout_customer_charge_content[categoryKey]
  return !!categoryData?.category_id
}

// Check if workout has selected group_id
const canAddYear = (categoryKey: string, workoutKey: string): boolean => {
  const workoutData = formData.workout_customer_charge_content[categoryKey]?.workouts?.[workoutKey]
  return !!workoutData?.group_id
}

// Check if there are available categories to add
const hasAvailableCategories = computed(() => {
  return getAvailableCategories.value.length > 0
})

// Check if there are available workouts for a specific category
const hasAvailableWorkouts = (categoryKey: string): boolean => {
  const categoryData = formData.workout_customer_charge_content[categoryKey]
  if (!categoryData?.category_id) {
    return false // No category selected, so no workouts available
  }

  const availableWorkouts = getAvailableWorkoutsForCategory(categoryData.category_id, categoryKey)
  return availableWorkouts.length > 0
}

const tableData = computed(() => {
  const data: HierarchicalTableRow[] = []

  Object.entries(formData.workout_customer_charge_content).forEach(
    ([categoryKey, categoryData]) => {
      // Add category row
      data.push({
        key: `category-${categoryKey}`,
        type: 'category',
        name: categoryData.category_name || 'Select Category',
        level: 0,
        categoryKey: categoryKey,
        categoryId: String(categoryData.category_id || ''),
      })

      // Add workout rows if category is expanded
      if (expandedKeys.value.includes(`category-${categoryKey}`)) {
        Object.entries(categoryData.workouts).forEach(([workoutKey, workoutData]) => {
          data.push({
            key: `group-${categoryKey}-${workoutKey}`,
            type: 'group',
            name: workoutData.group_name || 'Select Workout',
            level: 1,
            categoryKey: categoryKey,
            categoryId: String(categoryData.category_id || ''),
            groupKey: workoutKey,
            groupId: String(workoutData.group_id || ''),
            unitPrice: workoutData.unit_price,
          })

          // Add year rows if workout is expanded (with grid display)
          if (expandedKeys.value.includes(`group-${categoryKey}-${workoutKey}`)) {
            Object.entries(workoutData.years).forEach(([year, yearData]) => {
              data.push({
                key: `year-${categoryKey}-${workoutKey}-${year}`,
                type: 'year',
                name: `Year ${year}`,
                level: 2,
                categoryKey: categoryKey,
                groupKey: workoutKey,
                year: year,
              })

              // Note: Month data is now handled in the year grid, not as separate rows
            })
          }
        })
      }
    },
  )

  return data
})

const generateId = (): string => {
  return `${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
}

// Table methods
const onExpand = (expanded: boolean, record: HierarchicalTableRow) => {
  const key = record.key
  if (expanded) {
    if (!expandedKeys.value.includes(key)) {
      expandedKeys.value.push(key)
    }
  } else {
    const index = expandedKeys.value.indexOf(key)
    if (index > -1) {
      expandedKeys.value.splice(index, 1)
    }
  }
}

// Get available categories (exclude already selected ones)
const getAvailableCategories = computed(() => {
  const usedCategoryIds = new Set<string>()

  // Collect all currently selected category IDs
  Object.values(formData.workout_customer_charge_content).forEach((categoryData) => {
    if (categoryData.category_id) {
      usedCategoryIds.add(String(categoryData.category_id))
    }
  })

  // Filter out used categories
  const availableCategories = categories.value.filter(
    (category) => !usedCategoryIds.has(String(category.id)),
  )

  console.log(
    'Available categories:',
    availableCategories.length,
    'out of',
    categories.value.length,
  )
  console.log('Used category IDs:', Array.from(usedCategoryIds))

  return availableCategories
})

// Get available workouts for a category (exclude already selected ones in that category)
const getAvailableWorkoutsForCategory = (categoryId: string, currentCategoryKey: string) => {
  if (!categoryId) return []

  // Get all workouts for this category
  const allWorkouts = categoryWorkouts.value.filter(
    (group) => String(group.category_id) === String(categoryId),
  )

  // Get already used workout IDs in this category
  const usedWorkoutIds = new Set<string>()
  const categoryData = formData.workout_customer_charge_content[currentCategoryKey]

  if (categoryData?.workouts) {
    Object.values(categoryData.workouts).forEach((workoutData) => {
      if (workoutData.group_id) {
        usedWorkoutIds.add(String(workoutData.group_id))
      }
    })
  }

  // Filter out used workouts
  const availableWorkouts = allWorkouts.filter((workout) => !usedWorkoutIds.has(String(workout.id)))

  console.log(
    `Available workouts for category ${categoryId}:`,
    availableWorkouts.length,
    'out of',
    allWorkouts.length,
  )
  console.log('Used workout IDs in this category:', Array.from(usedWorkoutIds))

  return availableWorkouts
}

const getGroupsForCategory = (categoryId: string) => {
  const workouts = categoryWorkouts.value.filter(
    (group) => String(group.category_id) === String(categoryId),
  )
  console.log(`Getting workouts for category ${categoryId}:`, workouts.length, 'workouts found')
  return workouts
}

const updateCategoryData = async (record: HierarchicalTableRow) => {
  if (record.categoryKey && record.categoryId) {
    const category = categories.value.find((c) => String(c.id) === String(record.categoryId))

    if (category) {
      formData.workout_customer_charge_content[record.categoryKey].category_id = String(
        record.categoryId,
      )
      formData.workout_customer_charge_content[record.categoryKey].category_name = category.name

      // Load workouts for this category
      try {
        const workoutsData = await getCategoryWorkoutsByCategoryId(record.categoryId)
        categoryWorkouts.value = workoutsData

        // Clear any existing workout selections for this category since workouts changed
        const categoryData = formData.workout_customer_charge_content[record.categoryKey]
        Object.keys(categoryData.workouts || {}).forEach((workoutKey) => {
          categoryData.workouts[workoutKey].group_id = ''
          categoryData.workouts[workoutKey].group_name = ''
          categoryData.workouts[workoutKey].unit_price = 0
          categoryData.workouts[workoutKey].calculation_type = undefined

          // Clear all year data when category changes
          categoryData.workouts[workoutKey].years = {}
        })
      } catch (error) {
        console.error('Error loading category workouts:', error)
        categoryWorkouts.value = []
      }
    }
  }
}

const updateGroupData = (record: HierarchicalTableRow) => {
  if (record.categoryKey && record.groupKey && record.groupId) {
    const group = categoryWorkouts.value.find((g) => String(g.id) === String(record.groupId))

    if (group) {
      const workoutData =
        formData.workout_customer_charge_content[record.categoryKey].workouts[record.groupKey]
      const oldCalculationType = workoutData.calculation_type
      const newCalculationType = group.calculation_type || 'Manual'

      workoutData.group_id = String(record.groupId)
      workoutData.group_name = group.name
      workoutData.unit_price = group.unit_price
      workoutData.calculation_type = newCalculationType

      // If calculation type changed, clear all year data to avoid N/A conflicts
      if (oldCalculationType && oldCalculationType !== newCalculationType) {
        console.log(
          `Calculation type changed from ${oldCalculationType} to ${newCalculationType}, clearing year data`,
        )
        workoutData.years = {}
      }
    }
  }
}

// CRUD methods
const addCategory = () => {
  const categoryKey = generateId()
  formData.workout_customer_charge_content[categoryKey] = {
    category_id: '',
    category_name: '',
    workouts: {},
  }
  // Auto expand new category
  expandedKeys.value.push(`category-${categoryKey}`)
}

const removeCategory = (categoryKey: string) => {
  delete formData.workout_customer_charge_content[categoryKey]
  // Remove from expanded keys
  const keysToRemove = expandedKeys.value.filter((key) => key.includes(categoryKey))
  keysToRemove.forEach((key) => {
    const index = expandedKeys.value.indexOf(key)
    if (index > -1) {
      expandedKeys.value.splice(index, 1)
    }
  })
}

const addGroup = (categoryKey: string) => {
  const groupKey = generateId()
  if (!formData.workout_customer_charge_content[categoryKey].workouts) {
    formData.workout_customer_charge_content[categoryKey].workouts = {}
  }
  formData.workout_customer_charge_content[categoryKey].workouts[groupKey] = {
    group_id: '',
    group_name: '',
    unit_price: 0,
    years: {},
  }
  // Auto expand new group
  expandedKeys.value.push(`group-${categoryKey}-${groupKey}`)
}

const removeGroup = (categoryKey: string, groupKey: string) => {
  delete formData.workout_customer_charge_content[categoryKey].workouts[groupKey]
  // Remove from expanded keys
  const keysToRemove = expandedKeys.value.filter((key) =>
    key.includes(`${categoryKey}-${groupKey}`),
  )
  keysToRemove.forEach((key) => {
    const index = expandedKeys.value.indexOf(key)
    if (index > -1) {
      expandedKeys.value.splice(index, 1)
    }
  })
}

const addYear = (categoryKey: string, groupKey: string) => {
  const currentYear = new Date().getFullYear()
  let newYear = currentYear.toString()

  // Find next available year
  while (formData.workout_customer_charge_content[categoryKey].workouts[groupKey].years[newYear]) {
    newYear = (parseInt(newYear) + 1).toString()
  }

  formData.workout_customer_charge_content[categoryKey].workouts[groupKey].years[newYear] = {}

  quickFillValues[categoryKey + '-' + groupKey + '-' + newYear] = undefined
  // Auto expand new year
  expandedKeys.value.push(`year-${categoryKey}-${groupKey}-${newYear}`)
}

const removeYear = (categoryKey: string, groupKey: string, year: string) => {
  delete formData.workout_customer_charge_content[categoryKey].workouts[groupKey].years[year]
  // Remove from expanded keys
  const keysToRemove = expandedKeys.value.filter((key) =>
    key.includes(`${categoryKey}-${groupKey}-${year}`),
  )
  keysToRemove.forEach((key) => {
    const index = expandedKeys.value.indexOf(key)
    if (index > -1) {
      expandedKeys.value.splice(index, 1)
    }
  })
}

// Helper functions for grid layout
const getMonthQuantity = (
  categoryKey: string,
  workoutKey: string,
  year: string,
  month: number,
): number => {
  const monthKey = `month_${month}`
  const yearData =
    formData.workout_customer_charge_content[categoryKey]?.workouts[workoutKey]?.years[year]

  // Get calculation type for this workout
  const workoutData = formData.workout_customer_charge_content[categoryKey]?.workouts?.[workoutKey]
  const calcType = workoutData?.calculation_type || 'Manual'

  // For Auto/Link types, return 0 (but template will show N/A based on calculation_type)
  // For Manual type, return actual quantity or 0
  const quantity = yearData?.[monthKey]?.quantity

  if (calcType === 'Auto' || calcType === 'Link') {
    // For Auto/Link, return 0 but template will show N/A
    return 0
  } else {
    // For Manual, return actual quantity or 0
    return quantity || 0
  }
}

const updateMonthQuantity = (
  categoryKey: string,
  workoutKey: string,
  year: string,
  month: number,
  quantity: number,
) => {
  // Check calculation type - only allow updates for Manual type
  const calcType = getWorkoutCalculationType(categoryKey, workoutKey)
  if (calcType === 'Auto' || calcType === 'Link') {
    console.log(`Blocked update for ${calcType} calculation type`)
    return
  }

  const monthKey = `month_${month}`
  if (!formData.workout_customer_charge_content[categoryKey].workouts[workoutKey].years[year]) {
    formData.workout_customer_charge_content[categoryKey].workouts[workoutKey].years[year] = {}
  }
  if (
    !formData.workout_customer_charge_content[categoryKey].workouts[workoutKey].years[year][
      monthKey
    ]
  ) {
    formData.workout_customer_charge_content[categoryKey].workouts[workoutKey].years[year][
      monthKey
    ] = {
      month: month,
      quantity: 0,
    }
  }
  formData.workout_customer_charge_content[categoryKey].workouts[workoutKey].years[year][
    monthKey
  ].quantity = quantity || 0
}

const fillAllMonths = (categoryKey: string, groupKey: string, year: string) => {
  // Use stored calculation_type for this specific workout data
  const workoutData = formData.workout_customer_charge_content[categoryKey]?.workouts?.[groupKey]
  const calcType = workoutData?.calculation_type || 'Manual'
  if (calcType === 'Auto' || calcType === 'Link') return
  const key = categoryKey + '-' + groupKey + '-' + year
  const value = quickFillValues[key]
  if (value === undefined || value === null) return
  for (let month = 1; month <= 12; month++) {
    updateMonthQuantity(categoryKey, groupKey, year, month, value)
  }
}

// Helper to get calculationType for a workout
const getWorkoutCalculationType = (categoryKey: string, groupKey: string): string | undefined => {
  const groupId =
    formData.workout_customer_charge_content[categoryKey]?.workouts?.[groupKey]?.group_id
  if (!groupId) return undefined
  const workout = categoryWorkouts.value.find((w) => String(w.id) === String(groupId))
  console.log('workout?.calculation_type: ', workout?.calculation_type)
  return workout?.calculation_type
}

// Helper to get calculation type for a specific row (stored in form data)
const getRowCalculationType = (record: HierarchicalTableRow): string | undefined => {
  if (!record.categoryKey || !record.groupKey) return undefined

  // Get calculation_type stored in the workout data for this specific row
  const workoutData =
    formData.workout_customer_charge_content[record.categoryKey]?.workouts?.[record.groupKey]
  const storedCalculationType = workoutData?.calculation_type

  console.log(
    `Row calculation type for ${record.categoryKey}-${record.groupKey}:`,
    storedCalculationType,
  )
  return storedCalculationType || 'Manual' // Default to Manual if not set
}

// Methods
const loadDropdownData = async () => {
  try {
    const categoriesData = await getCategoriesForDropdown()
    categories.value = categoriesData
    console.log('Loaded categories for dropdown:', categoriesData.length, 'categories')
  } catch (error) {
    console.error('Error loading dropdown data:', error)
    categories.value = []
  }
}

const setAutoLinkMonthsToNull = () => {
  console.log('Setting Auto/Link months to null before save...')
  Object.entries(formData.workout_customer_charge_content).forEach(
    ([categoryKey, categoryData]) => {
      Object.entries(categoryData.workouts).forEach(([groupKey, groupData]) => {
        // Use stored calculation_type for this specific workout data
        const calcType = groupData.calculation_type || 'Manual'
        console.log(
          `Processing ${categoryKey}-${groupKey} with stored calculation type: ${calcType}`,
        )

        if (calcType === 'Auto' || calcType === 'Link') {
          console.log(`Setting null values for Auto/Link type: ${calcType}`)
          Object.entries(groupData.years).forEach(([year, yearData]) => {
            for (let m = 1; m <= 12; m++) {
              const monthKey = `month_${m}`
              if (yearData[monthKey]) {
                console.log(`Clearing ${monthKey} for ${calcType} type`)
                yearData[monthKey] = { month: m, quantity: null }
              }
            }
          })
        } else if (calcType === 'Manual') {
          console.log(`Keeping Manual type data as-is for: ${categoryKey}-${groupKey}`)
          // Manual type: keep user input as-is, but ensure structure is correct
          Object.entries(groupData.years).forEach(([year, yearData]) => {
            for (let m = 1; m <= 12; m++) {
              const monthKey = `month_${m}`
              if (yearData[monthKey] && typeof yearData[monthKey].quantity === 'undefined') {
                // If quantity is undefined, set to 0 for Manual type
                yearData[monthKey].quantity = 0
              }
            }
          })
        }
      })
    },
  )
}

const handleSave = async () => {
  try {
    saving.value = true
    setAutoLinkMonthsToNull()

    console.log('=== SAVE PROCESS STARTED ===')
    console.log('Form data before save:', JSON.stringify(formData, null, 2))
    console.log('Customer ID:', formData.customer_id)
    console.log('Division ID:', formData.division_id)
    console.log('Content keys:', Object.keys(formData.workout_customer_charge_content || {}))
    console.log('Is editing:', isEditing.value)
    console.log('Existing workout charge:', existingWorkoutCharge.value?.id)

    let savedCharge: WorkoutCustomerCharge

    if (isEditing.value && existingWorkoutCharge.value) {
      console.log('Updating existing workout charge...')
      savedCharge = await updateWorkoutCustomerCharge(existingWorkoutCharge.value.id, formData)
      message.success('Workout charge updated successfully')

      // For update: Keep modal open and emit save event
      emit('save', savedCharge)
    } else {
      console.log('Creating new workout charge...')
      savedCharge = await createWorkoutCustomerCharge(formData)
      message.success('Workout charge created successfully')

      // For create: Navigate to edit mode
      console.log('Navigating to edit mode with created data:', savedCharge)
      existingWorkoutCharge.value = savedCharge

      // Update form data with the saved data to ensure consistency
      formData.workout_customer_charge_content = {
        ...savedCharge.workout_customer_charge_content,
      }

      // Load category workouts for all categories in the created data
      console.log('Loading category workouts for created data...')
      const allCategoryIds = new Set<string>()
      Object.values(savedCharge.workout_customer_charge_content).forEach((categoryData) => {
        if (categoryData.category_id) {
          allCategoryIds.add(categoryData.category_id)
        }
      })

      // Load workouts for all categories
      for (const categoryId of allCategoryIds) {
        try {
          console.log('Loading workouts for category:', categoryId)
          const workoutsData = await getCategoryWorkoutsByCategoryId(categoryId)
          // Merge with existing categoryWorkouts
          categoryWorkouts.value = [
            ...categoryWorkouts.value.filter((w) => w.category_id !== categoryId),
            ...workoutsData,
          ]
          console.log('Loaded workouts for category', categoryId, ':', workoutsData)
        } catch (error) {
          console.error('Error loading workouts for category', categoryId, ':', error)
        }
      }

      // Ensure calculation_type is preserved from loaded workout data for created data
      console.log('Preserving calculation_type from loaded workouts for created data...')
      Object.entries(formData.workout_customer_charge_content).forEach(
        ([categoryKey, categoryData]) => {
          Object.entries(categoryData.workouts || {}).forEach(([workoutKey, workoutData]) => {
            if (workoutData.group_id) {
              const workout = categoryWorkouts.value.find(
                (w) => String(w.id) === String(workoutData.group_id),
              )
              if (workout && workout.calculation_type) {
                workoutData.calculation_type = workout.calculation_type
                console.log(
                  `Set calculation_type for created data ${categoryKey}-${workoutKey}: ${workout.calculation_type}`,
                )
              }
            }
          })
        },
      )

      // Auto-expand all levels to show created data
      const keysToExpand: string[] = []
      Object.entries(savedCharge.workout_customer_charge_content).forEach(
        ([categoryKey, categoryData]) => {
          keysToExpand.push(`category-${categoryKey}`)

          Object.entries(categoryData.workouts || {}).forEach(([workoutKey, workoutData]) => {
            keysToExpand.push(`group-${categoryKey}-${workoutKey}`)

            Object.keys(workoutData.years || {}).forEach((year) => {
              keysToExpand.push(`year-${categoryKey}-${workoutKey}-${year}`)
            })
          })
        },
      )

      expandedKeys.value = keysToExpand
      console.log('Auto-expanded keys for created data:', keysToExpand)
      console.log('Final category workouts loaded:', categoryWorkouts.value)

      // Emit save event to parent to refresh list but don't close modal
      emit('save', savedCharge)

      message.info('Now in edit mode. You can continue editing or close the modal.')
    }

    console.log('Save completed, result:', savedCharge)
    console.log('=== SAVE PROCESS COMPLETED ===')
  } catch (error) {
    console.error('Save error:', error)
    message.error(`Failed to save workout charge: ${error.message || error}`)
  } finally {
    saving.value = false
  }
}

const handleCancel = () => {
  emit('close')
  resetForm()
}

// Handle delete workout charge from modal
const handleDelete = async () => {
  if (!existingWorkoutCharge.value) {
    message.error('No workout charge to delete')
    return
  }

  try {
    saving.value = true
    console.log('Deleting workout charge from modal:', existingWorkoutCharge.value.id)

    // Import delete function from service
    const { deleteWorkoutCustomerChargeById } = await import(
      '../../services/master-data/workoutCustomerCharge/workoutCustomerChargeService'
    )
    await deleteWorkoutCustomerChargeById(existingWorkoutCharge.value.id)

    message.success('Workout charge deleted successfully')
    emit('save', null) // Emit null to indicate deletion
    emit('close')
  } catch (error) {
    console.error('Error deleting workout charge from modal:', error)
    message.error(`Failed to delete workout charge: ${error.message}`)
  } finally {
    saving.value = false
  }
}

const createAnother = () => {
  // Clear form to create another workout charge
  formData.workout_customer_charge_content = {}
  expandedKeys.value = []
  existingWorkoutCharge.value = null
  message.info('Ready to create another workout charge')
}

const resetForm = () => {
  formData.customer_id = props.customerId
  formData.division_id = props.divisionId
  formData.workout_customer_charge_content = {}
  categoryWorkouts.value = []
  expandedKeys.value = []
}

// Watch for props changes
watch(
  () => props.visible,
  async (newVisible) => {
    console.log('=== MODAL VISIBILITY CHANGED ===')
    console.log('New visible state:', newVisible)
    console.log('Customer ID:', props.customerId)
    console.log('Division ID:', props.divisionId)
    console.log('Existing data:', props.existingData)

    if (newVisible) {
      console.log('Modal is opening, initializing...')

      // Don't reinitialize database - it drops all existing data!
      console.log('Skipping database reinitialization to preserve existing data')

      await loadDropdownData()
      console.log('Categories loaded:', categories.value.length)

      // Check if existing data is passed from parent (for edit mode)
      if (props.existingData) {
        console.log('Loading existing data from props:', props.existingData)
        existingWorkoutCharge.value = props.existingData
        formData.workout_customer_charge_content = {
          ...props.existingData.workout_customer_charge_content,
        }

        // Load category workouts for all categories in existing data
        console.log('Loading category workouts for existing categories...')
        const allCategoryIds = new Set<string>()
        Object.values(props.existingData.workout_customer_charge_content).forEach(
          (categoryData) => {
            if (categoryData.category_id) {
              allCategoryIds.add(categoryData.category_id)
            }
          },
        )

        // Load workouts for all categories
        for (const categoryId of allCategoryIds) {
          try {
            console.log('Loading workouts for category:', categoryId)
            const workoutsData = await getCategoryWorkoutsByCategoryId(categoryId)
            // Merge with existing categoryWorkouts
            categoryWorkouts.value = [
              ...categoryWorkouts.value.filter((w) => w.category_id !== categoryId),
              ...workoutsData,
            ]
            console.log('Loaded workouts for category', categoryId, ':', workoutsData)
          } catch (error) {
            console.error('Error loading workouts for category', categoryId, ':', error)
          }
        }

        // Ensure calculation_type is preserved from loaded workout data
        console.log('Preserving calculation_type from loaded workouts...')
        Object.entries(formData.workout_customer_charge_content).forEach(
          ([categoryKey, categoryData]) => {
            Object.entries(categoryData.workouts || {}).forEach(([workoutKey, workoutData]) => {
              if (workoutData.group_id) {
                const workout = categoryWorkouts.value.find(
                  (w) => String(w.id) === String(workoutData.group_id),
                )
                if (workout && workout.calculation_type) {
                  workoutData.calculation_type = workout.calculation_type
                  console.log(
                    `Set calculation_type for ${categoryKey}-${workoutKey}: ${workout.calculation_type}`,
                  )
                }
              }
            })
          },
        )

        // Auto-expand all levels to show existing data
        const keysToExpand: string[] = []
        Object.entries(props.existingData.workout_customer_charge_content).forEach(
          ([categoryKey, categoryData]) => {
            keysToExpand.push(`category-${categoryKey}`)

            Object.entries(categoryData.workouts || {}).forEach(([workoutKey, workoutData]) => {
              keysToExpand.push(`group-${categoryKey}-${workoutKey}`)

              Object.keys(workoutData.years || {}).forEach((year) => {
                keysToExpand.push(`year-${categoryKey}-${workoutKey}-${year}`)
              })
            })
          },
        )

        expandedKeys.value = keysToExpand
        console.log('Auto-expanded keys for existing data:', keysToExpand)
        console.log('Final category workouts loaded:', categoryWorkouts.value)
      } else {
        // Try to load existing workout charge for this customer/division
        console.log('=== LOADING EXISTING DATA ===')
        console.log('Customer ID:', props.customerId)
        console.log('Division ID:', props.divisionId)

        try {
          const existing = await getWorkoutCustomerChargeByCustomerDivision(
            props.customerId,
            props.divisionId,
          )
          console.log('Load result:', existing)
          existingWorkoutCharge.value = existing

          if (existing) {
            console.log('Found existing data, loading into form...')
            // Load existing data for editing
            formData.workout_customer_charge_content = {
              ...existing.workout_customer_charge_content,
            }

            // Load category workouts for all categories in existing data
            console.log('Loading category workouts for existing categories...')
            const allCategoryIds = new Set<string>()
            Object.values(existing.workout_customer_charge_content).forEach((categoryData) => {
              if (categoryData.category_id) {
                allCategoryIds.add(categoryData.category_id)
              }
            })

            // Load workouts for all categories
            for (const categoryId of allCategoryIds) {
              try {
                console.log('Loading workouts for category:', categoryId)
                const workoutsData = await getCategoryWorkoutsByCategoryId(categoryId)
                // Merge with existing categoryWorkouts
                categoryWorkouts.value = [
                  ...categoryWorkouts.value.filter((w) => w.category_id !== categoryId),
                  ...workoutsData,
                ]
                console.log('Loaded workouts for category', categoryId, ':', workoutsData)
              } catch (error) {
                console.error('Error loading workouts for category', categoryId, ':', error)
              }
            }

            // Ensure calculation_type is preserved from loaded workout data
            console.log('Preserving calculation_type from loaded workouts...')
            Object.entries(formData.workout_customer_charge_content).forEach(
              ([categoryKey, categoryData]) => {
                Object.entries(categoryData.workouts || {}).forEach(([workoutKey, workoutData]) => {
                  if (workoutData.group_id) {
                    const workout = categoryWorkouts.value.find(
                      (w) => String(w.id) === String(workoutData.group_id),
                    )
                    if (workout && workout.calculation_type) {
                      workoutData.calculation_type = workout.calculation_type
                      console.log(
                        `Set calculation_type for ${categoryKey}-${workoutKey}: ${workout.calculation_type}`,
                      )
                    }
                  }
                })
              },
            )

            // Auto-expand all levels to show existing data
            const keysToExpand: string[] = []
            Object.entries(existing.workout_customer_charge_content).forEach(
              ([categoryKey, categoryData]) => {
                keysToExpand.push(`category-${categoryKey}`)

                Object.entries(categoryData.workouts || {}).forEach(([workoutKey, workoutData]) => {
                  keysToExpand.push(`group-${categoryKey}-${workoutKey}`)

                  Object.keys(workoutData.years || {}).forEach((year) => {
                    keysToExpand.push(`year-${categoryKey}-${workoutKey}-${year}`)
                  })
                })
              },
            )

            expandedKeys.value = keysToExpand
            console.log('Auto-expanded keys for existing data:', keysToExpand)
            console.log('Form data loaded:', formData.workout_customer_charge_content)
            console.log('Category workouts loaded:', categoryWorkouts.value)
          } else {
            console.log('No existing data found, resetting form')
            resetForm()
          }
        } catch (error) {
          console.error('Error loading existing workout charge:', error)
          existingWorkoutCharge.value = null
          resetForm()
        }
      }
    }
  },
)

// Initialize
onMounted(() => {
  formData.customer_id = props.customerId
  formData.division_id = props.divisionId
})
</script>

<script lang="ts">
export default {
  name: 'WorkoutChargeModalHierarchical',
}
</script>

<style scoped>
.workout-charge-modal {
  display: flex;
  flex-direction: column;
  height: 70vh;
}

.customer-info {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 10px;
  flex-shrink: 0; /* Don't shrink */
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item label {
  font-weight: 600;
  color: #666;
  min-width: 80px;
}

.info-value {
  font-weight: 500;
  color: #1890ff;
}

/* Fixed Action Buttons */
.action-buttons-fixed {
  margin-bottom: 16px;
  text-align: left;
  padding: 16px 20px;
  background: #fafafa;
  border: 1px solid #d9d9d9;
  border-bottom: none;
  border-radius: 6px 6px 0 0;
  flex-shrink: 0; /* Don't shrink */
}

/* Scrollable Table Container */
.table-container {
  flex: 1;
  overflow: hidden;
  border: 1px solid #d9d9d9;
  border-radius: 0 0 6px 6px;
  background: #fafafa;
}

/* Remove old styles */
.hierarchical-content {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 20px;
  background: #fafafa;
}

.action-buttons {
  margin-bottom: 16px;
  text-align: left;
}

.name-cell {
  display: flex;
  align-items: center;
}

.year-label {
  font-weight: 600;
  color: #1890ff;
  font-size: 14px;
}

.action-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.success-section {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.existing-data-section {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.existing-data-info {
  margin-top: 16px;
}

.info-card {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 12px;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.info-card label {
  font-weight: 600;
  color: #666;
  margin-bottom: 8px;
  font-size: 12px;
}

.info-card span {
  color: #1890ff;
  font-weight: 500;
  font-size: 11px;
  word-break: break-all;
}

.created-data-display {
  margin-top: 16px;
}

.created-data-display h4 {
  margin-bottom: 12px;
  color: #52c41a;
  font-weight: 600;
}

.data-preview {
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  line-height: 1.4;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: #333;
  font-family: 'Courier New', monospace;
  margin-bottom: 16px;
}

.success-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.created-data-grid {
  margin-bottom: 16px;
}

.created-data-grid .ant-table {
  border: 1px solid #e8e8e8;
}

.quantity-cell {
  font-weight: 600;
  color: #52c41a;
}

:deep(.ant-modal-body) {
  padding: 24px;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 8px 12px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-row-expand-icon) {
  color: #1890ff;
}

/* Grid Layout Styles */
.year-month-grid {
  padding: 16px;
}

.category-section {
  margin-bottom: 24px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.category-header h3 {
  margin: 0;
  color: #1890ff;
  font-weight: 600;
}

.workout-section {
  margin-bottom: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 12px;
}

.workout-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.workout-header h4 {
  margin: 0;
  color: #52c41a;
  font-weight: 500;
}

.year-month-table {
  margin-bottom: 12px;
}

.grid-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #e8e8e8;
  table-layout: fixed; /* Force table to fit container */
}

.grid-table th,
.grid-table td {
  border: 1px solid #e8e8e8;
  padding: 4px 6px;
  text-align: center;
  vertical-align: middle;
  overflow: hidden;
}

.grid-table th {
  background: #fafafa;
  font-weight: 600;
  font-size: 11px;
  white-space: nowrap;
}

.year-cell {
  background: #f0f9ff;
  font-weight: 600;
  color: #1890ff;
  width: 8%; /* Fixed width percentage */
  font-size: 11px;
}

.month-cell {
  width: 7.67%; /* 92% / 12 months = ~7.67% each */
}

.actions-cell {
  min-width: 100px;
}

.year-actions,
.workout-actions,
.category-actions {
  margin-top: 12px;
  text-align: center;
}

.unit-price {
  color: #fa8c16;
  font-weight: 600;
  font-size: 14px;
  margin-left: 8px;
}

.month-cell .ant-input-number {
  border: none;
  box-shadow: none;
  width: 100%;
}

.month-cell .ant-input-number-input {
  text-align: center;
  font-size: 11px;
  padding: 2px 4px;
}

.year-month-table {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  /* Remove overflow-x: auto to prevent horizontal scroll */
}

.workout-actions {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

/* Year Grid Container */
.year-grid-container {
  width: 100%;
}

.year-grid-container .year-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #1890ff;
  font-size: 14px;
}

.year-grid-container .year-month-table {
  margin-top: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  /* Remove overflow-x: auto to prevent horizontal scroll */
}

.year-grid-container .grid-table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
  table-layout: fixed; /* Force table to fit container */
}

.year-grid-container .grid-table th,
.year-grid-container .grid-table td {
  border: 1px solid #e8e8e8;
  padding: 4px 6px;
  text-align: center;
  vertical-align: middle;
}

.year-grid-container .grid-table th {
  background: #fafafa;
  font-weight: 600;
  font-size: 11px;
  color: #666;
  white-space: nowrap;
}

.year-grid-container .year-cell {
  background: #f0f9ff;
  font-weight: 600;
  color: #1890ff;
  width: 8%; /* Fixed width percentage */
  font-size: 11px;
}

.year-grid-container .month-cell {
  width: 7.67%; /* 92% / 12 months = ~7.67% each */
  padding: 2px;
}

.year-grid-container .month-cell .ant-input-number {
  border: none;
  box-shadow: none;
  width: 100%;
}

.year-grid-container .month-cell .ant-input-number-input {
  text-align: center;
  font-size: 11px;
  padding: 2px 4px;
}

/* Action buttons and no options text */
.action-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.no-options-text {
  color: #999;
  font-size: 12px;
  font-style: italic;
  margin-left: 8px;
}

.action-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-group .no-options-text {
  margin-left: 4px;
  margin-right: 8px;
}

/* Manual calculation type styling */
.manual-na-text {
  color: #aaa;
  font-style: italic;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* Modal Footer Styles */
.modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
}

.footer-left {
  flex: 1;
}

.footer-right {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* Delete button spacing */
.delete-button {
  margin-left: 16px !important;
}
</style>
