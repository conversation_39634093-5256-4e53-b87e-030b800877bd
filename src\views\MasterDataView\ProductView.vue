<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Modal,
  message,
  But<PERSON>,
  PageHeader,
  Select,
  Row as ARow,
  Col as ACol,
  Table as ATable,
  Alert,
  // Select, // Removed duplicate import
} from 'ant-design-vue'

import ActionButtons from '@IVC/components/ActionButtons.vue'
import type { Customer } from '@IVC/types/MasterDataTypes/Customer'
import type { ImportedProduct } from '@IVC/types/MasterDataTypes/Product'

import { getAllCustomers } from '@IVC/services/master-data/customer'
import {
  deleteImportedProduct,
  getAllImportedProducts,
  getPreviousVersionProduct,
} from '@IVC/services/master-data/product'
import type { SelectValue } from 'ant-design-vue/lib/select'
import { ImportOutlined } from '@ant-design/icons-vue'

const router = useRouter()
const localLoading = ref(false)
const customers = ref<Customer[]>([])
const selectedCustomerId = ref<number | undefined>(undefined)
const allProducts = ref<ImportedProduct[]>([]) // Store all products
const productsForSelectedCustomer = ref<ImportedProduct[]>([]) // Products for the selected customer

const productComparisonData = ref<{
  current: ImportedProduct
  previous: ImportedProduct | null
  differences: string[]
} | null>(null)
const isComparisonModalVisible = ref(false)

const productColumns = [
  {
    title: 'Version ID',
    dataIndex: 'versionId',
    key: 'version_id',
    sorter: (a: ImportedProduct, b: ImportedProduct) => a.versionId - b.versionId,
  },
  {
    title: 'Product Code',
    dataIndex: 'productCode',
    key: 'product_code',
    sorter: (a: ImportedProduct, b: ImportedProduct) => a.productCode.localeCompare(b.productCode),
  },
  {
    title: 'Product Name',
    dataIndex: 'productName',
    key: 'product_name',
    width: 250,
    ellipsis: { showTitle: true },
  },
  { title: 'Pieces Per Carton', dataIndex: 'productPpcNum', key: 'ppc_num', width: 180 },
  { title: 'Packaging Hierarchy', dataIndex: 'productHrc1', key: 'hrc1', width: 200 },
  { title: 'Product Category', dataIndex: 'productHrc3', key: 'hrc3', width: 180 },
  { title: 'Piece Cubic Meters', dataIndex: 'productPcm3', key: 'pcm3', width: 200 },
  { title: 'Imported At', dataIndex: 'importedAt', key: 'imported_at' },
  { title: 'Action', key: 'action', width: 150 },
]

const fetchInitialData = async () => {
  localLoading.value = true
  try {
    // Fetch customers for the select dropdown
    customers.value = await getAllCustomers()
    // Fetch all products initially
    allProducts.value = await getAllImportedProducts()
    // If a customer was previously selected (e.g. page refresh with query params), filter products
    filterProductsForSelectedCustomer()
  } catch (err) {
    message.error(`Failed to load initial data: ${(err as Error).message}`)
    allProducts.value = []
    productsForSelectedCustomer.value = []
  } finally {
    localLoading.value = false
  }
}

onMounted(async () => {
  await fetchInitialData()
  // Check for customerId in query params
  const queryCustomerId = router.currentRoute.value.query.customerId
  if (queryCustomerId && !selectedCustomerId.value) {
    const customerIdNum = Number(queryCustomerId)
    if (customers.value.some((c) => c.id === customerIdNum)) {
      selectedCustomerId.value = customerIdNum
      filterProductsForSelectedCustomer()
    }
  }
})

const filterProductsForSelectedCustomer = () => {
  if (selectedCustomerId.value) {
    productsForSelectedCustomer.value = allProducts.value
      .filter((p) => p.customerId === selectedCustomerId.value)
      .sort((a, b) => {
        // Optional: sort products
        const dateDiff = new Date(b.importedAt).getTime() - new Date(a.importedAt).getTime()
        if (dateDiff !== 0) return dateDiff
        return a.productCode.localeCompare(b.productCode)
      })
  } else {
    productsForSelectedCustomer.value = [] // Clear if no customer is selected
  }
}

const handleCustomerSelectionChange = (value: SelectValue) => {
  selectedCustomerId.value = value as number | undefined
  localLoading.value = true // Indicate loading while filtering
  filterProductsForSelectedCustomer()
  localLoading.value = false
}

const compareWithPreviousVersion = async (product: ImportedProduct) => {
  let previousProduct: ImportedProduct | null = null
  try {
    // Ensure we have the required fields
    if (!product.customerId) {
      message.error('Product must have a customer ID to compare versions')
      return
    }

    // Get the previous version of this product
    previousProduct = await getPreviousVersionProduct(
      product.productCode,
      product.customerId,
      product.versionId,
    )

    if (previousProduct) {
      const differences: string[] = []
      const fieldsToCompare: { key: keyof ImportedProduct; label: string }[] = [
        { key: 'productName', label: 'Product Name' },
        { key: 'productPpcNum', label: 'Pieces Per Carton' },
        { key: 'productHrc1', label: 'Packaging Hierarchy' },
        { key: 'productHrc3', label: 'Product Category' },
        { key: 'productPcm3', label: 'Piece Cubic Meters' },
      ]

      fieldsToCompare.forEach((field) => {
        const currentVal = product[field.key] ?? ''
        const prevVal = previousProduct![field.key] ?? '' // previousProduct is checked
        if (currentVal !== prevVal) {
          differences.push(`${field.label}: "${prevVal}" -> "${currentVal}"`)
        }
      })

      if (differences.length > 0) {
        productComparisonData.value = { current: product, previous: previousProduct, differences }
        isComparisonModalVisible.value = true
        return // Stop here, show modal
      }
    }

    // If no previous version or no differences
    productComparisonData.value = { current: product, previous: previousProduct, differences: [] }
    isComparisonModalVisible.value = true
  } catch (err) {
    message.error(`Error comparing versions: ${(err as Error).message}`)
  }
}

const handleUpdateProduct = (product: ImportedProduct) => {
  // Open a modal for editing the product
  console.log('Update product:', product)
  message.info('Update functionality to be implemented.')
}

const confirmDeleteProduct = (productId: number) => {
  Modal.confirm({
    title: 'Delete Product',
    content: 'Are you sure you want to delete this imported product record?',
    okText: 'Delete',
    okType: 'danger',
    async onOk() {
      try {
        // No need for localLoading.value = true; here, fetchAndGroupAllProducts has its own
        await deleteImportedProduct(productId)
        message.success('Product record deleted.')
        await fetchInitialData() // Refetch all data to update the list
      } catch (err) {
        message.error(`Failed to delete product: ${(err as Error).message}`)
      }
    },
  })
}

const goToImportPage = () => {
  const query: { customerId?: number } = {}
  if (selectedCustomerId.value) {
    query.customerId = selectedCustomerId.value
  }
  router.push({ path: '/master-data/product/import', query })
}
</script>

<template>
  <div class="header-container-master">
    <PageHeader title="Product List" sub-title="View and manage imported product data">
      <template #extra>
        <Button type="primary" @click="goToImportPage"> <ImportOutlined /> Import Products </Button>
      </template>
    </PageHeader>

    <div>
      <ARow :gutter="[16, 16]" style="margin-bottom: 16px">
        <ACol :span="8">
          <Select
            v-model:value="selectedCustomerId"
            placeholder="Select Customer to View Products"
            style="width: 100%"
            @change="handleCustomerSelectionChange"
            show-search
            option-filter-prop="label"
            :loading="customers.length === 0 && localLoading"
            allow-clear
          >
            <Select.Option
              v-for="customer in customers"
              :key="customer.id"
              :value="customer.id"
              :label="customer.name"
            >
              {{ customer.name }} ({{ customer.code }})
            </Select.Option>
          </Select>
        </ACol>
      </ARow>

      <ATable
        :columns="productColumns"
        :data-source="productsForSelectedCustomer"
        :loading="localLoading"
        row-key="id"
        :pagination="{ pageSize: 10, size: 'small' }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <ActionButtons
              name="Product"
              :show-edit="true"
              :show-delete="true"
              :show-save="false"
              custom1-icon="DiffOutlined"
              custom1-tooltip="Compare with Previous"
              @edit="handleUpdateProduct(record as ImportedProduct)"
              @delete="confirmDeleteProduct((record as ImportedProduct).id)"
              @custom1="compareWithPreviousVersion(record as ImportedProduct)"
            />
          </template>
        </template>
      </ATable>
    </div>

    <!-- Comparison Modal -->
    <Modal
      v-model:visible="isComparisonModalVisible"
      title="Product Version Comparison"
      width="700px"
      @ok="isComparisonModalVisible = false"
      :footer="null"
    >
      <div v-if="productComparisonData">
        <h4>Comparing Product: {{ productComparisonData.current.productCode }}</h4>
        <p><strong>Current Version ID:</strong> {{ productComparisonData.current.versionId }}</p>
        <p v-if="productComparisonData.previous">
          <strong>Previous Version ID:</strong> {{ productComparisonData.previous.versionId }}
        </p>
        <p v-else>No comparable previous version found for this product.</p>

        <Alert
          v-if="productComparisonData.differences.length > 0"
          type="warning"
          style="margin-bottom: 16px"
        >
          <template #message>Differences Found:</template>
          <template #description>
            <ul>
              <li v-for="(diff, index) in productComparisonData.differences" :key="index">
                {{ diff }}
              </li>
            </ul>
          </template>
        </Alert>
        <Alert v-else type="info" style="margin-bottom: 16px">
          <template #message>No Differences Found</template>
          <template #description>
            The current version is identical to the previous one or no previous version to compare.
          </template>
        </Alert>

        <!-- You can add a more detailed side-by-side comparison table here if needed -->
      </div>
    </Modal>
  </div>
</template>

<style scoped>
/* Add any specific styles for ProductView (Product List) if needed */
</style>
