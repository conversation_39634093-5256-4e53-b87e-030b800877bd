import { useSQLite } from '@IVC/hooks/useSQLite'

export type StockOperationSource = 'Manual' | 'Inbound' | 'Outbound'

export interface Stock {
  id: number
  productCode: string
  customerId: number
  avrRtpcQty: number // Total Pieces in Stock
  avrRtpcQtyTemp: number // Temporary quantity field
  operationSource: StockOperationSource
  location: string | null
  isActive: boolean
  createdAt: string
  updatedAt: string
  // Virtual fields from joined tables
  customerName?: string // From customer table
  productName?: string // From imported_products table
  productPpcNum?: string | null
  productHrc1?: string | null
  productHrc3?: string | null
  productPcm3?: string | null
  // Previous version data
  productPpcNumPrevious?: string | null
  // Calculated fields
  numOfCartonsCurrent?: number
  numOfCartonsPrevious?: number
  numOfPiecesCurrent?: number
  numOfPiecesPrevious?: number
}

export interface StockFilters {
  customerId?: number
  productCode?: string
  productName?: string
  location?: string
}

const { executeQuery, tables } = useSQLite()

export async function getAllStocks(): Promise<Stock[]> {
  const query = `
    SELECT DISTINCT
      s.id, s.product_code, s.customer_id, s.avr_rtpc_qty, s.avr_rtpc_qty_temp,
      s.operation_source, s.location, s.is_active,
      s.created_at, s.updated_at,
      c.name as customer_name,
      ip_current.product_name, ip_current.product_ppc_num,
      ip_current.product_hrc1, ip_current.product_hrc3, ip_current.product_pcm3,
      ip_previous.product_ppc_num as product_ppc_num_previous
    FROM ${tables.stock} s
    LEFT JOIN ${tables.customers} c ON s.customer_id = c.id
    -- Get current version (max version_id)
    LEFT JOIN (
      SELECT ip1.*
      FROM ${tables.imported_products} ip1
      INNER JOIN (
        SELECT customer_id, product_code, MAX(version_id) as max_version_id
        FROM ${tables.imported_products}
        GROUP BY customer_id, product_code
      ) ip2 ON ip1.customer_id = ip2.customer_id AND ip1.product_code = ip2.product_code AND ip1.version_id = ip2.max_version_id
    ) ip_current ON s.product_code = ip_current.product_code AND s.customer_id = ip_current.customer_id
    -- Get previous version (max version_id - 1)
    LEFT JOIN (
      SELECT ip1.*
      FROM ${tables.imported_products} ip1
      INNER JOIN (
        SELECT customer_id, product_code, MAX(version_id) - 1 as prev_version_id
        FROM ${tables.imported_products}
        GROUP BY customer_id, product_code
        HAVING MAX(version_id) > 1
      ) ip2 ON ip1.customer_id = ip2.customer_id AND ip1.product_code = ip2.product_code AND ip1.version_id = ip2.prev_version_id
    ) ip_previous ON s.product_code = ip_previous.product_code AND s.customer_id = ip_previous.customer_id
    WHERE s.is_active = 1
    ORDER BY s.customer_id, s.product_code ASC
  `

  try {
    const result = await executeQuery(query, [])
    if (!result?.result?.resultRows) return []

    const stocks = result.result.resultRows.map((row: unknown[]) => {
      const avrRtpcQty = row[3] as number
      const productPpcNum = row[12] as string | null
      const productPpcNumPrevious = row[16] as string | null

      // Helper function to calculate cartons and pieces
      const calculateValues = (quantity: number, ppcNum: string | null) => {
        if (quantity === 0 || !ppcNum) return { cartons: 0, pieces: 0 }
        const ppc = parseFloat(ppcNum)
        if (isNaN(ppc) || ppc === 0) return { cartons: 0, pieces: 0 }
        const cartons = Math.floor(quantity / ppc)
        const pieces = quantity % ppc
        return { cartons, pieces }
      }

      // Calculate current values
      const currentValues = calculateValues(avrRtpcQty, productPpcNum)
      // Calculate previous values (using current quantity but previous PPC)
      const previousValues = calculateValues(avrRtpcQty, productPpcNumPrevious)

      return {
        id: row[0] as number,
        productCode: row[1] as string,
        customerId: row[2] as number,
        avrRtpcQty: avrRtpcQty,
        avrRtpcQtyTemp: row[4] as number,
        operationSource: row[5] as 'Manual' | 'Inbound' | 'Outbound',
        location: row[6] as string | null,
        isActive: (row[7] as number) === 1,
        createdAt: row[8] as string,
        updatedAt: row[9] as string,
        // Virtual fields from joined tables
        customerName: row[10] as string,
        productName: row[11] as string,
        productPpcNum: productPpcNum,
        productHrc1: row[13] as string | null,
        productHrc3: row[14] as string | null,
        productPcm3: row[15] as string | null,
        // Previous version data
        productPpcNumPrevious: productPpcNumPrevious,
        // Calculated fields
        numOfCartonsCurrent: currentValues.cartons,
        numOfCartonsPrevious: productPpcNumPrevious ? previousValues.cartons : undefined,
        numOfPiecesCurrent: currentValues.pieces,
        numOfPiecesPrevious: productPpcNumPrevious ? previousValues.pieces : undefined,
      }
    })

    return stocks
  } catch (err) {
    console.error('Failed to get all stocks:', err)
    throw err
  }
}

export async function getStocksByCustomer(customerId: number): Promise<Stock[]> {
  const query = `
    SELECT DISTINCT
      s.id, s.product_code, s.customer_id, s.avr_rtpc_qty, s.avr_rtpc_qty_temp,
      s.operation_source, s.location, s.is_active,
      s.created_at, s.updated_at,
      c.name as customer_name,
      ip.product_name, ip.product_ppc_num,
      ip.product_hrc1, ip.product_hrc3, ip.product_pcm3
    FROM ${tables.stock} s
    LEFT JOIN ${tables.customers} c ON s.customer_id = c.id
    LEFT JOIN (
      SELECT ip1.*
      FROM ${tables.imported_products} ip1
      INNER JOIN (
        SELECT customer_id, product_code, MAX(version_id) as max_version_id
        FROM ${tables.imported_products}
        GROUP BY customer_id, product_code
      ) ip2 ON ip1.customer_id = ip2.customer_id AND ip1.product_code = ip2.product_code AND ip1.version_id = ip2.max_version_id
    ) ip ON s.product_code = ip.product_code AND s.customer_id = ip.customer_id
    WHERE s.is_active = 1 AND s.customer_id = ?
    ORDER BY s.product_code ASC
  `

  try {
    const result = await executeQuery(query, [customerId])
    if (!result?.result?.resultRows) return []

    const stocks = result.result.resultRows.map((row: unknown[]) => {
      return {
        id: row[0] as number,
        productCode: row[1] as string,
        customerId: row[2] as number,
        avrRtpcQty: row[3] as number,
        avrRtpcQtyTemp: row[4] as number,
        operationSource: row[5] as 'Manual' | 'Inbound' | 'Outbound',
        location: row[6] as string | null,
        isActive: (row[7] as number) === 1,
        createdAt: row[8] as string,
        updatedAt: row[9] as string,
        // Virtual fields from joined tables
        customerName: row[10] as string,
        productName: row[11] as string,
        productPpcNum: row[12] as string | null,
        productHrc1: row[13] as string | null,
        productHrc3: row[14] as string | null,
        productPcm3: row[15] as string | null,
      }
    })

    return stocks
  } catch (err) {
    console.error('Failed to get stocks by customer:', err)
    throw err
  }
}

export async function getStockById(id: number): Promise<Stock | null> {
  const query = `
    SELECT
      s.id, s.product_code, s.customer_id, s.avr_rtpc_qty, s.avr_rtpc_qty_temp,
      s.operation_source, s.location, s.is_active,
      s.created_at, s.updated_at,
      c.name as customer_name,
      ip.product_name, ip.product_ppc_num,
      ip.product_hrc1, ip.product_hrc3, ip.product_pcm3
    FROM ${tables.stock} s
    LEFT JOIN ${tables.customers} c ON s.customer_id = c.id
    LEFT JOIN (
      SELECT ip1.*
      FROM ${tables.imported_products} ip1
      INNER JOIN (
        SELECT customer_id, product_code, MAX(version_id) as max_version_id
        FROM ${tables.imported_products}
        GROUP BY customer_id, product_code
      ) ip2 ON ip1.customer_id = ip2.customer_id AND ip1.product_code = ip2.product_code AND ip1.version_id = ip2.max_version_id
    ) ip ON s.product_code = ip.product_code AND s.customer_id = ip.customer_id
    WHERE s.id = ?
    LIMIT 1
  `

  try {
    const result = await executeQuery(query, [id])
    if (!result?.result?.resultRows || result.result.resultRows.length === 0) return null

    const row = result.result.resultRows[0]

    return {
      id: row[0] as number,
      productCode: row[1] as string,
      customerId: row[2] as number,
      avrRtpcQty: row[3] as number,
      avrRtpcQtyTemp: row[4] as number,
      operationSource: row[5] as StockOperationSource,
      location: row[6] as string | null,
      isActive: (row[7] as number) === 1,
      createdAt: row[8] as string,
      updatedAt: row[9] as string,
      // Virtual fields from joined tables
      customerName: row[10] as string,
      productName: row[11] as string,
      productPpcNum: row[12] as string | null,
      productHrc1: row[13] as string | null,
      productHrc3: row[14] as string | null,
      productPcm3: row[15] as string | null,
    }
  } catch (err) {
    console.error('Failed to get stock by ID:', err)
    throw err
  }
}

export async function getStockByCustomerAndProduct(
  customerId: number,
  productCode: string,
): Promise<Stock | null> {
  const query = `
    SELECT DISTINCT
      s.id, s.product_code, s.customer_id, s.avr_rtpc_qty, s.avr_rtpc_qty_temp,
      s.operation_source, s.location, s.is_active,
      s.created_at, s.updated_at,
      c.name as customer_name,
      ip.product_name, ip.product_ppc_num,
      ip.product_hrc1, ip.product_hrc3, ip.product_pcm3
    FROM ${tables.stock} s
    LEFT JOIN ${tables.customers} c ON s.customer_id = c.id
    LEFT JOIN (
      SELECT ip1.*
      FROM ${tables.imported_products} ip1
      INNER JOIN (
        SELECT customer_id, product_code, MAX(version_id) as max_version_id
        FROM ${tables.imported_products}
        GROUP BY customer_id, product_code
      ) ip2 ON ip1.customer_id = ip2.customer_id AND ip1.product_code = ip2.product_code AND ip1.version_id = ip2.max_version_id
    ) ip ON s.product_code = ip.product_code AND s.customer_id = ip.customer_id
    WHERE s.is_active = 1 AND s.customer_id = ? AND s.product_code = ?
    LIMIT 1
  `

  try {
    const result = await executeQuery(query, [customerId, productCode])
    if (!result?.result?.resultRows || result.result.resultRows.length === 0) return null

    const row = result.result.resultRows[0]

    return {
      id: row[0] as number,
      productCode: row[1] as string,
      customerId: row[2] as number,
      avrRtpcQty: row[3] as number,
      avrRtpcQtyTemp: row[4] as number,
      operationSource: row[5] as StockOperationSource,
      location: row[6] as string | null,
      isActive: (row[7] as number) === 1,
      createdAt: row[8] as string,
      updatedAt: row[9] as string,
      // Virtual fields from joined tables
      customerName: row[10] as string,
      productName: row[11] as string,
      productPpcNum: row[12] as string | null,
      productHrc1: row[13] as string | null,
      productHrc3: row[14] as string | null,
      productPcm3: row[15] as string | null,
    }
  } catch (err) {
    console.error('Failed to get stock by customer and product:', err)
    throw err
  }
}

export async function createOrUpdateStock(
  stockData: Omit<Stock, 'id' | 'createdAt' | 'updatedAt' | 'operationSource' | 'isActive'>,
  operationSource: StockOperationSource = 'Manual',
): Promise<Stock> {
  const now = new Date().toISOString()

  // Check if stock already exists by customer and product code
  const existingStock = await getStockByCustomerAndProduct(
    stockData.customerId,
    stockData.productCode,
  )

  if (existingStock) {
    // Only update if quantity or location actually changed
    const quantityChanged = existingStock.avrRtpcQty !== stockData.avrRtpcQty
    const locationChanged = existingStock.location !== stockData.location

    if (!quantityChanged && !locationChanged) {
      // No significant changes, return existing stock
      return existingStock
    }

    // Update existing stock
    const query = `
      UPDATE ${tables.stock}
      SET avr_rtpc_qty = ?, avr_rtpc_qty_temp = ?, operation_source = ?, location = ?, updated_at = ?
      WHERE id = ?
    `

    await executeQuery(query, [
      stockData.avrRtpcQty,
      stockData.avrRtpcQtyTemp,
      operationSource,
      stockData.location,
      now,
      existingStock.id,
    ])

    return {
      ...existingStock,
      avrRtpcQty: stockData.avrRtpcQty,
      avrRtpcQtyTemp: stockData.avrRtpcQtyTemp,
      operationSource,
      location: stockData.location,
      updatedAt: now,
    }
  } else {
    // Create new stock
    const query = `
      INSERT INTO ${tables.stock}
      (product_code, customer_id, avr_rtpc_qty, avr_rtpc_qty_temp, operation_source, location, is_active, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, 1, ?, ?)
    `

    await executeQuery(query, [
      stockData.productCode,
      stockData.customerId,
      stockData.avrRtpcQty,
      stockData.avrRtpcQtyTemp,
      operationSource,
      stockData.location,
      now,
      now,
    ])

    // Get the inserted ID
    const idResult = await executeQuery(`SELECT last_insert_rowid() as newId`)
    const newId = (idResult?.result?.resultRows?.[0]?.[0] as number) ?? 0

    return {
      id: newId,
      productCode: stockData.productCode,
      customerId: stockData.customerId,
      avrRtpcQty: stockData.avrRtpcQty,
      avrRtpcQtyTemp: stockData.avrRtpcQtyTemp,
      operationSource,
      location: stockData.location,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    }
  }
}

export async function updateStockQuantity(
  customerId: number,
  productCode: string,
  newQuantity: number,
  operationSource: StockOperationSource = 'Manual',
): Promise<void> {
  const now = new Date().toISOString()

  // Get current stock to track changes
  const currentStock = await getStockByCustomerAndProduct(customerId, productCode)
  if (!currentStock) {
    throw new Error(`Stock not found for customer ${customerId} and product ${productCode}`)
  }

  // Check if quantity actually changed
  if (currentStock.avrRtpcQty === newQuantity) {
    return // No change needed
  }

  // Update the stock quantity
  const query = `
    UPDATE ${tables.stock}
    SET avr_rtpc_qty = ?, operation_source = ?, updated_at = ?
    WHERE id = ?
  `

  try {
    await executeQuery(query, [newQuantity, operationSource, now, currentStock.id])
  } catch (err) {
    console.error('Failed to update stock quantity:', err)
    throw err
  }
}

export async function deleteStock(
  id: number,
  operationSource: StockOperationSource = 'Manual',
): Promise<void> {
  // Get stock data before deletion
  const stockToDelete = await getStockById(id)
  if (!stockToDelete) {
    throw new Error(`Stock with ID ${id} not found`)
  }

  // Soft delete by setting is_active to false
  const now = new Date().toISOString()

  const query = `
    UPDATE ${tables.stock}
    SET is_active = 0, operation_source = ?, updated_at = ?
    WHERE id = ?
  `

  try {
    await executeQuery(query, [operationSource, now, id])
  } catch (err) {
    console.error('Failed to delete stock:', err)
    throw err
  }
}

// Calculate stock quantity based on inbound/outbound transactions
export async function calculateStockQuantity(
  customerId: number,
  productCode: string,
): Promise<number> {
  try {
    // Get total inbound quantity
    const inboundQuery = `
      SELECT COALESCE(SUM(avr_rtpc_qty), 0) as total_inbound
      FROM ${tables.customer_inbound}
      WHERE customer_id = ? AND avr_prod_cod = ?
    `
    const inboundResult = await executeQuery(inboundQuery, [customerId, productCode])
    const totalInbound = (inboundResult?.result?.resultRows?.[0]?.[0] as number) ?? 0

    // Get total outbound quantity
    const outboundQuery = `
      SELECT COALESCE(SUM(spr_rtpc_qty), 0) as total_outbound
      FROM ${tables.customer_outbound}
      WHERE customer_id = ? AND spr_prod_code = ?
    `
    const outboundResult = await executeQuery(outboundQuery, [customerId, productCode])
    const totalOutbound = (outboundResult?.result?.resultRows?.[0]?.[0] as number) ?? 0

    // Calculate remaining stock
    const remainingStock = totalInbound - totalOutbound
    return Math.max(0, remainingStock) // Ensure non-negative
  } catch (err) {
    console.error('Failed to calculate stock quantity:', err)
    return 0
  }
}

// Update all stock quantities based on inbound/outbound data
export async function recalculateAllStockQuantities(): Promise<void> {
  try {
    const stocks = await getAllStocks()

    for (const stock of stocks) {
      const calculatedQuantity = await calculateStockQuantity(stock.customerId, stock.productCode)
      if (calculatedQuantity !== stock.avrRtpcQty) {
        await updateStockQuantity(stock.customerId, stock.productCode, calculatedQuantity, 'Manual')
      }
    }
  } catch (err) {
    console.error('Failed to recalculate stock quantities:', err)
    throw err
  }
}

// Helper function to preserve existing stock quantities during import
export async function createOrUpdateStockPreservingQuantity(
  stockData: Omit<Stock, 'id' | 'createdAt' | 'updatedAt' | 'operationSource' | 'isActive'>,
  operationSource: StockOperationSource = 'Manual',
): Promise<Stock> {
  // Check if stock already exists by customer and product code
  const existingStock = await getStockByCustomerAndProduct(
    stockData.customerId,
    stockData.productCode,
  )

  if (existingStock) {
    // For existing stock, preserve the current quantity and only update other fields if needed
    return await createOrUpdateStock(
      {
        ...stockData,
        avrRtpcQty: existingStock.avrRtpcQty, // Preserve existing quantity
        avrRtpcQtyTemp: existingStock.avrRtpcQtyTemp, // Preserve existing temp quantity
      },
      operationSource,
    )
  } else {
    // For new stock, use the provided quantity (which should be 0 for new imports)
    return await createOrUpdateStock(stockData, operationSource)
  }
}

export interface StockCalculation {
  // Input values
  quantity: number // AI - from stock.avr_rtpc_qty
  piecesPerCarton: number // AZ - from imported_products.product_ppc_num
  packagingHierarchy: string // BA - from imported_products.product_hrc1
  productCategory: string // BB - from imported_products.product_hrc3
  pieceCubicMeters: number // BM - from imported_products.product_pcm3

  // Calculated values
  innerMaster: number // BD
  carton: number // BE
  pcs: number // BF
  innerCarton: number // BG
  innerPcs: number // BH
  totalCarton: number // BI
  totalPcs: number // BJ
  totalAIPcs: number | null // BK
  totalM3: number // BL
}

// Calculate stock values for Excel export using the same formulas as calculateStockFormulas
export function calculateStockValues(stock: Stock): StockCalculation {
  const {
    avrRtpcQty: quantity,
    productPpcNum: piecesPerCartonStr,
    productHrc1: packagingHierarchy,
    productHrc3: productCategory,
    productPcm3: pieceCubicMetersStr,
  } = stock

  // Convert string values to numbers with null coalescing
  const piecesPerCarton = parseFloat(piecesPerCartonStr ?? '0')
  const pieceCubicMeters = parseFloat(pieceCubicMetersStr ?? '0')
  const safePackagingHierarchy = packagingHierarchy ?? ''
  const safeProductCategory = productCategory ?? ''

  // BD = IFERROR(MID(BA3,FIND("(",BA3)+1,FIND(")",BA3)-FIND("(",BA3)-1)," ")
  let innerMaster = 0
  if (safePackagingHierarchy.includes('(') && safePackagingHierarchy.includes(')')) {
    const openParenIndex = safePackagingHierarchy.indexOf('(')
    const closeParenIndex = safePackagingHierarchy.indexOf(')')
    if (closeParenIndex > openParenIndex) {
      const extracted = safePackagingHierarchy.substring(openParenIndex + 1, closeParenIndex)
      innerMaster = parseFloat(extracted) || 0
    }
  }

  // BE = INT(AI3/AZ3)
  const carton = piecesPerCarton > 0 ? Math.floor(quantity / piecesPerCarton) : 0

  // BF = AI3-(BE3*AZ3)
  const pcs = quantity - carton * piecesPerCarton

  // BG = IF((LEFT(BA3,5)="INNER"),INT(BF3/BD3),0)
  const innerCarton =
    safePackagingHierarchy.substring(0, 5).toUpperCase() === 'INNER' && innerMaster > 0
      ? Math.floor(pcs / innerMaster)
      : 0

  // BH = IFERROR(BF3-(BG3*BD3),"")
  let innerPcs = 0
  if (safePackagingHierarchy.substring(0, 5).toUpperCase() === 'INNER') {
    innerPcs = pcs - innerCarton * innerMaster
  }

  // BI = IF((LEFT(BA3,5)="INNER"),(BE3+BG3),BE3)
  const totalCarton =
    safePackagingHierarchy.substring(0, 5).toUpperCase() === 'INNER' ? carton + innerCarton : carton

  // BJ = IF((LEFT(BA3,5)="INNER"),(BH3),(BF3))
  const totalPcs = safePackagingHierarchy.substring(0, 5).toUpperCase() === 'INNER' ? innerPcs : pcs

  // BK = IF(BB3="AI",AI3,"")
  const totalAIPcs = safeProductCategory.toUpperCase() === 'AI' ? quantity : null

  // BL = BM3*AI3
  const totalM3 = pieceCubicMeters * quantity

  return {
    quantity,
    piecesPerCarton,
    packagingHierarchy: safePackagingHierarchy,
    productCategory: safeProductCategory,
    pieceCubicMeters,
    innerMaster,
    carton,
    pcs,
    innerCarton,
    innerPcs,
    totalCarton,
    totalPcs,
    totalAIPcs,
    totalM3,
  }
}
