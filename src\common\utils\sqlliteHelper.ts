import { useSQLite } from '@IVC/hooks/useSQLite'
const { executeQuery } = useSQLite()

/**
 * Converts a string to camelCase
 * @param s  The string to convert
 * @returns The camelCase version of the string
 */
export function toCamelCase(s: string) {
  return s.replace(/_([a-z])/g, (_, c) => c.toUpperCase())
}

/**
 * Returns an array of objects with camelCase column names
 * @param table  Table name
 * @param camel  Convert to camelCase (default true)
 */
export async function queryAsObjects<T = any>(table: string, camel = true): Promise<T[]> {
  // 1. Lấy cột
  const pragma = await executeQuery(`PRAGMA table_info(${table})`)
  const cols = pragma.result.resultRows!.map((r) => r[1] as string) // col index 1 = name

  // 2. Lấy dữ liệu
  const sel = await executeQuery(`SELECT * FROM ${table}`)
  const rows = sel.result.resultRows || []

  // 3. Map sang object
  return rows.map((row) =>
    Object.fromEntries(cols.map((col, i) => [camel ? toCamelCase(col) : col, row[i]])),
  ) as T[]
}

export function convertRecordToCamelCase<T extends Record<string, any>>(
  record: T,
): Record<string, any> {
  const result: Record<string, any> = {}
  for (const key in record) {
    if (Object.prototype.hasOwnProperty.call(record, key)) {
      const camelKey = toCamelCase(key)
      result[camelKey] = record[key]
    }
  }
  return result
}

export function convertDataToCamelCase(data: Record<string, any>): Record<string, any> {
  const converted: Record<string, any> = {}
  for (const section in data) {
    if (Array.isArray(data[section])) {
      converted[section] = data[section].map(convertRecordToCamelCase)
    } else {
      converted[section] = convertRecordToCamelCase(data[section])
    }
  }
  return converted
}

/**
 * Delete file 'mydb.sqlite3' in OPFS
 * @returns
 */
export async function deleteOpfsDb() {
  // OPFS root directory for current origin
  const root = await navigator.storage.getDirectory()

  // Make sure all SQLite connections are closed before deleting
  // await db.close();

  // Delete file
  await root.removeEntry('mydb.sqlite3')
  console.log('DB removed!')
}
