<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import {
  Modal,
  message,
  Button,
  PageHeader,
  Select,
  Row as ARow,
  Col as ACol,
  Table as ATable,
  Tag,
} from 'ant-design-vue'
import type { TablePaginationConfig } from 'ant-design-vue'

import ActionButtons from '@IVC/components/ActionButtons.vue'
import StockEditModal from '@IVC/components/dialogs/StockEditModal.vue'
import type { Customer } from '@IVC/types/MasterDataTypes/Customer'
import {
  type Stock,
  type StockCalculation,
  getAllStocks,
  deleteStock,
  recalculateAllStockQuantities,
  calculateStockValues,
} from '@IVC/services/master-data/stock'
import { getAllCustomers } from '@IVC/services/master-data/customer'
import type { SelectValue } from 'ant-design-vue/lib/select'
import { determineStockStatus, getStatusColor, getQuantityColor } from '@IVC/utils/stockUtils'
import { ReloadOutlined, CalculatorOutlined } from '@ant-design/icons-vue'

const router = useRouter()
const localLoading = ref(false)
const recalculateLoading = ref(false)
const customers = ref<Customer[]>([])
const selectedCustomerId = ref<number | undefined>(undefined)
const allStocks = ref<Stock[]>([]) // Store all stocks
const stocksForSelectedCustomer = ref<Stock[]>([]) // Stocks for the selected customer

// Modal state
const editModalVisible = ref(false)
const selectedStock = ref<Stock | null>(null)

// Pagination
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
})

const stockColumns = [
  {
    title: 'Customer Name',
    dataIndex: 'customerName',
    key: 'customer_name',
    sorter: (a: Stock, b: Stock) => (a.customerName ?? '').localeCompare(b.customerName ?? ''),
    width: 120,
  },
  {
    title: 'Product Code',
    dataIndex: 'productCode',
    key: 'product_code',
    sorter: (a: Stock, b: Stock) => (a.productCode ?? '').localeCompare(b.productCode ?? ''),
    width: 120,
  },
  {
    title: 'Product Name',
    dataIndex: 'productName',
    key: 'product_name',
    width: 200,
    ellipsis: { showTitle: true },
  },
  {
    title: 'Quantity',
    dataIndex: 'avrRtpcQty',
    key: 'avr_rtpc_qty',
    width: 120,
    sorter: (a: Stock, b: Stock) => a.avrRtpcQty - b.avrRtpcQty,
  },
  {
    title: 'Status',
    key: 'status',
    width: 100,
    sorter: (a: Stock, b: Stock) =>
      determineStockStatus(a.avrRtpcQty).localeCompare(determineStockStatus(b.avrRtpcQty)),
  },
  {
    title: 'Operation',
    dataIndex: 'operationSource',
    key: 'operation_source',
    width: 120,
  },
  { title: 'Pieces Per Carton', dataIndex: 'productPpcNum', key: 'ppc_num', width: 120 },
  { title: 'Total Cartons', key: 'total_cartons', width: 100 },
  { title: 'Total Pieces', key: 'total_pieces', width: 100 },
  { title: 'Total AI', key: 'total_ai', width: 100 },
  { title: 'Total M3', key: 'total_m3', width: 100 },
  { title: 'Action', key: 'action', width: 80 },
]

const fetchInitialData = async () => {
  localLoading.value = true
  try {
    // Fetch customers for the select dropdown
    customers.value = await getAllCustomers()
    // Fetch all stocks initially
    allStocks.value = await getAllStocks()
    // If a customer was previously selected (e.g. page refresh with query params), filter stocks
    filterStocksForSelectedCustomer()
  } catch (err) {
    message.error(`Failed to load initial data: ${(err as Error).message}`)
    allStocks.value = []
    stocksForSelectedCustomer.value = []
  } finally {
    localLoading.value = false
  }
}

onMounted(async () => {
  await fetchInitialData()
  // Check for customerId in query params
  const queryCustomerId = router.currentRoute.value.query.customerId
  if (queryCustomerId && !selectedCustomerId.value) {
    const customerIdNum = Number(queryCustomerId)
    if (customers.value.some((c) => c.id === customerIdNum)) {
      selectedCustomerId.value = customerIdNum
      filterStocksForSelectedCustomer()
    }
  }
})

const filterStocksForSelectedCustomer = () => {
  if (selectedCustomerId.value) {
    stocksForSelectedCustomer.value = allStocks.value
      .filter((s) => s.customerId === selectedCustomerId.value)
      .sort((a, b) => {
        // Sort by product code
        return (a.productCode ?? '').localeCompare(b.productCode ?? '')
      })
  } else {
    stocksForSelectedCustomer.value = [...allStocks.value] // Show all stocks if no customer is selected
  }

  // Update pagination total
  pagination.total = stocksForSelectedCustomer.value.length
}

const handleCustomerSelectionChange = (value: SelectValue) => {
  selectedCustomerId.value = value as number | undefined
  localLoading.value = true // Indicate loading while filtering
  filterStocksForSelectedCustomer()
  localLoading.value = false
}

const handleUpdateStock = (stock: Stock) => {
  selectedStock.value = stock
  editModalVisible.value = true
}

const handleEditModalSuccess = async () => {
  await fetchInitialData()
  filterStocksForSelectedCustomer()
}

const handleTableChange = (pag: TablePaginationConfig) => {
  if (pag.current) pagination.current = pag.current
  if (pag.pageSize) pagination.pageSize = pag.pageSize
}

const confirmDeleteStock = (stockId: number) => {
  Modal.confirm({
    title: 'Delete Stock Record',
    content: 'Are you sure you want to delete this stock record?',
    okText: 'Delete',
    okType: 'danger',
    async onOk() {
      try {
        await deleteStock(stockId)
        message.success('Stock record deleted.')

        // Remove item from local data instead of reloading
        const index = allStocks.value.findIndex((item) => item.id === stockId)
        if (index !== -1) {
          allStocks.value.splice(index, 1)
        }

        // Update filtered data and pagination
        filterStocksForSelectedCustomer()
      } catch (err) {
        message.error(`Failed to delete stock: ${(err as Error).message}`)
      }
    },
  })
}

const handleRecalculateStocks = async () => {
  Modal.confirm({
    title: 'Recalculate Stock Quantities',
    content:
      'This will recalculate all stock quantities based on inbound and outbound transactions. This may take a while. Continue?',
    okText: 'Recalculate',
    okType: 'primary',
    async onOk() {
      try {
        recalculateLoading.value = true
        await recalculateAllStockQuantities()
        message.success('Stock quantities recalculated successfully.')
        await fetchInitialData() // Refresh the data
      } catch (err) {
        message.error(`Failed to recalculate stocks: ${(err as Error).message}`)
      } finally {
        recalculateLoading.value = false
      }
    },
  })
}

const getOperationSourceColor = (source: string) => {
  switch (source) {
    case 'Manual':
      return 'blue'
    case 'Inbound':
      return 'green'
    case 'Outbound':
      return 'orange'
    default:
      return 'default'
  }
}

// Helper function to get calculated values for a stock record
const getCalculatedValues = (record: Stock): StockCalculation => {
  return calculateStockValues(record)
}
</script>

<template>
  <div class="header-container-master">
    <PageHeader
      title="Stock Management"
      sub-title="View and manage stock levels with version tracking"
    >
      <template #extra>
        <Button type="default" @click="handleRecalculateStocks" :loading="recalculateLoading">
          <CalculatorOutlined /> Recalculate Stock
        </Button>
        <Button type="primary" @click="fetchInitialData" :loading="localLoading">
          <ReloadOutlined /> Refresh
        </Button>
      </template>
    </PageHeader>

    <div>
      <ARow :gutter="[16, 16]" style="margin-bottom: 16px">
        <ACol :span="8">
          <Select
            v-model:value="selectedCustomerId"
            placeholder="Select Customer to Filter Stocks"
            style="width: 100%"
            @change="handleCustomerSelectionChange"
            show-search
            option-filter-prop="label"
            :loading="customers.length === 0 && localLoading"
            allow-clear
          >
            <Select.Option
              v-for="customer in customers"
              :key="customer.id"
              :value="customer.id"
              :label="customer.name"
            >
              {{ customer.name }} ({{ customer.code }})
            </Select.Option>
          </Select>
        </ACol>
      </ARow>

      <ATable
        :columns="stockColumns"
        :data-source="stocksForSelectedCustomer"
        :loading="localLoading"
        row-key="id"
        :pagination="pagination"
        @change="handleTableChange"
        :scroll="{ x: 1500 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'avr_rtpc_qty'">
            <Tag :color="getQuantityColor(record.avrRtpcQty)">
              {{ record.avrRtpcQty }}
            </Tag>
          </template>
          <template v-else-if="column.key === 'status'">
            <Tag :color="getStatusColor(determineStockStatus(record.avrRtpcQty))">
              {{ determineStockStatus(record.avrRtpcQty) }}
            </Tag>
          </template>
          <template v-else-if="column.key === 'operation_source'">
            <Tag :color="getOperationSourceColor(record.operationSource)">
              {{ record.operationSource }}
            </Tag>
          </template>
          <template v-else-if="column.key === 'ppc_num'">
            <div class="two-line-display">
              <div class="current-line">
                <span class="label">Current:</span>
                <span class="value">{{ record.productPpcNum || '' }}</span>
              </div>
              <div class="previous-line">
                <span class="label">Previous:</span>
                <span class="value">{{ record.productPpcNumPrevious || '' }}</span>
              </div>
            </div>
          </template>
          <template v-else-if="column.key === 'total_cartons'">
            <div class="two-line-display">
              <div class="current-line">
                <span class="label">Current:</span>
                <span class="value">{{ getCalculatedValues(record as Stock).totalCarton }}</span>
              </div>
              <div class="previous-line">
                <span class="label">Previous:</span>
                <span class="value">{{
                  record.productPpcNumPrevious
                    ? getCalculatedValues({
                        ...record,
                        productPpcNum: record.productPpcNumPrevious,
                      } as Stock).totalCarton
                    : ''
                }}</span>
              </div>
            </div>
          </template>
          <template v-else-if="column.key === 'total_pieces'">
            <div class="two-line-display">
              <div class="current-line">
                <span class="label">Current:</span>
                <span class="value">{{ getCalculatedValues(record as Stock).totalPcs }}</span>
              </div>
              <div class="previous-line">
                <span class="label">Previous:</span>
                <span class="value">{{
                  record.productPpcNumPrevious
                    ? getCalculatedValues({
                        ...record,
                        productPpcNum: record.productPpcNumPrevious,
                      } as Stock).totalPcs
                    : ''
                }}</span>
              </div>
            </div>
          </template>
          <template v-else-if="column.key === 'total_ai'">
            <div class="two-line-display">
              <div class="current-line">
                <span class="label">Current:</span>
                <span class="value">{{
                  getCalculatedValues(record as Stock).totalAIPcs || ''
                }}</span>
              </div>
              <div class="previous-line">
                <span class="label">Previous:</span>
                <span class="value">{{
                  record.productPpcNumPrevious
                    ? getCalculatedValues({
                        ...record,
                        productPpcNum: record.productPpcNumPrevious,
                      } as Stock).totalAIPcs || ''
                    : ''
                }}</span>
              </div>
            </div>
          </template>
          <template v-else-if="column.key === 'total_m3'">
            <div class="two-line-display">
              <div class="current-line">
                <span class="label">Current:</span>
                <span class="value">{{
                  getCalculatedValues(record as Stock).totalM3.toFixed(4)
                }}</span>
              </div>
              <div class="previous-line">
                <span class="label">Previous:</span>
                <span class="value">{{
                  record.productPpcNumPrevious
                    ? getCalculatedValues({
                        ...record,
                        productPpcNum: record.productPpcNumPrevious,
                      } as Stock).totalM3.toFixed(4)
                    : ''
                }}</span>
              </div>
            </div>
          </template>
          <template v-else-if="column.key === 'action'">
            <ActionButtons
              name="Stock"
              :show-edit="true"
              :show-delete="true"
              :show-audit-trail="false"
              :show-save="false"
              @edit="handleUpdateStock(record as Stock)"
              @delete="confirmDeleteStock((record as Stock).id)"
            />
          </template>
        </template>
      </ATable>
    </div>

    <!-- Stock Edit Modal -->
    <StockEditModal
      v-model:visible="editModalVisible"
      :stock="selectedStock"
      @success="handleEditModalSuccess"
    />
  </div>
</template>

<style scoped>
/* Add any specific styles for StockView if needed */
.ant-statistic-title {
  font-size: 14px;
  color: #666;
}

.ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* Two-line display styling */
.two-line-display {
  line-height: 1.3;
  font-size: 13px;
}

.current-line,
.previous-line {
  margin-bottom: 2px;
  display: flex;
  align-items: center;
}

.current-line:last-child,
.previous-line:last-child {
  margin-bottom: 0;
}

.current-line .label {
  font-weight: 600;
  color: #1890ff;
  min-width: 55px;
  font-size: 12px;
}

.previous-line .label {
  font-weight: 600;
  color: #8c8c8c;
  min-width: 55px;
  font-size: 12px;
}

.current-line .value {
  color: #262626;
  font-weight: 500;
}

.previous-line .value {
  color: #8c8c8c;
  font-style: italic;
}

/* When previous value is empty, make it lighter */
.previous-line .value:empty::after {
  content: '';
  color: #d9d9d9;
}
</style>
