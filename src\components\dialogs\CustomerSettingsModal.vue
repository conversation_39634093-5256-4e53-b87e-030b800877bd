<template>
  <a-modal style="top: 20px" :width="900" :visible="visible" @cancel="handleClose">
    <template #title>
      <div style="display: flex; align-items: center">
        <span>Customer Settings{{ `${customer ? ` - ${customer.name}` : ''}` }}</span>
      </div>
    </template>
    <template #footer>
      <a-button key="back" @click="handleClose">Cancel</a-button>
      <a-button key="submit" type="primary" :loading="isSaving" @click="handleSaveSettings">
        Save Settings
      </a-button>
    </template>

    <div v-if="customer">
      <a-divider style="margin-top: 0px; margin-bottom: 12px" />

      <a-layout style="background: #fff; padding-top: 10px">
        <a-layout-sider width="200" style="background: #fff; border-right: 1px solid #f0f0f0">
          <a-menu
            v-model:selectedKeys="selectedMenuKeys"
            mode="inline"
            style="height: 100%; border-right: 0"
          >
            <a-menu-item key="quotation">Quotation Settings</a-menu-item>
            <a-menu-item key="notifications">Notification Preferences</a-menu-item>
            <a-menu-item key="billing">Billing Details</a-menu-item>
            <a-menu-item key="integrations">Integrations</a-menu-item>
            <a-menu-item key="masterDataImport">Master Data Import</a-menu-item>
            <!-- Add more categories here -->
          </a-menu>
        </a-layout-sider>
        <a-layout-content style="padding: 0 24px; min-height: 320px">
          <!-- Quotation Settings Content -->
          <div v-if="selectedMenuKeys[0] === 'quotation'">
            <h3>Quotation Settings</h3>
            <a-form layout="vertical" style="margin-top: 16px">
              <a-form-item label="Quotation Cycle">
                <a-select v-model:value="selectedQuotationCycle" style="width: 100%">
                  <a-select-option :value="QuotationCycleType.DAILY_MONTHLY_AVG">
                    Daily quotation, summarized monthly (average)
                  </a-select-option>
                  <a-select-option :value="QuotationCycleType.MONTHLY">
                    Monthly quotation (end of month)
                  </a-select-option>
                  <a-select-option :value="QuotationCycleType.BY_TERM"> By Term </a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item
                v-if="selectedQuotationCycle === QuotationCycleType.BY_TERM"
                label="Term Duration (days)"
              >
                <a-input-number
                  v-model:value="termDays"
                  :min="1"
                  placeholder="Enter days per term"
                  style="width: 100%"
                />
              </a-form-item>
            </a-form>
          </div>

          <!-- Notification Preferences Content -->
          <div v-if="selectedMenuKeys[0] === 'notifications'">
            <h3>Notification Preferences</h3>
            <p style="margin-top: 16px">
              Configure email and SMS notifications for various events.
            </p>
            <!-- Add notification settings form elements here -->
          </div>

          <!-- Billing Details Content -->
          <div v-if="selectedMenuKeys[0] === 'billing'">
            <h3>Billing Details</h3>
            <p style="margin-top: 16px">
              Manage payment methods, billing address, and invoice preferences.
            </p>
            <!-- Add billing settings form elements here -->
          </div>

          <!-- Integrations Content -->
          <div v-if="selectedMenuKeys[0] === 'integrations'">
            <h3>Integrations</h3>
            <p style="margin-top: 16px">Connect with third-party services.</p>
            <!-- Add integrations settings here -->
          </div>

          <!-- Master Data Import Content -->
          <div v-if="selectedMenuKeys[0] === 'masterDataImport'">
            <h3>Master Data Import</h3>
            <a-form layout="vertical" style="margin-top: 16px">
              <div>
                <h4>Column Mapping</h4>
                <p>
                  Enter the Excel column letter (e.g., A, B, AA) corresponding to each database
                  field.
                </p>
                <a-row :gutter="[16, 16]" style="margin-top: 10px">
                  <a-col :span="12" v-for="dbField in databaseFields" :key="dbField.key">
                    <a-form-item :label="dbField.label">
                      <a-input
                        v-model:value="columnMapping[dbField.key]"
                        style="width: 100%"
                        placeholder="e.g., D"
                        @change="
                          () =>
                            (columnMapping[dbField.key] = columnMapping[dbField.key]?.toUpperCase())
                        "
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
            </a-form>
          </div>
        </a-layout-content>
      </a-layout>
    </div>
    <div v-else>
      <p>No customer data provided.</p>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import { type Customer, type CustomerSettings } from '@IVC/types/MasterDataTypes/Customer'
import {
  addOrUpdateCustomerSettings,
  getCustomerSettings,
} from '@IVC/services/master-data/customer'

// Define types for Quotation Cycle
enum QuotationCycleType {
  DAILY_MONTHLY_AVG = 'daily_monthly_avg',
  MONTHLY = 'monthly',
  BY_TERM = 'by_term',
}

interface Props {
  visible: boolean
  customer: Customer | null
}

const props = defineProps<Props>()
const emit = defineEmits(['close', 'save'])

const selectedMenuKeys = ref<string[]>(['quotation']) // Default to the first category
const isSaving = ref(false)

// State for quotation cycle settings
const selectedQuotationCycle = ref<QuotationCycleType>(QuotationCycleType.DAILY_MONTHLY_AVG) // Default value
const termDays = ref<number | undefined>(undefined)

// State for Master Data Import
const columnMapping = ref<Record<string, string | undefined>>({})

const databaseFields = ref([
  { key: 'ProductCode', label: 'Product Code' },
  { key: 'ProductName', label: 'Product Name' },
  { key: 'ProductPpcNum', label: 'Product PPCNum' },
  { key: 'ProductHrc1', label: 'Product HRC1' },
  { key: 'ProductHrc3', label: 'Product HRC3' }, // Assuming HRC3 is needed
  { key: 'ProductPcm3', label: 'Product PCM3' },
  // 'Id' is usually auto-generated or handled differently, so not typically mapped from import file
])

const handleClose = () => {
  emit('close')
  // Consider resetting unsaved changes here if needed
}

const handleSaveSettings = async () => {
  isSaving.value = true
  if (!props.customer?.id) {
    message.error('Customer ID is missing. Cannot save settings.')
    isSaving.value = false
    return
  }

  try {
    // Prepare settings data to save
    // For now, we only have masterDataImportConfig.
    // Add quotation settings here once they are finalized in the DB schema.
    const settingsData: Omit<CustomerSettings, 'id' | 'createdAt' | 'updatedAt'> = {
      customerId: props.customer.id,
      masterDataImportMapping: columnMapping.value,
      // quotationCycle: selectedQuotationCycle.value, // Add when DB field exists
      // quotationTermDays: termDays.value, // Add when DB field exists
    }

    await addOrUpdateCustomerSettings(settingsData)
    message.success('Customer settings saved successfully!')
    emit('save', settingsData) // Emit event for parent to handle if needed
    emit('close') // Close modal after save
  } catch (err) {
    message.error(`Failed to save customer settings: ${(err as Error).message}`)
    console.error(err)
  } finally {
    isSaving.value = false
  }
}

// Watch for visibility to reset or load settings when modal opens/closes
watch(
  () => props.visible,
  async (isVisible) => {
    if (isVisible && props.customer) {
      selectedMenuKeys.value = ['quotation'] // Reset to default tab on open
      console.log(`Settings modal opened for ${props.customer.name}`)

      // Load existing settings for the customer
      isSaving.value = true // Use isSaving as a general loading indicator for settings
      try {
        const existingSettings = await getCustomerSettings(props.customer.id)
        if (existingSettings) {
          columnMapping.value = existingSettings.masterDataImportMapping || {}
          // selectedQuotationCycle.value = existingSettings.quotationCycle || QuotationCycleType.DAILY_MONTHLY_AVG;
          // termDays.value = existingSettings.quotationTermDays;
        } else {
          // No existing settings, reset to defaults
          columnMapping.value = {}
          // selectedQuotationCycle.value = QuotationCycleType.DAILY_MONTHLY_AVG;
          // termDays.value = undefined;
        }
      } catch (err) {
        message.error(`Failed to load customer settings: ${(err as Error).message}`)
        // Reset to defaults on error
        columnMapping.value = {}
        // selectedQuotationCycle.value = QuotationCycleType.DAILY_MONTHLY_AVG;
        // termDays.value = undefined;
      } finally {
        isSaving.value = false
      }
    } else {
      // Optionally reset when closing if settings are not saved immediately
      // selectedQuotationCycle.value = QuotationCycleType.DAILY_MONTHLY_AVG;
      // termDays.value = undefined;
    }
  },
)
</script>
