import { useSQLite } from '@IVC/hooks/useSQLite'
import type { ImportedProduct } from '@IVC/types/MasterDataTypes/Product'

const { executeQuery, tables } = useSQLite()

export async function findImportedProductByCodeAndCustomer(
  productCode: string,
  customerId: number,
): Promise<ImportedProduct | null> {
  const query = `
    SELECT id, product_code, product_name, product_ppc_num, product_hrc1, product_hrc3, product_pcm3, customer_id, imported_at, version_id
    FROM ${tables.imported_products}
    WHERE product_code = ? AND customer_id = ?
    ORDER BY version_id DESC, id DESC LIMIT 1; -- Get the latest version
  `
  const result = await executeQuery(query, [productCode, customerId])
  const row = result.result.resultRows?.[0]
  if (row) {
    return {
      id: row[0] as number,
      productCode: row[1] as string,
      productName: row[2] as string | undefined,
      productPpcNum: row[3] as string | undefined,
      productHrc1: row[4] as string | undefined,
      productHrc3: row[5] as string | undefined,
      productPcm3: row[6] as string | undefined,
      customerId: row[7] as number, // customerId will be present due to WHERE clause
      importedAt: row[8] as string,
      versionId: row[9] as number,
    }
  }
  return null
}

export async function addImportedProduct(
  productData: Omit<ImportedProduct, 'id' | 'importedAt'>,
): Promise<ImportedProduct> {
  const {
    productCode,
    productName,
    productPpcNum,
    productHrc1,
    productHrc3,
    productPcm3,
    customerId,
    versionId,
  } = productData

  const query = `
    INSERT INTO ${tables.imported_products}
      (product_code, product_name, product_ppc_num, product_hrc1, product_hrc3, product_pcm3, customer_id, version_id)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?);
  `
  const params = [
    productCode,
    productName ?? null,
    productPpcNum ?? null,
    productHrc1 ?? null,
    productHrc3 ?? null,
    productPcm3 ?? null,
    customerId ?? null,
    versionId,
  ]
  await executeQuery(query, params)
  const idResult = await executeQuery('SELECT last_insert_rowid() as id')
  const firstRow = idResult.result.resultRows?.[0]
  if (firstRow && typeof firstRow[0] === 'number') {
    const newProductId = firstRow[0]
    // Fetch the newly created product to get all its details including timestamp
    const newProductQuery = `SELECT id, product_code, product_name, product_ppc_num, product_hrc1, product_hrc3, product_pcm3, customer_id, imported_at, version_id FROM ${tables.imported_products} WHERE id = ?`
    const newProductResult = await executeQuery(newProductQuery, [newProductId])
    const newProductRow = newProductResult.result.resultRows?.[0]
    if (newProductRow) {
      return {
        id: newProductRow[0] as number,
        productCode: newProductRow[1] as string,
        productName: newProductRow[2] as string | undefined,
        productPpcNum: newProductRow[3] as string | undefined,
        productHrc1: newProductRow[4] as string | undefined,
        productHrc3: newProductRow[5] as string | undefined,
        productPcm3: newProductRow[6] as string | undefined,
        customerId: newProductRow[7] as number | undefined,
        importedAt: newProductRow[8] as string,
        versionId: newProductRow[9] as number,
      }
    }
  }
  throw new Error('Failed to retrieve ID or details after imported product insertion.')
}

export async function getNextVersionId(productCode: string, customerId: number): Promise<number> {
  const query = `
    SELECT MAX(version_id) as max_version
    FROM ${tables.imported_products}
    WHERE product_code = ? AND customer_id = ?
  `
  const result = await executeQuery(query, [productCode, customerId])
  const row = result.result.resultRows?.[0]
  const maxVersion = row?.[0] as number | null
  return (maxVersion ?? 0) + 1
}

export async function upsertImportedProduct(
  productData: Omit<ImportedProduct, 'id' | 'importedAt' | 'versionId'>,
): Promise<{ product: ImportedProduct; operation: 'inserted' | 'updated' }> {
  const {
    productCode,
    productName,
    productPpcNum,
    productHrc1,
    productHrc3,
    productPcm3,
    customerId,
  } = productData

  if (!customerId) {
    throw new Error('Customer ID is required to upsert a product.')
  }

  const existingProduct = await findImportedProductByCodeAndCustomer(productCode, customerId)

  if (existingProduct) {
    // Check if any data has actually changed
    const hasChanges =
      productName !== existingProduct.productName ||
      productPpcNum !== existingProduct.productPpcNum ||
      productHrc1 !== existingProduct.productHrc1 ||
      productHrc3 !== existingProduct.productHrc3 ||
      productPcm3 !== existingProduct.productPcm3

    if (!hasChanges) {
      // No changes, return existing product
      return { product: existingProduct, operation: 'updated' }
    }

    // Data has changed, create a new version
    const newVersionId = await getNextVersionId(productCode, customerId)
    const newProduct = await addImportedProduct({
      ...productData,
      versionId: newVersionId,
    })
    return { product: newProduct, operation: 'updated' }
  } else {
    // INSERT new product with version 1
    const newProduct = await addImportedProduct({
      ...productData,
      versionId: 1,
    })
    return { product: newProduct, operation: 'inserted' }
  }
}

export async function getImportedProductsByVersion(versionId: number): Promise<ImportedProduct[]> {
  const query = `
    SELECT id, product_code, product_name, product_ppc_num, product_hrc1, product_hrc3, product_pcm3, customer_id, imported_at, version_id
    FROM ${tables.imported_products}
    WHERE version_id = ? ORDER BY product_code ASC;
  `
  const result = await executeQuery(query, [versionId])
  if (!result.result.resultRows) return []
  return result.result.resultRows.map((row: unknown[]) => ({
    id: row[0] as number,
    productCode: row[1] as string,
    productName: row[2] as string | undefined,
    productPpcNum: row[3] as string | undefined,
    productHrc1: row[4] as string | undefined,
    productHrc3: row[5] as string | undefined,
    productPcm3: row[6] as string | undefined,
    customerId: row[7] as number | undefined,
    importedAt: row[8] as string,
    versionId: row[9] as number,
  }))
}

export async function getPreviousVersionProduct(
  productCode: string,
  customerId: number,
  currentVersionId: number,
): Promise<ImportedProduct | null> {
  const query = `
    SELECT id, product_code, product_name, product_ppc_num, product_hrc1, product_hrc3, product_pcm3, customer_id, imported_at, version_id
    FROM ${tables.imported_products}
    WHERE product_code = ? AND customer_id = ? AND version_id < ?
    ORDER BY version_id DESC LIMIT 1;
  `
  const result = await executeQuery(query, [productCode, customerId, currentVersionId])
  const row = result.result.resultRows?.[0]
  if (row) {
    return {
      id: row[0] as number,
      productCode: row[1] as string,
      productName: row[2] as string | undefined,
      productPpcNum: row[3] as string | undefined,
      productHrc1: row[4] as string | undefined,
      productHrc3: row[5] as string | undefined,
      productPcm3: row[6] as string | undefined,
      customerId: row[7] as number,
      importedAt: row[8] as string,
      versionId: row[9] as number,
    }
  }
  return null
}

export async function deleteImportedProduct(productId: number): Promise<void> {
  const query = `DELETE FROM ${tables.imported_products} WHERE id = ?;`
  await executeQuery(query, [productId])
}

export async function updateImportedProduct(
  product: Partial<ImportedProduct> & { id: number },
): Promise<void> {
  const { id, ...fieldsToUpdate } = product
  const fieldEntries = Object.entries(fieldsToUpdate).filter(([, value]) => value !== undefined)

  if (fieldEntries.length === 0) return

  const setClause = fieldEntries
    .map(([key]) => `${key.replace(/([A-Z])/g, '_$1').toLowerCase()} = ?`)
    .join(', ')
  const values = fieldEntries.map(([, value]) => value)

  const query = `UPDATE ${tables.imported_products} SET ${setClause} WHERE id = ?;`
  await executeQuery(query, [...values, id])
}

export async function getAllImportedProducts(): Promise<ImportedProduct[]> {
  const { executeQuery, tables } = useSQLite()
  const query = `
    SELECT id, product_code, product_name, product_ppc_num, product_hrc1, product_hrc3, product_pcm3, customer_id, imported_at, version_id
    FROM ${tables.imported_products}
    ORDER BY customer_id, version_id DESC, imported_at DESC;
  `
  const result = await executeQuery(query)

  if (!result.result.resultRows) return []
  return result.result.resultRows.map((row: unknown[]) => ({
    id: row[0] as number,
    productCode: row[1] as string,
    productName: row[2] as string | undefined,
    productPpcNum: row[3] as string | undefined,
    productHrc1: row[4] as string | undefined,
    productHrc3: row[5] as string | undefined,
    productPcm3: row[6] as string | undefined,
    customerId: row[7] as number | undefined,
    importedAt: row[8] as string,
    versionId: row[9] as number,
  }))
}

export async function getAllImportedProductsByCustomer(
  customerId: number,
): Promise<ImportedProduct[]> {
  const { executeQuery, tables } = useSQLite()
  const query = `
    SELECT id, product_code, product_name, product_ppc_num, product_hrc1, product_hrc3, product_pcm3, customer_id, imported_at, version_id
    FROM ${tables.imported_products}
    WHERE customer_id = ?
    ORDER BY version_id DESC, imported_at DESC;
  `
  const result = await executeQuery(query, [customerId])

  if (!result.result.resultRows) return []
  return result.result.resultRows.map((row: unknown[]) => ({
    id: row[0] as number,
    productCode: row[1] as string,
    productName: row[2] as string | undefined,
    productPpcNum: row[3] as string | undefined,
    productHrc1: row[4] as string | undefined,
    productHrc3: row[5] as string | undefined,
    productPcm3: row[6] as string | undefined,
    customerId: row[7] as number | undefined,
    importedAt: row[8] as string,
    versionId: row[9] as number,
  }))
}
