import type { DbId } from '@sqlite.org/sqlite-wasm'
import { sqlite3Worker1Promiser } from '@sqlite.org/sqlite-wasm'
import { ref } from 'vue'

const databaseConfig = {
  filename: 'file:mydb.sqlite3?vfs=opfs',
  tables: {
    test: {
      name: 'test_table',
      schema: `
        CREATE TABLE IF NOT EXISTS test_table (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `,
    },
    customers: {
      name: 'customers',
      schema: `
        CREATE TABLE IF NOT EXISTS customers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code TEXT NOT NULL,
          name TEXT NOT NULL,
          address TEXT,
          tax_number TEXT,
          phone TEXT,
          status TEXT NOT NULL DEFAULT 'new' CHECK(status IN ('new', 'active', 'inactive'))
        );
      `,
    },
    users: {
      name: 'users',
      schema: `
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username TEXT NOT NULL UNIQUE,
          password TEXT NOT NULL,
          email TEXT NOT NULL UNIQUE,
          role TEXT NOT NULL DEFAULT 'user',
          name TEXT NOT NULL,
          status BOOLEAN NOT NULL DEFAULT 1,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `,
    },
    customer_divisions: {
      name: 'customer_divisions',
      schema: `
        CREATE TABLE IF NOT EXISTS customer_divisions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code TEXT NOT NULL,
          name TEXT NOT NULL,
          description TEXT,
          customer_id INTEGER,
          status TEXT NOT NULL DEFAULT 'new' CHECK(status IN ('new', 'active', 'inactive')),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
        );
      `,
    },
    units: {
      // Added units table definition
      name: 'units',
      schema: `
        CREATE TABLE IF NOT EXISTS units (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code TEXT NOT NULL UNIQUE,
          name TEXT NOT NULL,
          status TEXT NOT NULL DEFAULT 'active' CHECK(status IN ('active', 'inactive'))
        );
      `,
    },
    categories: {
      // This is the new, simpler categories table (e.g., Storage Fee)
      name: 'categories',
      schema: `
        CREATE TABLE IF NOT EXISTS categories (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code TEXT NOT NULL UNIQUE,
          name TEXT NOT NULL,
          status TEXT NOT NULL DEFAULT 'active' CHECK(status IN ('active', 'inactive')) -- Added status
        );
      `,
    },
    customer_inbound: {
      name: 'customer_inbound',
      schema: `
        -- DROP TABLE IF EXISTS customer_inbound;

         CREATE TABLE IF NOT EXISTS customer_inbound (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          customer_id INTEGER,
          division_id INTEGER DEFAULT NULL,
          avr_as_num VARCHAR(255),
          avr_asln_num INTEGER,
          avr_assq_num INTEGER,
          avh_arv_ymd DATE,
          avr_prod_cod INTEGER,
          avd_prod_nam VARCHAR(255),
          avr_rtpc_qty INTEGER,
          prod_ppc_num INTEGER,
          prod_hrc1 VARCHAR(255),
          ai INTEGER,
          inner_master INTEGER,
          carton INTEGER,
          pcs INTEGER,
          inner_carton INTEGER,
          inner_pcs INTEGER,
          total_carton INTEGER,
          total_pcs INTEGER,
          total_ai INTEGER,
          total_m3 DOUBLE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
            );
      `,
    },
    customer_outbound: {
      name: 'customer_outbound',
      schema: `
        CREATE TABLE IF NOT EXISTS customer_outbound (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          customer_id INTEGER,
          division_id INTEGER DEFAULT NULL,
          spr_as_num VARCHAR(255),
          spr_asln_num INTEGER,
          spr_assq_num INTEGER,
          sph_ship_ymd DATE,
          sph_dlv_cod INTEGER,
          sph_dlv_nam1 VARCHAR(255),
          spr_prod_code INTEGER,
          spd_prod_nam VARCHAR(255),
          spr_rtpc_qty INTEGER,
          prod_ppc_num INTEGER,
          prod_hrc1 VARCHAR(255),
          ai INTEGER,
          inner_master INTEGER,
          carton INTEGER,
          pcs INTEGER,
          inner_carton INTEGER,
          inner_pcs INTEGER,
          total_carton INTEGER,
          total_pcs INTEGER,
          total_ai INTEGER,
          total_m3 DOUBLE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
        );
      `,
    },
    workout: {
      // This table replaces the old detailed categories table
      name: 'workout',
      schema: `
        CREATE TABLE IF NOT EXISTS workout (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code TEXT NOT NULL UNIQUE,
          name TEXT NOT NULL,
          unit_id INTEGER,
          unit_price REAL,
          unit_price_sign TEXT DEFAULT '+' CHECK(unit_price_sign IN ('+', '-')),
          unit_price_unit TEXT DEFAULT 'VND' CHECK(unit_price_unit IN ('VND', 'PERCENT')), -- Simplified options
          unit_price_is_percentage BOOLEAN DEFAULT 0, -- 0 for false, 1 for true
          status TEXT NOT NULL DEFAULT 'active' CHECK(status IN ('active', 'inactive')),
          vat REAL NOT NULL DEFAULT 0.0, -- VAT percentage, e.g., 0.1 for 10%
          category_id INTEGER NOT NULL, -- Link to the new simple categories table
          calculation_type TEXT NOT NULL DEFAULT 'Manual' CHECK(calculation_type IN ('Manual', 'Link', 'Auto')),
          linked_workout_id INTEGER, -- Link to another workout if calculation_type is 'Link' or stores ID for 'Auto' if preferred
          linked_auto_focus_code TEXT, -- Stores the code of the AutoFocus item if calculation_type is 'Auto'
          debit_code_id INTEGER, -- Link to debit_codes table
          FOREIGN KEY (unit_id) REFERENCES units(id),
          FOREIGN KEY (category_id) REFERENCES categories(id),
          FOREIGN KEY (linked_workout_id) REFERENCES workout(id) ON DELETE SET NULL, -- Or ON DELETE CASCADE, depending on desired behavior
          FOREIGN KEY (debit_code_id) REFERENCES debit_codes(id) ON DELETE SET NULL
        );
      `,
    },
    customer_workout: {
      // Renamed from customer_category
      name: 'customer_workout',
      schema: `
        CREATE TABLE IF NOT EXISTS customer_workout (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          customer_id INTEGER,
          workout_id INTEGER, -- Changed from category_id
          unit_price REAL,
          discount_percentage REAL, -- Nullable, overrides category discount if set
          FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
          FOREIGN KEY (workout_id) REFERENCES workout(id) ON DELETE CASCADE, -- FK to workout table
          UNIQUE(customer_id, workout_id) -- Ensure unique combination
        );
      `,
    },
    customer_settings: {
      name: 'customer_settings',
      schema: `
        CREATE TABLE IF NOT EXISTS customer_settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          customer_id INTEGER NOT NULL UNIQUE, -- Each customer has one settings entry
          master_data_import_mapping TEXT, -- Stores JSON string of column mappings
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
        );
      `,
    },

    imported_products: {
      name: 'imported_products',
      schema: `
        CREATE TABLE IF NOT EXISTS imported_products (
          id INTEGER PRIMARY KEY AUTOINCREMENT, -- Internal DB ID
          product_code TEXT NOT NULL, -- From Excel
          product_name TEXT,
          product_ppc_num TEXT, -- Using TEXT to be flexible with data types from Excel
          product_hrc1 TEXT,
          product_hrc3 TEXT,
          product_pcm3 TEXT,
          customer_id INTEGER, -- To associate with a specific customer if needed
          imported_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Timestamp of import
          version_id INTEGER NOT NULL DEFAULT 1, -- Version tracking for product data changes
          FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL, -- Or CASCADE
          UNIQUE(product_code, version_id, customer_id) -- Ensure a product is unique within a version for a customer
                  );
      `,
    },
    customer_import_mapping: {
      name: 'customer_import_mapping',
      schema: `
        CREATE TABLE IF NOT EXISTS customer_import_mapping (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          customer_id INTEGER NOT NULL,
          import_type TEXT NOT NULL CHECK(import_type IN ('inbound', 'outbound')),
          excel_column TEXT NOT NULL,
          db_column TEXT NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
          UNIQUE(customer_id, import_type, excel_column)
        );
      `,
    },
    company_info: {
      name: 'company_info',
      schema: `
        CREATE TABLE IF NOT EXISTS company_info (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          address TEXT NOT NULL,
          phone TEXT NOT NULL,
          tax_code TEXT NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `,
    },
    company_stores: {
      name: 'company_stores',
      schema: `
        CREATE TABLE IF NOT EXISTS company_stores (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          location TEXT NOT NULL,
          manager TEXT NOT NULL,
          phone TEXT NOT NULL,
          status TEXT NOT NULL DEFAULT 'active',
          is_default BOOLEAN NOT NULL DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `,
    },
    invoices: {
      name: 'invoices',
      schema: `
        -- DROP TABLE IF EXISTS invoices;

        CREATE TABLE IF NOT EXISTS invoices (
          id TEXT PRIMARY KEY,
          customer_id INTEGER,
          customer_division_id INTEGER,
          total_amount REAL NOT NULL DEFAULT 0,
          status TEXT NOT NULL DEFAULT 'DRAFT' CHECK(status IN ('DRAFT', 'PENDING', 'APPROVED', 'REJECTED', 'PAID', 'OVERDUE')),
          notes TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (customer_id) REFERENCES customers(id),
          FOREIGN KEY (customer_division_id) REFERENCES customer_divisions(id)
        );
      `,
    },
    invoice_items: {
      name: 'invoice_items',
      schema: `
        -- DROP TABLE IF EXISTS invoice_items;

        CREATE TABLE IF NOT EXISTS invoice_items (
          id TEXT PRIMARY KEY,
          invoice_id TEXT NOT NULL,
          category_id INTEGER NOT NULL,
          workout_id INTEGER,
          unit_id INTEGER,
          unit_price REAL NOT NULL DEFAULT 0,
          quantity REAL NOT NULL DEFAULT 0,
          amount REAL NOT NULL DEFAULT 0,
          vat REAL NOT NULL DEFAULT 0,
          vat_amount REAL NOT NULL DEFAULT 0,
          debit_code_id INTEGER,
          description TEXT,
          status TEXT NOT NULL DEFAULT 'DRAFT' CHECK(status IN ('DRAFT', 'PENDING', 'APPROVED', 'REJECTED', 'PAID', 'OVERDUE')),
          created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
          is_edited BOOLEAN DEFAULT 0,
          FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
          FOREIGN KEY (category_id) REFERENCES categories(id),
          FOREIGN KEY (workout_id) REFERENCES workout(id),
          FOREIGN KEY (unit_id) REFERENCES units(id),
          FOREIGN KEY (debit_code_id) REFERENCES debit_codes(id)
        );
      `,
    },
    debit_codes: {
      name: 'debit_codes',
      schema: `
        CREATE TABLE IF NOT EXISTS debit_codes (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code TEXT NOT NULL,
          name TEXT NOT NULL,
          description TEXT NOT NULL,
          is_active BOOLEAN NOT NULL DEFAULT 1,
          is_used BOOLEAN NOT NULL DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `,
    },
    auto_focus: {
      name: 'auto_focus',
      schema: `
        CREATE TABLE IF NOT EXISTS auto_focus (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code TEXT NOT NULL UNIQUE,
          name TEXT NOT NULL,
          note TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `,
    },
    auto_focus_customers: {
      name: 'auto_focus_customers',
      schema: `
        CREATE TABLE IF NOT EXISTS auto_focus_customers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          auto_focus_id INTEGER NOT NULL,
          customer_id INTEGER NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (auto_focus_id) REFERENCES auto_focus (id) ON DELETE CASCADE,
          FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE,
          UNIQUE(auto_focus_id, customer_id)
        );
      `,
    },
    auto_function: {
      name: 'auto_function',
      schema: `
        CREATE TABLE IF NOT EXISTS auto_function (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code TEXT NOT NULL UNIQUE,
          name TEXT NOT NULL,
          note TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `,
    },
    auto_function_customers: {
      name: 'auto_function_customers',
      schema: `
        CREATE TABLE IF NOT EXISTS auto_function_customers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          auto_function_id INTEGER NOT NULL,
          customer_id INTEGER NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (auto_function_id) REFERENCES auto_function (id) ON DELETE CASCADE,
          FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE,
          UNIQUE(auto_function_id, customer_id)
        );
      `,
    },
    auto_function_customer_detail: {
      name: 'auto_function_customer_detail',
      schema: `
        CREATE TABLE IF NOT EXISTS auto_function_customer_detail (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          auto_function_id INTEGER NOT NULL,
          customer_id INTEGER NOT NULL,
          division_id INTEGER DEFAULT NULL,
          year_month TEXT NOT NULL, -- Format: 'YYYY-MM' (e.g., '2024-12')
          value DOUBLE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (auto_function_id) REFERENCES auto_function (id) ON DELETE CASCADE,
          FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE,
          FOREIGN KEY (division_id) REFERENCES customer_divisions (id) ON DELETE SET NULL,
          UNIQUE(auto_function_id, customer_id, division_id, year_month)
        );
      `,
    },
    cod: {
      name: 'cod',
      schema: `
        CREATE TABLE IF NOT EXISTS cod (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code TEXT NOT NULL UNIQUE,
          name TEXT,
          address TEXT,
          code_type TEXT NOT NULL CHECK (code_type IN ('Store', 'Customer')),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `,
    },
    stock: {
      name: 'stock',
      schema: `
        CREATE TABLE IF NOT EXISTS stock (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          product_code TEXT NOT NULL,
          customer_id INTEGER NOT NULL,
          avr_rtpc_qty INTEGER DEFAULT 0,
          avr_rtpc_qty_temp INTEGER DEFAULT 0,
          operation_source TEXT NOT NULL DEFAULT 'Manual' CHECK(operation_source IN ('Manual', 'Inbound', 'Outbound')),
          location TEXT,
          is_active BOOLEAN NOT NULL DEFAULT 1,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
        );
      `,
    },
    stock_audit_batches: {
      name: 'stock_audit_batches',
      schema: `
        CREATE TABLE IF NOT EXISTS stock_audit_batches (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          operation_type TEXT NOT NULL CHECK(operation_type IN ('create', 'update', 'delete', 'bulk_import', 'bulk_update')),
          operation_source TEXT NOT NULL CHECK(operation_source IN ('Manual', 'Inbound', 'Outbound', 'Import', 'System')),
          performed_by_user_id INTEGER,
          notes TEXT,
          affected_count INTEGER DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (performed_by_user_id) REFERENCES users(id) ON DELETE SET NULL
        );
      `,
    },
    stock_audit_history: {
      name: 'stock_audit_history',
      schema: `
        CREATE TABLE IF NOT EXISTS stock_audit_history (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          audit_batch_id INTEGER NOT NULL,
          stock_id INTEGER NOT NULL,
          product_code TEXT NOT NULL,
          customer_id INTEGER NOT NULL,
          field_name TEXT NOT NULL,
          old_value TEXT,
          new_value TEXT,
          change_reason TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (audit_batch_id) REFERENCES stock_audit_batches(id) ON DELETE CASCADE,
          FOREIGN KEY (stock_id) REFERENCES stock(id) ON DELETE CASCADE,
          FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
        );
      `,
    },
    workout_customer_charges: {
      name: 'workout_customer_charges',
      schema: `
        CREATE TABLE IF NOT EXISTS workout_customer_charges (
          id TEXT PRIMARY KEY,
          customer_id INTEGER NOT NULL,
          division_id INTEGER,
          category_id INTEGER NOT NULL,
          workout_id INTEGER NOT NULL,
          year INTEGER NOT NULL,
          month_1 INTEGER,
          month_2 INTEGER,
          month_3 INTEGER,
          month_4 INTEGER,
          month_5 INTEGER,
          month_6 INTEGER,
          month_7 INTEGER,
          month_8 INTEGER,
          month_9 INTEGER,
          month_10 INTEGER,
          month_11 INTEGER,
          month_12 INTEGER,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
          FOREIGN KEY (division_id) REFERENCES customer_divisions(id) ON DELETE SET NULL,
          FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
          FOREIGN KEY (workout_id) REFERENCES workout(id) ON DELETE CASCADE
        );
      `,
    },
    workout_customer_charge: {
      name: 'workout_customer_charge',
      schema: `
        CREATE TABLE IF NOT EXISTS workout_customer_charge (
          id TEXT PRIMARY KEY,
          customer_id INTEGER NOT NULL,
          division_id INTEGER NULL,
          category_id TEXT NOT NULL,
          workout_id TEXT NOT NULL,
          year INTEGER NOT NULL,
          month_1 INTEGER NULL,
          month_2 INTEGER NULL,
          month_3 INTEGER NULL,
          month_4 INTEGER NULL,
          month_5 INTEGER NULL,
          month_6 INTEGER NULL,
          month_7 INTEGER NULL,
          month_8 INTEGER NULL,
          month_9 INTEGER NULL,
          month_10 INTEGER NULL,
          month_11 INTEGER NULL,
          month_12 INTEGER NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `,
    },
  },
} as const

export function useSQLite() {
  const isLoading = ref(false)
  const error = ref<Error | null>(null)
  const isInitialized = ref(false)

  let promiser: ReturnType<typeof sqlite3Worker1Promiser> | null = null
  let dbId: string | null = null

  async function insertDefaultCustomers() {
    if (!promiser || !dbId) return
    // Check if table is empty before inserting default data
    const checkEmptyResult = await promiser('exec', {
      dbId,
      sql: `SELECT COUNT(*) FROM ${databaseConfig.tables.customers.name};`,
      returnValue: 'resultRows',
    })

    if (checkEmptyResult.type === 'error') {
      console.error('Error checking if customers table is empty:', checkEmptyResult.result.message)
      return
    }

    if (checkEmptyResult.result.resultRows?.[0]?.[0] === 0) {
      // Now TypeScript knows result is the success type
      const defaultCustomers = [
        {
          code: 'MUJ',
          name: 'Muji',
          address: '123 Main St',
          tax_number: 'TX123',
          phone: '555-0101',
          status: 'active',
        },
        {
          code: 'NIT',
          name: 'Nitori',
          address: '456 Oak Ave',
          tax_number: 'TX456',
          phone: '555-0102',
          status: 'new',
        },
      ]
      for (const customer of defaultCustomers) {
        await promiser('exec', {
          dbId,
          sql: `INSERT INTO ${databaseConfig.tables.customers.name} (code, name, address, tax_number, phone, status) VALUES (?, ?, ?, ?, ?, ?);`,
          bind: [
            customer.code,
            customer.name,
            customer.address,
            customer.tax_number,
            customer.phone,
            customer.status,
          ],
        })
      }
      console.log('Default customers inserted.')
    }
  }

  async function insertDefaultCustomerDivisions() {
    if (!promiser || !dbId) return
    // Check if customer_divisions table is empty
    const checkDivisionsEmptyResult = await promiser('exec', {
      dbId,
      sql: `SELECT COUNT(*) FROM ${databaseConfig.tables.customer_divisions.name};`,
      returnValue: 'resultRows',
    })

    if (checkDivisionsEmptyResult.type === 'error') {
      console.error(
        'Error checking if customer_divisions table is empty:',
        checkDivisionsEmptyResult.result.message,
      )
      return
    }

    if (checkDivisionsEmptyResult.result.resultRows?.[0]?.[0] === 0) {
      // Now TypeScript knows result is the success type
      // Assuming default customers have IDs 1 and 2 after insertion
      const defaultDivisions = [
        {
          code: 'DC',
          name: 'DC',
          description: 'DC division of Muji',
          customer_id: 1,
          status: 'active',
        },
        {
          code: 'EC',
          name: 'EC',
          description: 'EC division of Muji',
          customer_id: 1,
          status: 'active',
        },
      ]
      for (const division of defaultDivisions) {
        await promiser('exec', {
          dbId,
          sql: `INSERT INTO ${databaseConfig.tables.customer_divisions.name} (code, name, description, customer_id, status) VALUES (?, ?, ?, ?, ?);`,
          bind: [
            division.code,
            division.name,
            division.description,
            division.customer_id,
            division.status,
          ],
        })
      }
      console.log('Default customer divisions inserted.')
    }
  }

  async function insertDefaultWorkouts() {
    if (!promiser || !dbId) return

    const checkWorkoutsEmptyResult = await promiser('exec', {
      dbId,
      sql: `SELECT COUNT(*) FROM ${databaseConfig.tables.workout.name};`,
      returnValue: 'resultRows',
    })

    if (checkWorkoutsEmptyResult.type === 'error') {
      console.error(
        'Error checking if workout table is empty:',
        checkWorkoutsEmptyResult.result.message,
      )
      return
    }

    if (checkWorkoutsEmptyResult.result.resultRows?.[0]?.[0] === 0) {
      // --- Helper Mappings (ASSUMES sequential IDs from default data insertion) ---
      // Unit Codes to Assumed IDs (based on order in insertDefaultUnits)
      const unitCodeToId: Record<string, number> = {
        M2: 1,
        SSCPM: 2,
        MONTH: 3,
        LRPT: 4,
        M2PM: 5,
        LPD: 6,
        '20FT': 7,
        '40FT': 8,
        LCLPAS: 9,
        CTN: 10,
        PCS: 11,
        LBL: 12,
        SET: 13,
        CBM: 14,
        MH: 15,
        MD: 16,
        MS: 17,
        TTC: 18,
        M3: 19,
        VT: 20,
        TRU: 21,
        ORD: 22,
        ATT: 23,
        PLT: 24, // Added from previous context, ensure it's in defaultUnits if used
        BOX: 25, // Added from previous context
        BAG: 26, // Added from previous context
      }

      // Category Codes to Assumed IDs (based on order in insertDefaultCategories)
      const categoryCodeToId: Record<string, number> = {
        SF: 1,
        INB: 2,
        OBTS: 3,
        OBTC: 4,
        OBFEC: 5,
        EHCU: 6, // This code is used multiple times for different categories in your list.
        // I'll map to the first one. You might need more specific category codes.
        SCEPSR: 7,
        SCEPVR: 8,
        STCVAS: 9,
        SYSC: 10,
        OTCWP: 11,
        DCSORA: 12,
        // EHCU_DCSORA: 13, // Example if you need a more specific EHCU
        DCVINCMD9: 13, // Adjusted ID due to EHCU reuse
        // EHCU_DCVINCMD9: 15,
        HOTW: 14, // Adjusted ID
        DCMMD2: 15,
        // EHCU_DCMMD2: 18,
        DCLTTD1: 16,
        // EHCU_DCLTTD1: 20,
        // HOTW_DCLTTD1: 21, // Example for specific HOTW
        DTSHCM: 17,
        DCAFHHCM: 18,
        VASVSIP2: 19,
      }

      // Debit Code Codes to Assumed IDs (based on order in insertDefaultDebitCodes)
      const debitCodeToId: Record<string, number> = {
        W1H: 1, // PHI DICH VU
        TL1: 2, // DAN NHAN
        WB1: 3, // BOC XEP KHO
        OW1: 4, // LUU KHO
        WM1: 5, // QUAN LY KHO
        WS1: 6, // PHI HE THONG
        TW1: 7, // VAN CHUYEN
      }

      const defaultWorkouts = [
        // Category: Storage Fee
        {
          code: 'SF_FIXED_SPACE_FY1',
          name: 'Fixed Space for 1st fiscal year (2023-2024) – by 6,038 m2',
          unit_id: unitCodeToId['M2'],
          unit_price: 110000,
          vat: 0.08,
          category_id: categoryCodeToId['SF'],
          debit_code_id: debitCodeToId['OW1'], // LUU KHO
        },
        {
          code: 'SF_MGMT_FEE_FIXED',
          name: 'Management fee',
          unit_id: unitCodeToId['SSCPM'],
          unit_price_is_percentage: true,
          unit_price: 6,
          unit_price_unit: 'PERCENT',
          vat: 0.08,
          category_id: categoryCodeToId['SF'],
          calculation_type: 'Link',
          linked_workout_id: 1,
          debit_code_id: debitCodeToId['WM1'], // QUAN LY KHO
        }, // Assuming SF_FIXED_SPACE_FY1 is ID 1
        {
          code: 'SF_EXTRA_OFFICE',
          name: 'Extra storage charge (office area by 133 m2)',
          unit_id: unitCodeToId['MONTH'],
          unit_price: 14000000,
          vat: 0.08,
          category_id: categoryCodeToId['SF'],
          debit_code_id: debitCodeToId['OW1'], // LUU KHO
        },
        {
          code: 'SF_HANDLING_EXTRA',
          name: 'Handling for extra storage charge (occurring & parking lot)',
          unit_id: unitCodeToId['MONTH'],
          unit_price: 9000000,
          vat: 0.08,
          category_id: categoryCodeToId['SF'],
          debit_code_id: debitCodeToId['WB1'], // BOC XEP KHO
        },
        {
          code: 'SF_EXPANSION_MAT',
          name: 'Expansion Material',
          unit_id: unitCodeToId['MONTH'],
          unit_price: 66462556,
          vat: 0.08,
          category_id: categoryCodeToId['SF'],
          debit_code_id: debitCodeToId['OW1'], // LUU KHO
        },

        // Category: Storage charge - for extra pallet storage on rack
        {
          code: 'SCEPSR_LRPT',
          name: 'Storage charge - for extra pallet storage on rack',
          unit_id: unitCodeToId['LRPT'],
          unit_price: 4840000,
          vat: 0.08,
          category_id: categoryCodeToId['SCEPSR'],
          debit_code_id: debitCodeToId['OW1'], // LUU KHO
        },
        {
          code: 'SCEPSR_M2M',
          name: 'Storage charge - for extra pallet storage on rack',
          unit_id: unitCodeToId['M2PM'],
          unit_price: 110000,
          vat: 0.08,
          category_id: categoryCodeToId['SCEPSR'],
          debit_code_id: debitCodeToId['OW1'], // LUU KHO
        },
        {
          code: 'SCEPSR_MGMT_FEE',
          name: 'Management fee',
          unit_id: unitCodeToId['SSCPM'],
          unit_price_is_percentage: true,
          unit_price: 0,
          unit_price_unit: 'PERCENT',
          vat: 0.08,
          category_id: categoryCodeToId['SCEPSR'],
          debit_code_id: debitCodeToId['WM1'], // QUAN LY KHO
        }, // Original data shows 0 for unit price, assuming 6% of above item charge means it's linked or calculated differently in practice. For default, setting unit_price to 0.

        // Category: Storage charge - for extra pallet VNA rack
        {
          code: 'SCEPVR_L10D',
          name: 'Storage charge - for extra pallet VNA rack',
          unit_id: unitCodeToId['LRPT'],
          unit_price: 3850000,
          vat: 0.08,
          category_id: categoryCodeToId['SCEPVR'],
          debit_code_id: debitCodeToId['OW1'], // LUU KHO
        }, // Assuming "01 Line / 10 days" maps to LRPT
        // { code: 'SCEPVR_M2M', name: 'Storage charge - for extra pallet storage on rack', unit_id: unitCodeToId['M2PM'], category_id: categoryCodeToId['SCEPVR'] }, // This line was in old data, not in new table for VNA
        {
          code: 'SCEPVR_MGMT_FEE',
          name: 'Management fee',
          unit_id: unitCodeToId['SSCPM'],
          unit_price_is_percentage: true,
          unit_price: 0.06,
          unit_price_unit: 'PERCENT',
          vat: 0.08,
          category_id: categoryCodeToId['SCEPVR'],
          debit_code_id: debitCodeToId['WM1'], // QUAN LY KHO
        },

        // Category: Inbound
        {
          code: 'INB_DEV_20FT',
          name: 'De-vanning',
          unit_id: unitCodeToId['20FT'],
          unit_price: 960000,
          vat: 0.08,
          category_id: categoryCodeToId['INB'],
          debit_code_id: debitCodeToId['WB1'], // BOC XEP KHO
        },
        {
          code: 'INB_DEV_40FT',
          name: 'De-vanning',
          unit_id: unitCodeToId['40FT'],
          unit_price: 1369000,
          vat: 0.08,
          category_id: categoryCodeToId['INB'],
          debit_code_id: debitCodeToId['WB1'], // BOC XEP KHO
        },
        {
          code: 'INB_DEV_LCL_CBM',
          name: 'De-vanning LCL/Air shipment (by CBM)',
          unit_id: unitCodeToId['CBM'],
          unit_price: 27400,
          vat: 0.08,
          category_id: categoryCodeToId['INB'],
          debit_code_id: debitCodeToId['WB1'], // BOC XEP KHO
        }, // Changed unit to CBM
        {
          code: 'INB_HFA_FURN_CTN',
          name: 'Inbound (Hfa & furniture)',
          unit_id: unitCodeToId['CTN'],
          unit_price: 2200,
          vat: 0.08,
          category_id: categoryCodeToId['INB'],
          debit_code_id: debitCodeToId['WB1'], // BOC XEP KHO
        },
        {
          code: 'INB_HFA_PCS',
          name: 'Inbound (Hfa by pcs)',
          unit_id: unitCodeToId['PCS'],
          unit_price: 2000,
          vat: 0.08,
          category_id: categoryCodeToId['INB'],
          debit_code_id: debitCodeToId['WB1'], // BOC XEP KHO
        },
        {
          code: 'INB_SUBLBL_PRINTPASTE',
          name: 'Sub-label labeling (including printing label + pasting)',
          unit_id: unitCodeToId['LBL'],
          unit_price: 1800,
          vat: 0.08,
          category_id: categoryCodeToId['INB'],
          debit_code_id: debitCodeToId['W1H'], // PHI DICH VU
        },
        {
          code: 'INB_LBL_PASTE',
          name: 'Label pasting (sub-label, energy label,etc,….);',
          unit_id: unitCodeToId['LBL'],
          unit_price: 1000,
          vat: 0.08,
          category_id: categoryCodeToId['INB'],
          debit_code_id: debitCodeToId['W1H'], // PHI DICH VU
        },
        {
          code: 'INB_INPUT_INSTR',
          name: 'Inputting instruction (manual) into product box (AI)',
          unit_id: unitCodeToId['SET'],
          unit_price: 10000,
          vat: 0.08,
          category_id: categoryCodeToId['INB'],
        },

        // Category: Outbound to Store (Shipment for Store)
        {
          code: 'OBTS_PICK_CTN',
          name: 'Picking',
          unit_id: unitCodeToId['CTN'],
          unit_price: 3900,
          vat: 0.08,
          category_id: categoryCodeToId['OBTS'],
        },
        {
          code: 'OBTS_PICK_PCS',
          name: 'Picking',
          unit_id: unitCodeToId['PCS'],
          unit_price: 2200,
          vat: 0.08,
          category_id: categoryCodeToId['OBTS'],
        },
        {
          code: 'OBTS_LOAD_CBM',
          name: 'Loading',
          unit_id: unitCodeToId['CBM'],
          unit_price: 27400,
          vat: 0.08,
          category_id: categoryCodeToId['OBTS'],
        },
        {
          code: 'OBTS_VAS_PACK_S',
          name: 'VAS - Packing Material - Size S',
          unit_id: unitCodeToId['CTN'],
          unit_price: 35000,
          vat: 0.08,
          category_id: categoryCodeToId['OBTS'],
        },
        {
          code: 'OBTS_VAS_PACK_M',
          name: 'VAS - Packing Material - Size M',
          unit_id: unitCodeToId['CTN'],
          unit_price: 40000,
          vat: 0.08,
          category_id: categoryCodeToId['OBTS'],
        },
        {
          code: 'OBTS_MIXSEAL_GT',
          name: 'Mix carton seal by Green Tape',
          unit_id: unitCodeToId['CTN'],
          unit_price: 750,
          vat: 0.08,
          category_id: categoryCodeToId['OBTS'],
        },

        // Category: Outbound to Customer (Shipment for Customer)
        {
          code: 'OBTC_PICK_CTN',
          name: 'Picking',
          unit_id: unitCodeToId['CTN'],
          unit_price: 3900,
          vat: 0.08,
          category_id: categoryCodeToId['OBTC'],
          debit_code_id: debitCodeToId['WB1'], // BOC XEP KHO
        },
        {
          code: 'OBTC_PICK_PCS',
          name: 'Picking',
          unit_id: unitCodeToId['PCS'],
          unit_price: 2200,
          vat: 0.08,
          category_id: categoryCodeToId['OBTC'],
          debit_code_id: debitCodeToId['WB1'], // BOC XEP KHO
        },
        {
          code: 'OBTC_SHIPLBL_PRNTPASTE',
          name: 'Shipping label labeling (including printing + pasting)',
          unit_id: unitCodeToId['LBL'],
          unit_price: 1800,
          vat: 0.08,
          category_id: categoryCodeToId['OBTC'],
          debit_code_id: debitCodeToId['W1H'], // PHI DICH VU
        },
        {
          code: 'OBTC_LOAD_CBM',
          name: 'Loading',
          unit_id: unitCodeToId['CBM'],
          unit_price: 27400,
          vat: 0.08,
          category_id: categoryCodeToId['OBTC'],
          debit_code_id: debitCodeToId['TW1'], // VAN CHUYEN
        },

        // Category: Outbound for EC (Courier service)
        {
          code: 'OBFEC_PICK_CTN',
          name: 'Picking',
          unit_id: unitCodeToId['CTN'],
          unit_price: 3900,
          vat: 0.08,
          category_id: categoryCodeToId['OBFEC'],
        },
        {
          code: 'OBFEC_PICK_PCS',
          name: 'Picking',
          unit_id: unitCodeToId['PCS'],
          unit_price: 2200,
          vat: 0.08,
          category_id: categoryCodeToId['OBFEC'],
        },
        {
          code: 'OBFEC_LBL_PASTE',
          name: 'Labeling (only pasting)',
          unit_id: unitCodeToId['LBL'],
          unit_price: 1000,
          vat: 0.08,
          category_id: categoryCodeToId['OBFEC'],
        },

        // Category: Stocktaking Charge - VAS
        {
          code: 'STCVAS_EXTRA_HAND_CTN',
          name: 'Extra handling Charge for Stocktaking charge at WH - Vsip2-Block 18.',
          unit_id: unitCodeToId['CTN'],
          unit_price: 550,
          vat: 0.08,
          category_id: categoryCodeToId['STCVAS'],
        },
        {
          code: 'STCVAS_EXTRA_HAND_PCS',
          name: 'Extra handling Charge for Stocktaking charge at WH - Vsip2-Block 18.',
          unit_id: unitCodeToId['PCS'],
          unit_price: 243,
          vat: 0.08,
          category_id: categoryCodeToId['STCVAS'],
        },

        // Category: System charge
        {
          code: 'SYSC_RUNNING',
          name: 'running',
          unit_id: unitCodeToId['MONTH'],
          unit_price: 2500000,
          vat: 0.08,
          category_id: categoryCodeToId['SYSC'],
          debit_code_id: debitCodeToId['WS1'], // PHI HE THONG
        },

        // Category: OT charge (apply for warehouse personel )
        {
          code: 'OTCWP_EXCL_NIGHT_SUN_HOL',
          name: '* excluding night shift (after 22:00), Sunday & Holidays',
          unit_id: unitCodeToId['MH'],
          unit_price: 150000,
          vat: 0.08,
          category_id: categoryCodeToId['OTCWP'],
        },

        // Category: Delivery charge from Nitori DC Block 18 - Sora Gardens (Thu Dau Mot, Bình Dương)
        {
          code: 'DCSORA_TO_SORA_TRIP',
          name: 'Delivery charge from Nitori DC Block 18 - Sora Garden (Thu Dau Mot, Binh duong). DELIVERY TRIP',
          unit_id: unitCodeToId['TTC'],
          unit_price: 6150000,
          vat: 0.08,
          category_id: categoryCodeToId['DCSORA'],
        },
        {
          code: 'DCSORA_FROM_SORA_TRIP',
          name: 'Delivery charge from Sora Gardens - Nitori DC Block 18 (Thu Dau Mot, Binh duong). RETURN TRIP',
          unit_id: unitCodeToId['TTC'],
          unit_price: 0,
          vat: 0.08,
          category_id: categoryCodeToId['DCSORA'],
        }, // Assuming 0 as price is "-"

        // Category: Extra handling charge for unloading (associated with Sora) - Assuming this EHCU is for DCSORA
        {
          code: 'EHCU_SORA_10_19',
          name: '"* if requested, working hour: 10:00 -19:00, excuding on Sunday & Holidays."',
          unit_id: unitCodeToId['MD'],
          unit_price: 500000,
          vat: 0.08,
          category_id: categoryCodeToId['EHCU'],
        },

        // Category: Delivery charge from Nitori DC Block 18 - Vincom Mega Mall (Dist 9, Thu Duc city)
        {
          code: 'DCVINCMD9_TO_VINCOM_TRIP',
          name: 'Delivery charge from Nitori DC Block 18 - Vincom Mega Mall (Dist 9, Thu Duc city). DELIVERY TRIP',
          unit_id: unitCodeToId['TTC'],
          unit_price: 11466000,
          vat: 0.08,
          category_id: categoryCodeToId['DCVINCMD9'],
        },
        {
          code: 'DCVINCMD9_FROM_VINCOM_TRIP',
          name: 'Delivery charge from Vincom Mega Mall (Dist 9, Thu Duc city ) - Nitori DC Block 18 (Thu Dau Mot, Binh duong). RETURN TRIP',
          unit_id: unitCodeToId['TTC'],
          unit_price: 0,
          vat: 0.08,
          category_id: categoryCodeToId['DCVINCMD9'],
        },

        // Category: Extra handling charge for unloading (associated with Vincom D9) - Assuming this EHCU is for DCVINCMD9
        {
          code: 'EHCU_VINCMD9_22_02',
          name: '* if requested, working hour: 22:00 -02:00, excluding on Sunday & Holidays',
          unit_id: unitCodeToId['MS'],
          unit_price: 450000,
          vat: 0.08,
          category_id: categoryCodeToId['EHCU'],
        },

        // Category: Handling for overtime working (This seems general, might need specific category or link to delivery)
        {
          code: 'HOTW_GEN_AFTER_24',
          name: '* charge after 24:00. Min charge by 01 manhour.',
          unit_id: unitCodeToId['MH'],
          unit_price: 150000,
          vat: 0.08,
          category_id: categoryCodeToId['HOTW'],
        },

        // Category: Delivery charge from Nitori DC - Mega Mall (Dist 2,Thao Dien).
        {
          code: 'DCMMD2_TO_MMD2_TRIP',
          name: 'Delivery charge from Nitori DC Block 18 - Mega Mall (Dist 2,Thao Dien, Ho Chi Minh City) DELIVERY TRIP',
          unit_id: unitCodeToId['TTC'],
          unit_price: 17724000,
          vat: 0.08,
          category_id: categoryCodeToId['DCMMD2'],
        },
        {
          code: 'DCMMD2_FROM_MMD2_TRIP',
          name: 'Delivery charge from Mega Mall (Dist 2,Thao Dien, Ho Chi Minh City) - Nitori DC Block 18 (Thu Dau Mot, Binh Duong). RETURN TRIP',
          unit_id: unitCodeToId['TTC'],
          unit_price: 1400000,
          vat: 0.08,
          category_id: categoryCodeToId['DCMMD2'],
        },

        // Category: Extra handling charge for unloading (associated with Mega Mall D2)
        {
          code: 'EHCU_MMD2_22_02',
          name: '* if requested, working hour: 22:00 -02:00, excluding on Sunday & Holidays',
          unit_id: unitCodeToId['MS'],
          unit_price: 450000,
          vat: 0.08,
          category_id: categoryCodeToId['EHCU'],
        },

        // Category: Delivery charge from Nitori DC - Nitori Lê Thánh Tôn (Parkson Đồng Khởi, Dist 1).
        {
          code: 'DCLTTD1_TO_LTT_TRIP',
          name: 'Delivery charge from Nitori DC Block 18 - Nitori Lê Thánh Tôn (Parkson Đồng Khởi, Dist 1). DELIVERY TRIP',
          unit_id: unitCodeToId['TTC'],
          unit_price: 55180000,
          vat: 0.08,
          category_id: categoryCodeToId['DCLTTD1'],
        },
        {
          code: 'DCLTTD1_FROM_LTT_TRIP',
          name: 'Delivery charge from Nitori Lê Thánh Tôn (Parkson Đồng Khởi, Dist 1) - Nitori DC Block 18 (Thu Dau Mot, Binh Duong) RETURN TRIP',
          unit_id: unitCodeToId['TTC'],
          unit_price: 7290000,
          vat: 0.08,
          category_id: categoryCodeToId['DCLTTD1'],
        },

        // Category: Extra handling charge for unloading (associated with Le Thanh Ton)
        {
          code: 'EHCU_LTTD1_21_04_5HC',
          name: '* job performance by 05 head-counts (working hour: 21:00 -04:00, excluding on Sunday & Holidays)',
          unit_id: unitCodeToId['MS'],
          unit_price: 450000,
          vat: 0.08,
          category_id: categoryCodeToId['EHCU'],
        },

        // Category: Handling for overtime working (associated with Le Thanh Ton)
        {
          code: 'HOTW_LTTD1_AFTER_01',
          name: '* if occurred, charge after from 01:00 (Mon-Sat), excluding on Sunday & Holidays.',
          unit_id: unitCodeToId['MH'],
          unit_price: 150000,
          vat: 0.08,
          category_id: categoryCodeToId['HOTW'],
        },

        // Category: Delivery to Store in HCM area
        {
          code: 'DTSHCM_HANDOVER_M3',
          name: 'Including worker for hand-over cargo (Min charge 8 M3 per delivery)',
          unit_id: unitCodeToId['M3'],
          unit_price: 200000,
          vat: 0.08,
          category_id: categoryCodeToId['DTSHCM'],
        },
        {
          code: 'DTSHCM_TRANSPORT_VT',
          name: 'Transportation (FOT)',
          unit_id: unitCodeToId['VT'],
          unit_price: 1450000,
          vat: 0.08,
          category_id: categoryCodeToId['DTSHCM'],
        }, // Matched "Van truck"
        {
          code: 'DTSHCM_WORKER_FEE_TRU',
          name: 'Worker fee for delivery (02 head count)',
          unit_id: unitCodeToId['TRU'],
          unit_price: 600000,
          vat: 0.08,
          category_id: categoryCodeToId['DTSHCM'],
        }, // Matched "Truck"

        // Category: Delivery to customer and assemble Furniture in HCM area (...)
        // { code: 'DCAFHHCM_HANDOVER_TRU', name: 'Including worker for hand-over cargo (Min charge 8 M3 per delivery)', unit_id: unitCodeToId['TRU'], category_id: categoryCodeToId['DCAFHHCM'] }, // This seems to be a duplicate of DTSHCM_WORKER_FEE_TRU or similar. Removed for now.
        {
          code: 'DCAFHHCM_EXTRA_WORKER',
          name: 'Extra worker fee (for big item delivery, TBA case by case)',
          unit_id: unitCodeToId['TRU'],
          unit_price: 400000,
          vat: 0.08,
          category_id: categoryCodeToId['DCAFHHCM'],
        },
        {
          code: 'DCAFHHCM_ASSEMBLY',
          name: 'Assembly charge',
          unit_id: unitCodeToId['ORD'],
          unit_price: 500000,
          vat: 0.08,
          category_id: categoryCodeToId['DCAFHHCM'],
        },
        {
          code: 'DCAFHHCM_EXTRA_ORDER',
          name: 'Extra delivery order (on same truck)',
          unit_id: unitCodeToId['ORD'],
          unit_price: 200000,
          vat: 0.08,
          category_id: categoryCodeToId['DCAFHHCM'],
        },
        {
          code: 'DCAFHHCM_REDELIVERY',
          name: 'Attempt to re-delivery',
          unit_id: unitCodeToId['ATT'],
          unit_price: 700000,
          vat: 0.08,
          category_id: categoryCodeToId['DCAFHHCM'],
        },

        // Category: Value added service charge at VSIP 2 warehouse
        {
          code: 'VASVSIP2_LBL_SORT',
          name: 'Label sorting charge - VAS',
          unit_id: unitCodeToId['MH'],
          unit_price: 60000,
          vat: 0.08,
          category_id: categoryCodeToId['VASVSIP2'],
        },
      ]

      for (const workout of defaultWorkouts) {
        await promiser('exec', {
          dbId,
          sql: `INSERT INTO ${databaseConfig.tables.workout.name} (code, name, unit_id, unit_price, status, vat, category_id, calculation_type, unit_price_is_percentage, unit_price_unit, linked_workout_id, debit_code_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);`,
          bind: [
            workout.code,
            workout.name,
            workout.unit_id,
            workout.unit_price ?? 0, // Default unit_price
            'active', // Default status
            workout.vat ?? 0.08, // Default VAT from new data
            workout.category_id,
            workout.calculation_type ?? 'Manual', // Default calculation_type
            workout.unit_price_is_percentage ?? 0, // Default
            workout.unit_price_unit ?? 'VND', // Default
            workout.linked_workout_id ?? null, // Add linked_workout_id
            workout.debit_code_id ?? '', // Add debit_code_id
          ],
        })
      }
      console.log('Default workouts inserted.')
    } else {
      console.log('Workout table already has data. Skipping default insertion.')
    }
  }

  async function insertDefaultUnits() {
    if (!promiser || !dbId) return
    // Check if units table is empty
    const checkUnitsEmptyResult = await promiser('exec', {
      dbId,
      sql: `SELECT COUNT(*) FROM ${databaseConfig.tables.units.name};`,
      returnValue: 'resultRows',
    })

    if (checkUnitsEmptyResult.type === 'error') {
      console.error('Error checking if units table is empty:', checkUnitsEmptyResult.result.message)
      return
    }

    if (checkUnitsEmptyResult.result.resultRows?.[0]?.[0] === 0) {
      const defaultUnits = [
        // Order here is important for ID mapping in insertDefaultWorkouts
        { code: 'M2', name: 'Square Meter', status: 'active' },
        { code: 'SSCPM', name: 'Space Storage Charge Per Month', status: 'active' },
        { code: 'MONTH', name: 'Month', status: 'active' },
        { code: 'LRPT', name: 'Line Rack Per Term', status: 'active' },
        { code: 'M2PM', name: 'M2 Per Month', status: 'active' },
        { code: 'LPD', name: 'Line Per Day', status: 'active' },
        { code: '20FT', name: 'Container (20ft)', status: 'active' },
        { code: '40FT', name: 'Container (40ft)', status: 'active' },
        { code: 'LCLPAS', name: 'LCL Per Air Shipment', status: 'active' },
        { code: 'CTN', name: 'Carton', status: 'active' },
        { code: 'PCS', name: 'Pieces', status: 'active' },
        { code: 'LBL', name: 'Label', status: 'active' },
        { code: 'SET', name: 'Set', status: 'active' },
        { code: 'CBM', name: 'CBM', status: 'active' },
        { code: 'MH', name: 'Man hour', status: 'active' },
        { code: 'MD', name: 'Man day', status: 'active' },
        { code: 'MS', name: 'Man Shift', status: 'active' },
        { code: 'TTC', name: 'Total Transport Cost', status: 'active' },
        { code: 'M3', name: 'M3', status: 'active' },
        { code: 'VT', name: 'Van Truck', status: 'active' },
        { code: 'TRU', name: 'Truck', status: 'active' },
        { code: 'ORD', name: 'Order', status: 'active' },
        { code: 'ATT', name: 'Attempt', status: 'active' },
        { code: 'PLT', name: 'Pallet', status: 'active' },
        { code: 'BOX', name: 'Box', status: 'active' },
        { code: 'BAG', name: 'Bag', status: 'active' },
      ]
      for (const unit of defaultUnits) {
        await promiser('exec', {
          dbId,
          sql: `INSERT INTO ${databaseConfig.tables.units.name} (code, name, status) VALUES (?, ?, ?);`,
          bind: [unit.code, unit.name, unit.status],
        })
      }
      console.log('Default units inserted.')
    } else {
      console.log('Units table already has data. Skipping default insertion.')
    }
  }

  async function insertDefaultCategories() {
    if (!promiser || !dbId) return
    // Check if categories table is empty
    const checkCategoriesEmptyResult = await promiser('exec', {
      dbId,
      sql: `SELECT COUNT(*) FROM ${databaseConfig.tables.categories.name};`,
      returnValue: 'resultRows',
    })

    if (checkCategoriesEmptyResult.type === 'error') {
      console.error(
        'Error checking if categories table is empty:',
        checkCategoriesEmptyResult.result.message,
      )
      return
    }

    if (checkCategoriesEmptyResult.result.resultRows?.[0]?.[0] === 0) {
      // Updated list of default categories
      // Order here is important for ID mapping in insertDefaultWorkouts
      const defaultCategories = [
        { code: 'SF', name: 'Storage Fee', status: 'active' },
        { code: 'INB', name: 'Inbound', status: 'active' },
        { code: 'OBTS', name: 'Outbound to Store (Shipment for Store)', status: 'active' },
        { code: 'OBTC', name: 'Outbound to Customer (Shipment for Customer)', status: 'active' },
        { code: 'OBFEC', name: 'Outbound for EC (Courier service)', status: 'active' },
        { code: 'EHCU', name: 'Extra handling charge for unloading', status: 'active' },
        {
          code: 'SCEPSR',
          name: 'Storage charge - for extra pallet storage on rack',
          status: 'active',
        },
        { code: 'SCEPVR', name: 'Storage charge - for extra pallet VNA rack', status: 'active' },
        { code: 'STCVAS', name: 'Stocktaking Charge - VAS', status: 'active' },
        { code: 'SYSC', name: 'System charge', status: 'active' },
        { code: 'OTCWP', name: 'OT charge (apply for warehouse personel )', status: 'active' },
        {
          code: 'DCSORA',
          name: 'Delivery charge from Nitori DC Block 18 - Sora Gardens (Thu Dau Mot, Bình Dương)',
          status: 'active',
        },
        {
          code: 'DCVINCMD9',
          name: 'Delivery charge from Nitori DC Block 18 - Vincom Mega Mall (Dist 9, Thu Duc city)',
          status: 'active',
        },
        { code: 'HOTW', name: 'Handling for overtime working', status: 'active' },
        {
          code: 'DCMMD2',
          name: 'Delivery charge from Nitori DC - Mega Mall (Dist 2,Thao Dien).',
          status: 'active',
        },
        {
          code: 'DCLTTD1',
          name: 'Delivery charge from Nitori DC - Nitori Lê Thánh Tôn (Parkson Đồng Khởi, Dist 1).',
          status: 'active',
        },
        { code: 'DTSHCM', name: 'Delivery to Store in HCM area', status: 'active' },
        {
          code: 'DCAFHHCM',
          name: 'Delivery to customer and assemble Furniture in HCM area (operating hour 09:00 – 15:00 on Mon-Fri; Sat: from 09:00-12:00)',
          status: 'active',
        },
        {
          code: 'VASVSIP2',
          name: 'Value added service charge at VSIP 2 warehouse',
          status: 'active',
        },
      ]
      for (const category of defaultCategories) {
        await promiser('exec', {
          dbId,
          sql: `INSERT INTO ${databaseConfig.tables.categories.name} (code, name, status) VALUES (?, ?, ?);`,
          bind: [category.code, category.name, category.status],
        })
      }
      console.log('Default categories inserted.')
    } else {
      console.log('Categories table already has data. Skipping default insertion.')
    }
  }

  async function insertDefaultWorkoutCustomerCharges() {
    if (!promiser || !dbId) return

    // Check if table is empty
    const checkEmptyResult = await promiser('exec', {
      dbId,
      sql: `SELECT COUNT(*) FROM ${databaseConfig.tables.workout_customer_charges.name};`,
      returnValue: 'resultRows',
    })

    if (checkEmptyResult.type === 'error') {
      console.error(
        'Error checking if workout_customer_charges table is empty:',
        checkEmptyResult.result.message,
      )
      return
    }

    if (checkEmptyResult.result.resultRows?.[0]?.[0] === 0) {
      // Get first customer (Muji) and its first division
      const customerResult = await promiser('exec', {
        dbId,
        sql: 'SELECT id FROM customers WHERE code = ? LIMIT 1;',
        bind: ['NIT'],
        returnValue: 'resultRows',
      })

      if (customerResult.type === 'error' || !customerResult.result.resultRows?.length) return

      const customerId = customerResult.result.resultRows[0][0]

      const divisionResult = await promiser('exec', {
        dbId,
        sql: 'SELECT id FROM customer_divisions WHERE customer_id = ? LIMIT 1;',
        bind: [customerId],
        returnValue: 'resultRows',
      })

      if (divisionResult.type === 'error') return
      const divisionId = divisionResult.result.resultRows?.[0]?.[0]

      // Get all workouts with their categories
      const workoutsResult = await promiser('exec', {
        dbId,
        sql: 'SELECT id, category_id FROM workout WHERE category_id = ?;',
        bind: ['1'],
        returnValue: 'resultRows',
      })

      if (workoutsResult.type === 'error' || !workoutsResult.result.resultRows?.length) return

      const currentYear = new Date().getFullYear()
      const currentMonth = 1

      // Create sample charges for each workout
      for (const [workoutId, categoryId] of workoutsResult.result.resultRows) {
        const chargeId = `CHG-${Date.now()}-${workoutId}`
        const monthValues = Array(12).fill(null)

        // Set a sample value for the current month and previous month
        // Fill values from currentMonth to month 12
        for (let i = currentMonth - 1; i < 12; i++) {
          monthValues[i] = Math.floor(Math.random() * 10) + 1 // Random value between 1-10
        }

        await promiser('exec', {
          dbId,
          sql: `
            INSERT INTO ${databaseConfig.tables.workout_customer_charges.name}
            (id, customer_id, division_id, category_id, workout_id, year,
             month_1, month_2, month_3, month_4, month_5, month_6,
             month_7, month_8, month_9, month_10, month_11, month_12,
             created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
          `,
          bind: [
            chargeId,
            customerId,
            divisionId,
            categoryId,
            workoutId,
            currentYear,
            ...monthValues,
            new Date().toISOString(),
            new Date().toISOString(),
          ],
        })
      }

      console.log('Default workout customer charges inserted.')
    }
  }

  async function initialize() {
    if (isInitialized.value) return true

    isLoading.value = true
    error.value = null

    try {
      // Initialize the SQLite worker
      promiser = await new Promise((resolve) => {
        const _promiser = sqlite3Worker1Promiser({
          onready: () => resolve(_promiser),
        })
      })

      if (!promiser) throw new Error('Failed to initialize promiser')

      // Get configuration and open database
      await promiser('config-get', {})
      const openResponse = await promiser('open', {
        filename: databaseConfig.filename,
      })

      if (openResponse.type === 'error') {
        throw new Error(openResponse.result.message)
      }

      dbId = openResponse.result.dbId as string

      // Drop all tables before initialize initial tables
      // dropAllTables()

      // Create initial tables
      for (const table of Object.values(databaseConfig.tables)) {
        const execResult = await promiser('exec', {
          dbId,
          sql: table.schema,
        })
        if (execResult.type === 'error') {
          console.error(`Error creating table ${table.name}:`, execResult.result.message)
        }
      }

      // Initialize default data
      // await initializeDefaultData()

      isInitialized.value = true
      return true
    } catch (err) {
      console.log(err)
      error.value = err instanceof Error ? err : new Error('Unknown error')
      throw error.value
    } finally {
      isLoading.value = false
    }
  }

  async function insertDefaultWorkoutCustomerCharges() {
    if (!promiser || !dbId) return

    const checkEmptyResult = await promiser('exec', {
      dbId,
      sql: `SELECT COUNT(*) FROM ${databaseConfig.tables.workout_customer_charge.name};`,
      returnValue: 'resultRows',
    })

    if (checkEmptyResult.type === 'error') {
      console.error(
        'Error checking if workout_customer_charge table is empty:',
        checkEmptyResult.result.message,
      )
      return
    }

    if (checkEmptyResult.result.resultRows?.[0]?.[0] === 0) {
      console.log('Workout customer charge table is empty. No default data to insert.')
    } else {
      console.log('Workout customer charge table already has data. Skipping default insertion.')
    }
  }

  async function initializeDefaultData() {
    if (!promiser || !dbId) {
      throw new Error('Database not initialized')
    }

    try {
      // Initialize data in the correct order based on dependencies
      await insertDefaultCustomers()
      await insertDefaultCustomerDivisions() // Depends on customers
      await insertDefaultUnits()
      await insertDefaultCategories()
      await insertDefaultWorkouts() // Depends on units, categories, and debit codes
      await insertDefaultWorkoutCustomerCharges()
      await insertDefaultWorkoutCustomerCharges() // Check workout customer charge table

      console.log('Default data initialized successfully')
    } catch (error) {
      console.error('Error initializing default data:', error)
      throw error
    }
  }

  async function dropAllTables() {
    if (!dbId || !promiser) {
      await initialize()
    }

    if (!promiser || !dbId) {
      throw new Error('Failed to initialize database')
    }

    isLoading.value = true
    error.value = null

    try {
      // Disable foreign key checks temporarily
      await promiser('exec', {
        dbId,
        sql: 'PRAGMA foreign_keys = OFF;',
      })

      // Get all tables in reverse order to handle dependencies
      const tables = Object.values(databaseConfig.tables).reverse()

      // Drop each table
      for (const table of tables) {
        const dropResult = await promiser('exec', {
          dbId,
          sql: `DROP TABLE IF EXISTS ${table.name};`,
        })

        if (dropResult.type === 'error') {
          console.error(`Error dropping table ${table.name}:`, dropResult.result.message)
        } else {
          console.log(`Successfully dropped table ${table.name}`)
        }
      }

      // Re-enable foreign key checks
      await promiser('exec', {
        dbId,
        sql: 'PRAGMA foreign_keys = ON;',
      })

      console.log('All tables dropped successfully')
      isInitialized.value = false
      return true
    } catch (err) {
      console.error('Error dropping tables:', err)
      error.value = err instanceof Error ? err : new Error('Unknown error')
      throw error.value
    } finally {
      isLoading.value = false
    }
  }

  async function executeQuery(sql: string, params: unknown[] = []) {
    if (!dbId || !promiser) {
      await initialize()
    }

    isLoading.value = true
    error.value = null

    try {
      const result = await promiser!('exec', {
        dbId: dbId as DbId,
        sql,
        bind: params,
        returnValue: 'resultRows',
      })

      if (result.type === 'error') {
        throw new Error(result.result.message)
      }

      return result
    } catch (err) {
      console.log(err)
      // Define a more specific type for the expected error structure from the promiser
      type PromiserError = { result?: { message?: string } }

      if (err instanceof Error) {
        error.value = err
      } else if (
        err &&
        typeof err === 'object' &&
        'result' in err &&
        typeof (err as PromiserError).result?.message === 'string'
      ) {
        error.value = new Error((err as { result: { message: string } }).result.message)
      } else {
        error.value = new Error('An unknown error occurred during query execution.')
      }
      throw error.value
    } finally {
      isLoading.value = false
    }
  }

  const tables = Object.values(databaseConfig.tables).reduce(
    (acc, table) => {
      acc[table.name] = table.name
      return acc
    },
    {} as Record<
      (typeof databaseConfig.tables)[keyof typeof databaseConfig.tables]['name'],
      (typeof databaseConfig.tables)[keyof typeof databaseConfig.tables]['name']
    >,
  )

  return {
    isLoading,
    error,
    isInitialized,
    tables,
    executeQuery,
    initialize,
    dropAllTables,
  }
}
