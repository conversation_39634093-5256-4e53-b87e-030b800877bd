export interface COD {
  id: number
  code: string
  name?: string
  address?: string
  codeType: 'Store' | 'Customer'
  createdAt: string
  updatedAt: string
}

export interface CODFormData {
  code: string
  name?: string
  address?: string
  codeType: 'Store' | 'Customer'
}

export interface CODFilters {
  search?: string
  codeType?: 'Store' | 'Customer'
}

export const COD_TYPES = [
  { value: 'Store', label: 'Store' },
  { value: 'Customer', label: 'Customer' },
] as const
