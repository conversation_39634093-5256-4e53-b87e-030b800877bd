<script setup lang="ts">
import { useCounterStore } from '@IVC/stores/counter'
import { storeToRefs } from 'pinia'

const counterStore = useCounterStore()

const { count } = storeToRefs(counterStore)
const { increment } = counterStore

const handleIncrement = () => {
  increment()
}
</script>

<template>
  <div class="greetings">xxxx</div>
  <a-button type="primary" @click="handleIncrement">Hi</a-button>
</template>
