@import './base.css';

.ant-table-cell {
  padding: 8px !important;
}

.ant-page-header {
  padding-top: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.header-container-invoice {
  padding: 5px 15px 15px 15px;
  background-color: #ffffff;
}

.header-container-invoice .ant-page-header {
  padding-bottom: 5px;
}

.header-container-master {
  padding: 5px 15px 15px 15px;
  background-color: #ffffff;
}

.header-container-master .ant-page-header {
  padding-bottom: 5px;
}

.header-container-invoice .ant-page-header .ant-page-header-heading-title,
.header-container-master .ant-page-header .ant-page-header-heading-title {
  margin-right: 8px;
}

.header-container-invoice .ant-page-header .ant-page-header-heading-extra {
  display: flex;
}

.header-container-invoice .ant-page-header .ant-page-header-heading-sub-title,
.header-container-master .ant-page-header .ant-page-header-heading-sub-title {
  position: relative !important;
  bottom: -3px !important;
  font-style: italic !important;
  font-size: 12px !important;
}

.page-header {
  margin-bottom: 0;
}

.page-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.page-header p {
  color: #8c8c8c;
  margin: 0;
  font-size: 14px;
}

.search-card {
  box-shadow: 0 1px 2px rgb(22 8 90 / 40%) !important;
}

.ant-card .ant-card-head {
  min-height: 35px;
  margin-bottom: 0px;
  padding: 0 15px;
  color: rgb(22 8 90);
  background: #16085a14;
}
.search-section .ant-card .ant-card-body {
  padding: 15px;
}

.search-section .ant-card-body .ant-form-item:last-child {
  margin-bottom: 10px;
}

.drop-zone {
  background-color: #7cb22514 !important;
  padding: 10px !important;
}

.search-section {
  margin-bottom: 20px;
  margin-top: 10px;
}

.ant-space-item {
  font-size: 16px;
}
/* button */
.ant-button {
  min-width: 96px;
}

.ant-btn-default {
  border-color: #50458394;
  min-width: 96px;
}

/* button search */
.btn-search-condition {
  background-color: #9291b2;
  color: white;
  border: 1px solid #9291b2;
}

.btn-search-condition:hover {
  background-color: #59587e !important;
  color: white !important;
  border: 1px solid #9291b2 !important;
  border-color: #9291b2 !important;
}

/* drop */
.drop-message {
  color: #353535 !important;
}

.drop-message .upload-icon {
  margin-bottom: 0 !important;
  color: #777 !important;
}

/* table */
.ant-table-wrapper .ant-table-thead tr th {
  background: #ececf2 !important;
}

.ant-table-wrapper .ant-table-container {
    /* border-start-start-radius: 8px;
    border-start-end-radius: 8px; */
    border: 1px solid #f1f1f1;
    /* border-end-end-radius: 8px;
    border-end-start-radius: 8px; */
    border-radius: 8px;
}

.ant-table-tbody tr:last-child td {
  border-bottom: none !important;
}

.report-container .ant-card .ant-card-body {
  padding: 0px 15px 15px 15px !important;
}

.report-container .report-content-monthly .ant-card .ant-card-body {
  padding-top: 15px !important;
  min-height: 367px;

}
