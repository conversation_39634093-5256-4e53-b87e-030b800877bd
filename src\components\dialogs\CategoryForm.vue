<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue'
import { Form, Input, Modal, Select, message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import type { Category } from '@IVC/types/MasterDataTypes/Category'

const props = defineProps<{
  visible: boolean
  categoryData?: Partial<Category> | null // Allow id to be undefined for new categories
}>()

const emit = defineEmits(['close', 'save'])

const formRef = ref<FormInstance>()
const formState = ref<Partial<Category>>({})
const isLoading = ref(false) // For save button

const modalTitle = computed(() => (props.categoryData?.id ? 'Edit Category' : 'Add New Category'))

onMounted(() => {
  // Data will be fetched when modal becomes visible
})

watch(
  () => props.visible,
  (isVisible) => {
    if (isVisible) {
      if (props.categoryData) {
        formState.value = {
          ...props.categoryData,
          status: props.categoryData.status || 'active', // Default to 'active' if status is undefined
        }
      } else {
        formState.value = {
          code: '',
          name: '',
          status: 'active',
        }
      }
    }
  },
  { immediate: true },
)

const handleOk = async () => {
  isLoading.value = true
  try {
    await formRef.value?.validate()
    emit('save', { ...formState.value })
  } catch (errorInfo) {
    console.log('Validation Failed:', errorInfo)
    message.error('Please correct the form errors.')
  } finally {
    // isLoading is handled by the parent component or reset in resetFormAndLoading
  }
}

const handleCancel = () => {
  emit('close')
}

const resetFormAndLoading = () => {
  formRef.value?.resetFields()
  isLoading.value = false
}

defineExpose({ resetFormAndLoading })
</script>

<template>
  <Modal
    :title="modalTitle"
    :visible="props.visible"
    :confirm-loading="isLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :destroy-on-close="true"
  >
    <Form ref="formRef" :model="formState" layout="vertical" name="category_form">
      <Form.Item
        label="Category Code"
        name="code"
        :rules="[{ required: true, message: 'Please input category code!' }]"
      >
        <Input v-model:value="formState.code" />
      </Form.Item>
      <Form.Item
        label="Category Name"
        name="name"
        :rules="[{ required: true, message: 'Please input category name!' }]"
      >
        <Input v-model:value="formState.name" />
      </Form.Item>

      <Form.Item
        label="Status"
        name="status"
        :rules="[{ required: true, message: 'Please select status!' }]"
      >
        <Select v-model:value="formState.status">
          <Select.Option value="active">Active</Select.Option>
          <Select.Option value="inactive">Inactive</Select.Option>
        </Select>
      </Form.Item>
    </Form>
  </Modal>
</template>
